# 邮箱验证码功能配置指南

## 📧 功能概述

邮箱验证码功能已成功实现，包含以下特性：

- ✅ 6位数字验证码，5分钟有效期
- ✅ 支持多个个人邮箱随机发送
- ✅ 防止频繁发送（1分钟间隔限制）
- ✅ 绑定邮箱后赠送1天VIP会员
- ✅ 支持邀请奖励机制
- ✅ 精美的HTML邮件模板
- ✅ 自动清理过期验证码

## 🔧 配置步骤

### 1. 配置个人邮箱

在 `backend/.env` 文件中配置 `PERSONAL_EMAILS` 环境变量：

```bash
# 邮件发送配置
# 个人邮箱配置，支持多个邮箱，用逗号分隔
# 格式：邮箱:密码:SMTP服务器:端口
PERSONAL_EMAILS=<EMAIL>:your-app-password1:smtp.gmail.com:587,<EMAIL>:your-app-password2:smtp.qq.com:587

# 邮件发送配置
EMAIL_FROM_NAME=智能视频平台
```

### 2. 获取邮箱应用密码

#### Gmail 配置
1. 登录 Gmail 账户
2. 进入 Google 账户设置 → 安全性
3. 开启两步验证
4. 生成应用专用密码
5. 使用应用密码替换普通密码

#### QQ邮箱配置
1. 登录 QQ 邮箱
2. 设置 → 账户
3. 开启 SMTP 服务
4. 获取授权码
5. 使用授权码作为密码

#### 163邮箱配置
1. 登录 163 邮箱
2. 设置 → POP3/SMTP/IMAP
3. 开启 SMTP 服务
4. 设置客户端授权密码
5. 使用授权密码

### 3. 常用SMTP配置

| 邮箱服务商 | SMTP服务器 | 端口 | 加密方式 |
|-----------|-----------|------|---------|
| Gmail | smtp.gmail.com | 587 | TLS |
| QQ邮箱 | smtp.qq.com | 587 | TLS |
| 163邮箱 | smtp.163.com | 587 | TLS |
| 126邮箱 | smtp.126.com | 587 | TLS |
| Outlook | smtp-mail.outlook.com | 587 | TLS |

### 4. 配置示例

```bash
# 单个邮箱配置
PERSONAL_EMAILS=<EMAIL>:abcd1234efgh:smtp.gmail.com:587

# 多个邮箱配置（推荐）
PERSONAL_EMAILS=<EMAIL>:password1:smtp.gmail.com:587,<EMAIL>:password2:smtp.qq.com:587,<EMAIL>:password3:smtp.163.com:587
```

## 🚀 API接口

### 发送验证码

```bash
POST /api/user/send-email-verification
Content-Type: application/json

{
  "email": "<EMAIL>"
}
```

**响应示例：**
```json
{
  "success": true,
  "message": "验证码已发送，请查收邮件",
  "data": {
    "email": "<EMAIL>"
  }
}
```

### 验证邮箱验证码

```bash
POST /api/user/verify-email-code
Authorization: Bearer <token>
Content-Type: application/json

{
  "email": "<EMAIL>",
  "code": "123456"
}
```

**响应示例：**
```json
{
  "success": true,
  "message": "邮箱绑定成功！已为您开通1天VIP会员",
  "data": {
    "user": {
      "id": 1,
      "email": "<EMAIL>",
      "emailVerified": true,
      "isVip": true,
      "vipExpireAt": "2024-01-02T00:00:00.000Z"
    },
    "vipGift": {
      "granted": true,
      "duration": "1天",
      "expireAt": "2024-01-02T00:00:00.000Z"
    }
  }
}
```

## 🎁 VIP奖励机制

- **绑定邮箱奖励**：1天VIP会员
- **邀请奖励**：邀请者获得3天VIP（通过InvitationService处理）
- **佣金系统**：支持最多5级邀请关系，50%佣金

## 🔒 安全特性

1. **频率限制**：同一邮箱1分钟内只能发送一次验证码
2. **验证码过期**：验证码5分钟后自动过期
3. **一次性使用**：验证码验证成功后立即失效
4. **邮箱唯一性**：每个邮箱只能绑定一个账户
5. **自动清理**：定期清理过期验证码记录

## 🛠️ 故障排除

### 常见问题

1. **邮件发送失败**
   - 检查邮箱密码是否为应用专用密码
   - 确认SMTP服务器和端口配置正确
   - 检查网络连接和防火墙设置

2. **验证码收不到**
   - 检查垃圾邮件文件夹
   - 确认邮箱地址输入正确
   - 查看后端日志确认发送状态

3. **验证码验证失败**
   - 确认验证码未过期（5分钟有效期）
   - 检查验证码输入是否正确
   - 确认验证码未被使用过

### 日志监控

后端会输出详细的邮件发送日志：

```
📧 正在发送验证码邮件: <EMAIL> -> <EMAIL>
✅ 验证码邮件发送成功: <EMAIL>, 验证码: 123456
🔍 验证邮箱验证码: userId=1, email=<EMAIL>, code=123456
✅ 邮箱绑定成功: userId=1, email=<EMAIL>, VIP奖励已发放
```

## 📱 前端集成

前端已集成相关API调用：

- `apiService.sendEmailVerification(data)` - 发送验证码
- `apiService.verifyEmailCode(data)` - 验证验证码

LinkEmailPage 组件已更新使用新的验证码API。

## 🔄 定时任务

建议设置定时任务清理过期验证码：

```javascript
// 每小时清理一次过期验证码
setInterval(async () => {
  await EmailService.cleanupExpiredCodes();
}, 60 * 60 * 1000);
```

## 📊 数据库表结构

```sql
CREATE TABLE `email_verification_codes` (
  `id` int NOT NULL AUTO_INCREMENT,
  `email` varchar(255) NOT NULL COMMENT '邮箱地址',
  `code` varchar(6) NOT NULL COMMENT '6位数字验证码',
  `expiresAt` datetime NOT NULL COMMENT '过期时间',
  `isUsed` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已使用',
  `createdAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_email_verification_codes_email` (`email`),
  KEY `idx_email_verification_codes_email_code` (`email`, `code`),
  KEY `idx_email_verification_codes_expiresAt` (`expiresAt`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='邮箱验证码表';
```

配置完成后重启后端服务即可使用邮箱验证码功能！
