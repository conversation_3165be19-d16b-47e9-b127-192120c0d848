# 第三方充值防重复订单机制

## 概述

为了防止用户重复创建订单和恶意攻击，我们实现了一套完善的防重复订单机制。

## 核心机制

### 1. 时间窗口限制
- **时间窗口**: 30分钟
- **规则**: 同一用户对同一SKU在30分钟内只能有一个pending状态的订单

### 2. 订单状态检查
在创建新订单前，系统会：
1. 查找用户在30分钟内创建的pending状态订单
2. 通过第三方API检查订单的真实状态
3. 根据检查结果决定是重用还是创建新订单

### 3. 智能重用机制
如果发现pending订单：
- **第三方状态为NEW**: 重用现有订单，重新发起支付
- **第三方状态为DONE**: 更新本地状态为已支付，创建新订单
- **第三方状态为EXPIRED/CANCELLED**: 更新本地状态为过期，创建新订单

## 实现流程

### 创建订单流程

```mermaid
flowchart TD
    A[用户请求创建订单] --> B[验证用户和SKU]
    B --> C[查找30分钟内的pending订单]
    C --> D{是否存在pending订单?}
    
    D -->|否| E[创建新的第三方订单]
    E --> F[发起支付获取支付链接]
    F --> G[保存到数据库]
    G --> H[返回新订单信息]
    
    D -->|是| I[检查第三方订单状态]
    I --> J{第三方状态是什么?}
    
    J -->|NEW| K[重用现有订单]
    K --> L[重新发起支付]
    L --> M[更新支付信息]
    M --> N[返回重用订单信息]
    
    J -->|DONE| O[更新本地状态为已支付]
    O --> P[触发充值处理]
    P --> E
    
    J -->|EXPIRED/CANCELLED| Q[更新本地状态为过期]
    Q --> E
```

### 防攻击特性

1. **频率限制**: 30分钟内同一SKU只能创建一个订单
2. **状态同步**: 实时检查第三方订单状态，防止状态不一致
3. **安全重用**: 只有确认第三方状态为NEW才重用订单
4. **自动清理**: 过期订单自动标记，不影响新订单创建

## 代码实现

### 核心方法

#### 1. findPendingOrder
```typescript
private async findPendingOrder(userId: number, skuId: string): Promise<ThirdPartyOrder | null>
```
- 查找半小时内的pending订单
- 通过第三方API验证订单状态
- 返回可重用的订单或null

#### 2. updateOrderStatus
```typescript
private async updateOrderStatus(thirdPartyOrderId: string, orderInfo: any): Promise<void>
```
- 根据第三方状态更新本地订单状态
- 自动处理已支付订单的充值逻辑

#### 3. createRechargeOrder (优化后)
```typescript
async createRechargeOrder(params: CreateRechargeOrderParams & { payMethod: PayMethod }): Promise<RechargeOrderResult>
```
- 集成防重复检查逻辑
- 支持订单重用和新建
- 返回结果包含 `isReused` 标记

## 返回结果

### RechargeOrderResult 接口
```typescript
interface RechargeOrderResult {
  orderId: number;           // 我们系统的订单ID
  thirdPartyOrderId: string; // 第三方订单ID
  amount: number;            // 金额
  coins: number;             // 金币数量
  payUrl: string;            // 支付链接
  status: ThirdPartyOrderStatus; // 订单状态
  isReused?: boolean;        // 是否重用了现有订单
}
```

### 重用订单示例
```json
{
  "orderId": 123,
  "thirdPartyOrderId": "683f148fffbfac82956773d2",
  "amount": 10.00,
  "coins": 100,
  "payUrl": "https://pay.example.com/...",
  "status": "pending",
  "isReused": true
}
```

### 新订单示例
```json
{
  "orderId": 124,
  "thirdPartyOrderId": "683f151bffbfac82956773df",
  "amount": 10.00,
  "coins": 100,
  "payUrl": "https://pay.example.com/...",
  "status": "pending",
  "isReused": false
}
```

## 日志记录

系统会记录详细的日志信息：

```
检查现有订单状态: 683f148fffbfac82956773d2
找到可重用的pending订单: 683f148fffbfac82956773d2
重用现有订单: 683f148fffbfac82956773d2
```

或

```
订单状态已变化，更新本地状态: DONE
创建新订单: 683f151bffbfac82956773df
```

## 优势

1. **防止重复扣费**: 避免用户误操作创建多个订单
2. **提升用户体验**: 快速重用现有订单，无需重新创建
3. **防恶意攻击**: 限制订单创建频率，防止系统被攻击
4. **状态一致性**: 实时同步第三方订单状态
5. **资源优化**: 减少不必要的第三方API调用

## 注意事项

1. **网络异常处理**: 如果第三方API检查失败，为安全起见不重用订单
2. **时间窗口**: 30分钟的时间窗口可以根据业务需求调整
3. **并发处理**: 使用数据库事务确保并发安全
4. **监控告警**: 建议监控重用订单的比例，异常时及时告警

## 配置参数

- **时间窗口**: 30分钟 (可在代码中调整)
- **订单过期时间**: 30分钟 (与时间窗口保持一致)
- **重试机制**: 第三方API调用失败时的处理策略
