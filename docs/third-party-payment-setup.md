# 第三方支付配置说明

## 重要提醒

根据第三方支付平台的要求，`redirectUrl` 和 `callbackUrl` 必须使用真实的域名，不能使用 localhost 或 IP 地址。

## 参数说明

### redirectUrl
- **用途**: 支付成功后的重定向链接
- **要求**: 应该指向订单页面，需要包含完整域名
- **示例**: `https://yourdomain.com/orders`
- **说明**: 用户支付完成后会跳转到这个页面查看订单状态

### callbackUrl  
- **用途**: 订单支付成功或退款时触发的后端回调通知
- **要求**: 
  - 必须使用域名 HTTPS，不能通过 IP 访问
  - 需要在5秒内处理完毕并返回200/201状态码
  - 回调失败时会进行重试(最多5次)
- **示例**: `https://yourdomain.com/api/third-party-recharge/callback`
- **说明**: 第三方支付平台会将订单数据以POST请求(JSON格式)发送给此链接

## 配置步骤

### 1. 准备域名
确保你有一个可访问的域名，并且支持 HTTPS。

### 2. 配置环境变量
在 `backend/.env` 文件中设置：

```bash
# 支付成功后的重定向链接，指向订单页面
THIRD_PARTY_REDIRECT_URL=https://yourdomain.com/orders

# 支付回调通知地址，必须使用HTTPS域名
THIRD_PARTY_CALLBACK_URL=https://yourdomain.com/api/third-party-recharge/callback
```

### 3. 部署到服务器
确保你的应用部署到支持 HTTPS 的服务器上，域名可以正常访问。

### 4. 测试回调接口
确保回调接口 `/api/third-party-recharge/callback` 可以：
- 正常接收 POST 请求
- 在5秒内处理完成
- 返回 200 或 201 状态码

## 开发环境注意事项

在开发环境中，如果没有可用的域名，可以：

1. **使用内网穿透工具**（推荐）
   - 使用 ngrok、frp 等工具将本地服务暴露到公网
   - 获得临时的 HTTPS 域名用于测试

2. **使用测试域名**
   - 申请免费的测试域名
   - 配置 DNS 指向你的服务器

3. **本地测试限制**
   - 本地开发时第三方支付回调无法正常工作
   - 可以通过手动调用回调接口进行测试

## 生产环境部署

### 1. 域名配置
```bash
# 生产环境示例
THIRD_PARTY_REDIRECT_URL=https://your-production-domain.com/orders
THIRD_PARTY_CALLBACK_URL=https://your-production-domain.com/api/third-party-recharge/callback
```

### 2. SSL 证书
确保域名配置了有效的 SSL 证书，支持 HTTPS 访问。

### 3. 防火墙配置
确保服务器防火墙允许第三方支付平台的回调请求。

## 故障排除

### 回调失败
如果回调失败，检查：
1. 域名是否可以正常访问
2. HTTPS 证书是否有效
3. 回调接口是否在5秒内响应
4. 返回状态码是否为 200/201

### 重定向失败
如果重定向失败，检查：
1. 前端路由 `/orders` 是否正确配置
2. 域名是否可以正常访问
3. 用户是否有权限访问订单页面

## 支持的支付方式

目前支持：
- 支付宝 (alipay)
- 微信支付 (wxpay)

## API 流程

1. 前端调用 `/api/third-party-recharge/create-order` 创建订单
2. 后端立即调用第三方支付API获取支付链接
3. 前端在新窗口打开支付链接
4. 用户完成支付后跳转到 `redirectUrl`
5. 第三方平台向 `callbackUrl` 发送支付结果通知
6. 后端处理回调，更新订单状态和用户余额
