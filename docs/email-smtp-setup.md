# 📧 邮箱SMTP配置指南

## 🚨 Gmail连接问题解决方案

如果遇到Gmail SMTP连接超时问题（ETIMEDOUT），建议使用以下解决方案：

## 🇨🇳 推荐：使用国内邮箱服务商

### 1. 163邮箱配置（推荐）

**步骤：**
1. 登录163邮箱：https://mail.163.com
2. 进入设置 → POP3/SMTP/IMAP
3. 开启SMTP服务
4. 设置客户端授权密码（不是登录密码）
5. 记录授权密码

**配置示例：**
```bash
PERSONAL_EMAILS="<EMAIL>:your-auth-code:smtp.163.com:587"
```

### 2. QQ邮箱配置

**步骤：**
1. 登录QQ邮箱：https://mail.qq.com
2. 设置 → 账户
3. 开启SMTP服务
4. 获取授权码（16位）
5. 记录授权码

**配置示例：**
```bash
PERSONAL_EMAILS="<EMAIL>:your-auth-code:smtp.qq.com:587"
```

### 3. 126邮箱配置

**步骤：**
1. 登录126邮箱：https://mail.126.com
2. 设置 → POP3/SMTP/IMAP
3. 开启SMTP服务
4. 设置客户端授权密码
5. 记录授权密码

**配置示例：**
```bash
PERSONAL_EMAILS="<EMAIL>:your-auth-code:smtp.126.com:587"
```

## 🌍 Gmail配置（如果网络允许）

### 获取Gmail应用专用密码

1. 登录Google账户：https://myaccount.google.com
2. 安全性 → 两步验证（必须先开启）
3. 应用专用密码 → 生成新密码
4. 选择"邮件"和设备类型
5. 复制生成的16位密码

**配置示例：**
```bash
PERSONAL_EMAILS="<EMAIL>:your-app-password:smtp.gmail.com:587"
```

## 🔧 多邮箱配置

可以配置多个邮箱提高发送成功率：

```bash
PERSONAL_EMAILS="<EMAIL>:auth1:smtp.163.com:587,<EMAIL>:auth2:smtp.qq.com:587,<EMAIL>:auth3:smtp.126.com:587"
```

## 📋 常用SMTP配置表

| 邮箱服务商 | SMTP服务器 | 端口 | 安全类型 | 获取授权码位置 |
|-----------|-----------|------|---------|---------------|
| 163邮箱 | smtp.163.com | 587 | TLS | 设置→POP3/SMTP/IMAP |
| QQ邮箱 | smtp.qq.com | 587 | TLS | 设置→账户→SMTP |
| 126邮箱 | smtp.126.com | 587 | TLS | 设置→POP3/SMTP/IMAP |
| Gmail | smtp.gmail.com | 587 | TLS | Google账户→安全性→应用专用密码 |
| Outlook | smtp-mail.outlook.com | 587 | TLS | 账户设置→安全性 |

## 🧪 测试邮箱配置

配置完成后，可以通过以下方式测试：

### 1. API测试
```bash
curl -X POST http://localhost:3002/api/user/send-email-verification \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>"}'
```

### 2. 查看后端日志
观察控制台输出，确认：
- ✅ 邮箱配置已加载
- ✅ SMTP连接成功
- ✅ 邮件发送成功

## 🚨 常见问题排查

### 1. 连接超时（ETIMEDOUT）
- **原因**：网络无法访问SMTP服务器
- **解决**：更换为国内邮箱服务商

### 2. 认证失败（Authentication failed）
- **原因**：密码错误或未开启SMTP
- **解决**：检查授权码，确认SMTP服务已开启

### 3. 发送频率限制
- **原因**：短时间内发送过多邮件
- **解决**：等待一段时间或使用多个邮箱轮换

### 4. 邮件进入垃圾箱
- **原因**：邮件内容或发送方被识别为垃圾邮件
- **解决**：优化邮件内容，使用知名邮箱服务商

## 🔒 安全建议

1. **使用授权码**：不要使用登录密码
2. **定期更换**：定期更换授权码
3. **权限最小化**：只开启必要的邮箱服务
4. **监控日志**：定期检查邮件发送日志

## 📝 配置示例

### 生产环境推荐配置
```bash
# 使用多个163邮箱提高稳定性
PERSONAL_EMAILS="<EMAIL>:auth1:smtp.163.com:587,<EMAIL>:auth2:smtp.163.com:587"
EMAIL_FROM_NAME=智能视频平台
```

### 开发环境配置
```bash
# 使用单个邮箱即可
PERSONAL_EMAILS="<EMAIL>:auth-code:smtp.163.com:587"
EMAIL_FROM_NAME=智能视频平台-开发环境
```

配置完成后重启后端服务即可生效！
