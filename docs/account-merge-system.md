# 📧 账号合并系统设计文档

## 🎯 功能概述

账号合并系统解决了用户忘记账号后通过邮箱找回的问题。当用户尝试绑定一个已被其他账号使用的邮箱时，系统会自动合并账号，确保用户不会丢失之前的数据和权限。

## 🔄 工作流程

### 场景1：邮箱验证码绑定时自动合并

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant B as 后端
    participant DB as 数据库

    U->>F: 输入邮箱验证码
    F->>B: POST /api/user/verify-email-code
    B->>DB: 检查邮箱是否已被使用
    
    alt 邮箱已被其他用户使用
        B->>B: 触发账号合并逻辑
        B->>DB: 合并用户数据
        B->>DB: 禁用旧账号
        B->>F: 返回合并结果
        F->>U: 显示合并成功信息
    else 邮箱未被使用
        B->>DB: 正常绑定邮箱
        B->>F: 返回绑定成功
        F->>U: 显示绑定成功
    end
```

### 场景2：主动账号合并

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant B as 后端
    participant DB as 数据库

    U->>F: 访问账号合并页面
    U->>F: 输入要合并的邮箱
    F->>B: POST /api/user/merge-account
    B->>DB: 查找目标账号
    B->>B: 执行账号合并
    B->>DB: 更新数据
    B->>F: 返回合并结果
    F->>U: 显示合并成功页面
```

## 🔧 技术实现

### 后端实现

#### 1. 邮箱绑定时自动合并

```typescript
// UserService.bindEmailWithVipReward
static async bindEmailWithVipReward(userId: number, email: string) {
  // 检查邮箱是否已被其他用户使用
  const existingUser = await User.findOne({
    where: {
      email,
      emailVerified: true,
      id: { [Op.ne]: userId }
    }
  });

  // 如果邮箱已被使用，执行账号合并
  if (existingUser) {
    return await this.mergeAccountByEmail(userId, email);
  }

  // 否则正常绑定邮箱
  // ...
}
```

#### 2. 账号合并核心逻辑

```typescript
// UserService.mergeAccountByEmail
static async mergeAccountByEmail(currentUserId: number, email: string) {
  const transaction = await sequelize.transaction();
  
  try {
    // 1. 获取用户信息
    const currentUser = await User.findByPk(currentUserId, { transaction });
    const targetUser = await User.findOne({ /* ... */ });

    // 2. 合并VIP权限（取较长的到期时间）
    let finalVipExpireAt = /* 计算逻辑 */;

    // 3. 合并金币（取较大值）
    const finalCoins = Math.max(currentUser.coins, targetUser.coins);

    // 4. 转移用户数据
    await UserVideo.update(/* 转移收藏、购买、历史 */);
    await Order.update(/* 转移订单 */);
    await User.update(/* 转移邀请关系 */);

    // 5. 更新当前用户
    await currentUser.update({ /* 合并后的信息 */ });

    // 6. 禁用目标用户
    await targetUser.update({
      isMerged: true,
      mergedToUserId: currentUserId,
      isActive: false
    });

    await transaction.commit();
    return { /* 合并结果 */ };
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
}
```

### 前端实现

#### 1. 邮箱绑定页面

```typescript
// LinkEmailPage.tsx
const handleVerifyAndLink = async () => {
  try {
    const response = await apiService.verifyEmailCode({
      email,
      code: verificationCode
    });

    // 检查是否是账号合并
    if (response.mergedData) {
      console.log('检测到账号合并:', response.mergedData);
    }

    updateUser(response.user);
    setStep('success');
  } catch (error) {
    setError(error.response?.data?.message || '验证失败');
  }
};
```

#### 2. 账号合并页面

```typescript
// MergeAccountPage.tsx
const handleSubmit = async (e: React.FormEvent) => {
  try {
    const result = await apiService.mergeAccount(email);
    updateUser(result.user);
    setMergeResult(result.mergedData);
    setSuccess(true);
  } catch (error) {
    setError(error.message || '账号合并失败');
  }
};
```

## 📊 数据合并规则

### 1. VIP权限合并

- **规则**：取较长的VIP到期时间
- **特殊情况**：如果都没有VIP，给予1天VIP奖励

```typescript
let finalVipExpireAt = null;
if (targetUser.isVip && targetUser.vipExpireAt) {
  const targetVipExpire = new Date(targetUser.vipExpireAt);
  const currentVipExpire = currentUser.vipExpireAt ? new Date(currentUser.vipExpireAt) : new Date();
  
  finalVipExpireAt = targetVipExpire > currentVipExpire ? targetVipExpire : currentVipExpire;
}
```

### 2. 金币合并

- **规则**：取较大的金币数量
- **记录**：记录继承的金币数量

```typescript
const finalCoins = Math.max(currentUser.coins || 0, targetUser.coins || 0);
const coinsInherited = (targetUser.coins || 0) > (currentUser.coins || 0) 
  ? (targetUser.coins || 0) - (currentUser.coins || 0) 
  : 0;
```

### 3. 用户数据转移

- **收藏记录**：全部转移到当前用户
- **购买记录**：全部转移到当前用户
- **观看历史**：全部转移到当前用户
- **订单记录**：全部转移到当前用户

### 4. 邀请关系转移

- **被邀请者**：更新为当前用户邀请
- **邀请统计**：取较大值
- **佣金记录**：累加

### 5. 账号状态

- **当前用户**：保持活跃，更新为合并后的信息
- **目标用户**：标记为已合并，禁用账号

## 🔒 安全考虑

### 1. 事务保证

- 使用数据库事务确保数据一致性
- 合并失败时自动回滚

### 2. 权限验证

- 只能合并已验证邮箱的账号
- 需要用户认证才能执行合并

### 3. 数据完整性

- 避免重复数据转移
- 保留原账号记录用于审计

## 📱 用户体验

### 1. 自动合并

- 用户绑定邮箱时自动检测并合并
- 无需额外操作，体验流畅

### 2. 合并提示

- 清晰显示合并的内容
- 展示继承的VIP、金币等信息

### 3. 合并历史

- 保留合并记录
- 支持查看合并详情

## 🧪 测试用例

### 1. 基本合并测试

```bash
# 运行测试脚本
node backend/test-account-merge.js
```

### 2. 边界情况测试

- 合并不存在的邮箱
- 合并自己的账号
- 合并未验证的邮箱
- 重复合并

### 3. 数据完整性测试

- 验证VIP权限正确合并
- 验证金币数量正确
- 验证用户数据完整转移

## 📈 监控指标

### 1. 合并成功率

- 监控合并操作的成功率
- 记录失败原因

### 2. 数据完整性

- 验证合并后数据的完整性
- 监控数据丢失情况

### 3. 用户满意度

- 收集用户对合并功能的反馈
- 优化用户体验

## 🔄 未来优化

### 1. 智能合并

- 基于用户行为智能判断合并策略
- 提供更个性化的合并选项

### 2. 批量合并

- 支持管理员批量合并账号
- 提供合并预览功能

### 3. 合并撤销

- 在一定时间内支持合并撤销
- 提供数据恢复功能

---

**账号合并系统确保用户永远不会因为忘记账号而丢失数据，提供了完整的账号找回和数据继承解决方案。** 🎉
