# SmartVideoWeb Makefile
# 一键启动和管理脚本

.PHONY: help dev start build clean install seed lint

# 默认目标
help:
	@echo "🚀 SmartVideoWeb 项目管理命令"
	@echo "================================"
	@echo "make dev        - 启动开发服务器 (前端 + 后端)"
	@echo "make start      - 启动生产服务器"
	@echo "make build      - 构建项目"
	@echo "make install    - 安装所有依赖"
	@echo "make clean      - 清理 node_modules 和构建文件"
	@echo "make seed       - 初始化数据库数据"
	@echo "make lint       - 代码检查"
	@echo "make help       - 显示此帮助信息"

# 开发模式 - 同时启动前端和后端
dev:
	@echo "🎯 启动开发服务器..."
	@echo "后端: http://localhost:3002"
	@echo "前端: http://localhost:5173"
	@echo "按 Ctrl+C 停止服务器"
	@npm run dev

# 生产模式
start:
	@echo "🚀 启动生产服务器..."
	@npm run start

# 构建项目
build:
	@echo "🔨 构建项目..."
	@npm run build

# 安装所有依赖
install:
	@echo "📦 安装所有依赖..."
	@npm run install:all

# 清理项目
clean:
	@echo "🧹 清理项目..."
	@npm run clean

# 数据库种子数据
seed:
	@echo "🌱 初始化数据库..."
	@npm run seed

# 代码检查
lint:
	@echo "🔍 代码检查..."
	@npm run lint

# 快速重启 (清理 + 安装 + 启动)
restart: clean install dev

# 首次设置
setup: install seed
	@echo "✅ 项目设置完成！"
	@echo "运行 'make dev' 启动开发服务器"
