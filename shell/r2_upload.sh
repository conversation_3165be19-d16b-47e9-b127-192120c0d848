#!/bin/bash

# 配置 R2 凭证
ACCOUNT_ID="20803a6fbe3b8f875c17a3c59408b9ca"  # 替换为你的 Cloudflare Account ID
ACCESS_KEY="fddcc1ebef061915b2e1add3e1a378db"       # 替换为你的 R2 Access Key
SECRET_KEY="94301e266a62bcb37a439da21cb0ff5890b406a98d68e58171d5a470ebc7da87"   # 替换为你的 R2 Secret Key
BUCKET_NAME="dev"           # 替换为你的存储桶名称
CUSTOM_DOMAIN="https://storage.free-connect.sbs"  # 自定义域名
R2_ENDPOINT="https://${ACCOUNT_ID}.r2.cloudflarestorage.com"  # 使用 R2 原始端点

# 检查参数
if [ "$#" -ne 2 ]; then
    echo "用法: $0 <本地文件> <R2路径>"
    exit 1
fi

LOCAL_FILE="$1"
R2_KEY="${2#/}"  # 移除开头斜杠

# 生成签名
CONTENT_TYPE="application/x-apple-aspen-config"
DATE=$(date -u +"%Y%m%dT%H%M%SZ")
AMZ_DATE=$(date -u +"%Y%m%d")

# 生成签名密钥
function hmac_sha256 {
    printf "$2" | openssl dgst -sha256 -mac HMAC -macopt "$1" | sed 's/^.* //'
}

# 删除 R2 文件的函数
delete_r2_file() {
    local key=$1
    local date=$(date -u +"%Y%m%dT%H%M%SZ")
    local amz_date=$(date -u +"%Y%m%d")
    
    # 生成签名密钥
    local signing_key=$(hmac_sha256 "key:AWS4$SECRET_KEY" "$amz_date")
    signing_key=$(hmac_sha256 "hexkey:$signing_key" "auto")
    signing_key=$(hmac_sha256 "hexkey:$signing_key" "s3")
    signing_key=$(hmac_sha256 "hexkey:$signing_key" "aws4_request")
    
    # 规范请求
    local canonical_request="DELETE
/${BUCKET_NAME}/${key}

host:${ACCOUNT_ID}.r2.cloudflarestorage.com
x-amz-content-sha256:UNSIGNED-PAYLOAD
x-amz-date:${date}

host;x-amz-content-sha256;x-amz-date
UNSIGNED-PAYLOAD"
    
    # 字符串签名
    local string_to_sign="AWS4-HMAC-SHA256
${date}
${amz_date}/auto/s3/aws4_request
$(printf "$canonical_request" | openssl dgst -sha256 | sed 's/^.* //')"
    
    # 计算签名
    local signature=$(hmac_sha256 "hexkey:$signing_key" "$string_to_sign")
    
    # 执行删除
    echo "正在删除 R2 上的文件 ${key}..."
    local response=$(curl -v -X DELETE \
        -H "Host: ${ACCOUNT_ID}.r2.cloudflarestorage.com" \
        -H "x-amz-content-sha256: UNSIGNED-PAYLOAD" \
        -H "x-amz-date: $date" \
        -H "Authorization: AWS4-HMAC-SHA256 Credential=${ACCESS_KEY}/${amz_date}/auto/s3/aws4_request, SignedHeaders=host;x-amz-content-sha256;x-amz-date, Signature=${signature}" \
        "${R2_ENDPOINT}/${BUCKET_NAME}/${key}" 2>&1)
    
    # 检查删除结果
    if [[ $response == *"<Error>"* ]]; then
        echo "删除失败: $response"
        return 1
    elif [[ $response == *"HTTP/"* ]]; then
        local http_code=$(echo "$response" | grep -o "HTTP/[0-9.]* [0-9]*" | tail -n 1 | awk '{print $2}')
        if [[ "$http_code" -ge 200 && "$http_code" -lt 300 ]]; then
            echo "删除成功! (HTTP $http_code)"
            return 0
        else
            echo "删除失败: HTTP $http_code"
            echo "响应内容: $response"
            return 1
        fi
    else
        echo "删除失败: 未知错误"
        echo "响应内容: $response"
        return 1
    fi
}

# 先尝试删除文件
delete_r2_file "$R2_KEY"

# 获取文件大小（兼容macOS和Linux）
if [[ "$(uname)" == "Darwin" ]]; then
    FILE_SIZE=$(stat -f%z "$LOCAL_FILE")
else
    FILE_SIZE=$(stat -c%s "$LOCAL_FILE")
fi

# 生成签名
CONTENT_TYPE="application/x-apple-aspen-config"
DATE=$(date -u +"%Y%m%dT%H%M%SZ")
AMZ_DATE=$(date -u +"%Y%m%d")

# 生成签名密钥
function hmac_sha256 {
    printf "$2" | openssl dgst -sha256 -mac HMAC -macopt "$1" | sed 's/^.* //'
}

SIGNING_KEY=$(hmac_sha256 "key:AWS4$SECRET_KEY" "$AMZ_DATE")
SIGNING_KEY=$(hmac_sha256 "hexkey:$SIGNING_KEY" "auto")
SIGNING_KEY=$(hmac_sha256 "hexkey:$SIGNING_KEY" "s3")
SIGNING_KEY=$(hmac_sha256 "hexkey:$SIGNING_KEY" "aws4_request")

# 规范请求
CANONICAL_REQUEST="PUT
/${BUCKET_NAME}/${R2_KEY}

content-length:${FILE_SIZE}
content-type:${CONTENT_TYPE}
host:${ACCOUNT_ID}.r2.cloudflarestorage.com
x-amz-content-sha256:UNSIGNED-PAYLOAD
x-amz-date:${DATE}

content-length;content-type;host;x-amz-content-sha256;x-amz-date
UNSIGNED-PAYLOAD"

# 字符串签名
STRING_TO_SIGN="AWS4-HMAC-SHA256
${DATE}
${AMZ_DATE}/auto/s3/aws4_request
$(printf "$CANONICAL_REQUEST" | openssl dgst -sha256 | sed 's/^.* //')"

# 计算签名
SIGNATURE=$(hmac_sha256 "hexkey:$SIGNING_KEY" "$STRING_TO_SIGN")

# 执行上传
echo "正在上传 $LOCAL_FILE 到 R2..."

# 创建临时文件来存储文件内容
TEMP_FILE=$(mktemp)
cat "$LOCAL_FILE" > "$TEMP_FILE"

# 添加调试信息
echo "请求URL: ${R2_ENDPOINT}/${BUCKET_NAME}/${R2_KEY}"
echo "Host: ${ACCOUNT_ID}.r2.cloudflarestorage.com"
echo "Content-Type: ${CONTENT_TYPE}"
echo "Content-Length: ${FILE_SIZE}"

RESPONSE=$(curl -v -X PUT -T "$TEMP_FILE" \
    -H "Content-Length: $FILE_SIZE" \
    -H "Content-Type: $CONTENT_TYPE" \
    -H "Host: ${ACCOUNT_ID}.r2.cloudflarestorage.com" \
    -H "x-amz-content-sha256: UNSIGNED-PAYLOAD" \
    -H "x-amz-date: $DATE" \
    -H "Authorization: AWS4-HMAC-SHA256 Credential=${ACCESS_KEY}/${AMZ_DATE}/auto/s3/aws4_request, SignedHeaders=content-length;content-type;host;x-amz-content-sha256;x-amz-date, Signature=${SIGNATURE}" \
    "${R2_ENDPOINT}/${BUCKET_NAME}/${R2_KEY}" 2>&1)

# 清理临时文件
rm -f "$TEMP_FILE"

# 检查响应中是否包含错误
if [[ $RESPONSE == *"<Error>"* ]]; then
    echo "上传失败: $RESPONSE"
    exit 1
elif [[ $RESPONSE == *"HTTP/"* ]]; then
    # 检查 HTTP 状态码
    HTTP_CODE=$(echo "$RESPONSE" | grep -o "HTTP/[0-9.]* [0-9]*" | tail -n 1 | awk '{print $2}')
    if [[ "$HTTP_CODE" -ge 200 && "$HTTP_CODE" -lt 300 ]]; then
        echo "上传成功! (HTTP $HTTP_CODE)"
        echo "访问地址: ${CUSTOM_DOMAIN}/${R2_KEY}"
    else
        echo "上传失败: HTTP $HTTP_CODE"
        echo "响应内容: $RESPONSE"
        exit 1
    fi
else
    echo "上传失败: 未知错误"
    echo "响应内容: $RESPONSE"
    exit 1
fi