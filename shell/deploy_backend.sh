#!/bin/bash

# 赫本平台后端部署脚本
# 功能：下载、部署和启动后端应用

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
VERSION="$1"
if [ -z "$VERSION" ]; then
    echo "错误: 请指定版本号"
    echo "用法: $0 <version>"
    echo "示例: $0 20250608-150227"
    echo ""
    echo "提示: 使用 ./shell/version_manager.sh list backend 查看可用版本"
    exit 1
fi

FILENAME="backend-${VERSION}.tar.gz"
DOWNLOAD_URL="https://storage.free-connect.sbs/backend/${FILENAME}"
DEPLOY_DIR="/opt/smart-video-backend"
SERVICE_NAME="smart-video-backend"
SERVICE_PORT="3002"
LOG_DIR="/var/log/smart-video-backend"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查系统要求
check_system_requirements() {
    log_info "检查系统要求..."
    
    # 检查操作系统
    if [[ "$OSTYPE" != "linux-gnu"* ]]; then
        log_warning "此脚本主要为 Linux 系统设计"
    fi
    
    # 检查是否为 root 用户
    if [[ $EUID -ne 0 ]]; then
        log_warning "建议使用 root 权限运行此脚本以获得最佳体验"
        log_info "当前将以普通用户权限运行"
    fi
    
    # 检查必要工具
    local missing_tools=()
    
    if ! command -v wget &> /dev/null && ! command -v curl &> /dev/null; then
        missing_tools+=("wget 或 curl")
    fi
    
    if ! command -v tar &> /dev/null; then
        missing_tools+=("tar")
    fi
    
    if ! command -v node &> /dev/null; then
        missing_tools+=("node")
    fi
    
    if ! command -v npm &> /dev/null; then
        missing_tools+=("npm")
    fi
    
    if [ ${#missing_tools[@]} -ne 0 ]; then
        log_error "缺少必要工具: ${missing_tools[*]}"
        log_info "请先安装这些工具，然后重新运行脚本"
        exit 1
    fi
    
    # 检查 Node.js 版本
    NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 16 ]; then
        log_error "Node.js 版本过低 (当前: v$(node --version | cut -d'v' -f2), 要求: >= 16.0.0)"
        exit 1
    fi
    
    log_success "系统要求检查通过"
}

# 停止现有服务
stop_existing_service() {
    log_info "停止现有服务..."
    
    # 尝试停止 PM2 进程
    if command -v pm2 &> /dev/null; then
        pm2 stop $SERVICE_NAME 2>/dev/null || true
        pm2 delete $SERVICE_NAME 2>/dev/null || true
        log_info "已停止 PM2 进程"
    fi
    
    # 尝试停止 systemd 服务
    if systemctl is-active --quiet $SERVICE_NAME 2>/dev/null; then
        systemctl stop $SERVICE_NAME
        log_info "已停止 systemd 服务"
    fi
    
    # 查找并终止占用端口的进程
    local pid=$(lsof -ti:$SERVICE_PORT 2>/dev/null || true)
    if [ ! -z "$pid" ]; then
        log_warning "发现端口 $SERVICE_PORT 被进程 $pid 占用，正在终止..."
        kill -9 $pid 2>/dev/null || true
        sleep 2
    fi
    
    log_success "现有服务已停止"
}

# 备份现有部署
backup_existing_deployment() {
    if [ -d "$DEPLOY_DIR" ]; then
        log_info "备份现有部署..."
        
        local backup_dir="${DEPLOY_DIR}.backup.$(date +%Y%m%d-%H%M%S)"
        mv "$DEPLOY_DIR" "$backup_dir"
        
        log_success "已备份到: $backup_dir"
    fi
}

# 下载后端包
download_backend_package() {
    log_info "下载后端包..."
    
    local temp_dir=$(mktemp -d)
    cd "$temp_dir"
    
    # 尝试使用 wget 或 curl 下载
    if command -v wget &> /dev/null; then
        wget "$DOWNLOAD_URL" -O "$FILENAME"
    elif command -v curl &> /dev/null; then
        curl -L "$DOWNLOAD_URL" -o "$FILENAME"
    else
        log_error "无法下载文件，缺少 wget 或 curl"
        exit 1
    fi

    # 检查下载是否成功
    if [ ! -f "$FILENAME" ] || [ ! -s "$FILENAME" ]; then
        log_error "下载失败或文件为空，请检查版本号是否正确"
        exit 1
    fi

    local file_size=$(du -h "$FILENAME" | cut -f1)
    log_success "下载完成: $FILENAME ($file_size)"
    
    # 保存临时目录路径
    export TEMP_DOWNLOAD_DIR="$temp_dir"
}

# 解压和部署
extract_and_deploy() {
    log_info "解压和部署应用..."
    
    cd "$TEMP_DOWNLOAD_DIR"

    # 解压文件
    tar --no-xattrs -xzf "$FILENAME"
    
    # 创建部署目录
    mkdir -p "$DEPLOY_DIR"
    mkdir -p "$LOG_DIR"

    # 移动文件到部署目录
    mv ./* "$DEPLOY_DIR/" 2>/dev/null || true

    # 设置权限
    chmod +x "$DEPLOY_DIR/start.sh"
    chmod -R 755 "$DEPLOY_DIR"
    
    # 清理临时文件
    cd /
    rm -rf "$TEMP_DOWNLOAD_DIR"
    
    log_success "应用部署完成"
}

# 安装依赖
install_dependencies() {
    log_info "安装应用依赖..."
    
    cd "$DEPLOY_DIR"
    
    # 安装生产依赖
    npm ci --production --silent
    
    log_success "依赖安装完成"
}

# 配置环境
configure_environment() {
    log_info "配置环境..."
    
    cd "$DEPLOY_DIR"
    
    # 检查环境配置文件
    if [ -f ".env.production" ]; then
        log_info "发现 .env.production 文件，复制为 .env"
        cp .env.production .env
        log_success "生产环境配置已应用"
    elif [ -f ".env" ]; then
        log_success "发现 .env 文件，使用现有配置"
    elif [ -f ".env.example" ]; then
        log_info "创建 .env 文件..."
        cp .env.example .env
        log_warning "请编辑 .env 文件配置数据库连接等信息"
    else
        log_warning "未找到环境配置文件，请手动创建 .env 文件"
    fi

    # 确保 NODE_ENV 设置为 production
    if [ -f ".env" ]; then
        if ! grep -q "NODE_ENV=production" .env; then
            echo "NODE_ENV=production" >> .env
            log_info "已设置 NODE_ENV=production"
        fi
    fi
    
    # 设置日志目录权限
    chmod -R 755 "$LOG_DIR"
    
    log_success "环境配置完成"
}

# 安装和配置 PM2
setup_pm2() {
    log_info "配置 PM2..."

    # PM2 已安装，跳过安装步骤
    if ! command -v pm2 &> /dev/null; then
        log_error "PM2 未安装，请先安装 PM2: npm install -g pm2"
        exit 1
    fi

    cd "$DEPLOY_DIR"

    # 更新 PM2 配置文件中的日志路径
    if [ -f "ecosystem.config.js" ]; then
        sed -i "s|./logs/|$LOG_DIR/|g" ecosystem.config.js
    fi

    log_success "PM2 配置完成"
}

# 创建 systemd 服务 (跳过，直接使用 PM2)
create_systemd_service() {
    log_info "跳过 systemd 服务创建，使用 PM2 管理服务..."
    log_success "将使用 PM2 管理服务"
}

# 启动服务
start_service() {
    log_info "启动服务..."

    cd "$DEPLOY_DIR"

    # 直接使用 PM2 启动服务
    log_info "使用 PM2 启动服务..."

    # 停止可能存在的同名进程
    pm2 stop $SERVICE_NAME 2>/dev/null || true
    pm2 delete $SERVICE_NAME 2>/dev/null || true

    # 启动新服务
    if [ -f "ecosystem.config.js" ]; then
        pm2 start ecosystem.config.js
    else
        # 如果没有 ecosystem.config.js，直接启动 start.sh
        pm2 start ./start.sh --name "$SERVICE_NAME"
    fi

    pm2 save

    # 设置 PM2 开机自启 (跳过，避免权限问题)
    log_info "跳过 PM2 开机自启设置 (避免权限问题)"

    # 等待服务启动
    sleep 5

    log_success "服务启动完成"
}

# 验证部署
verify_deployment() {
    log_info "验证部署..."

    # 检查 PM2 进程状态
    if pm2 list | grep -q "$SERVICE_NAME.*online"; then
        log_success "PM2 进程运行正常"
    else
        log_warning "PM2 进程状态异常"
        pm2 logs $SERVICE_NAME --lines 10
        return 1
    fi

    # 等待应用启动
    log_info "等待应用启动..."
    sleep 10

    # 检查端口是否被监听
    local port_check=false
    for i in {1..5}; do
        if command -v lsof &> /dev/null && lsof -i:$SERVICE_PORT &> /dev/null; then
            port_check=true
            break
        elif command -v netstat &> /dev/null && netstat -ln | grep ":$SERVICE_PORT " &> /dev/null; then
            port_check=true
            break
        elif command -v ss &> /dev/null && ss -ln | grep ":$SERVICE_PORT " &> /dev/null; then
            port_check=true
            break
        fi
        log_info "等待端口启动... ($i/5)"
        sleep 3
    done

    if [ "$port_check" = true ]; then
        log_success "服务正在监听端口 $SERVICE_PORT"
    else
        log_warning "端口检查失败，但 PM2 进程正常运行"
        log_info "请手动检查应用日志: pm2 logs $SERVICE_NAME"
        log_info "检查端口: lsof -i:$SERVICE_PORT 或 netstat -ln | grep $SERVICE_PORT"
    fi

    log_success "部署验证完成"
}

# 显示部署信息
show_deployment_info() {
    log_info "部署信息:"
    echo "=================================="
    echo "服务名称: $SERVICE_NAME"
    echo "部署目录: $DEPLOY_DIR"
    echo "服务端口: $SERVICE_PORT"
    echo "日志目录: $LOG_DIR"
    echo "=================================="
    echo ""
    echo "常用命令:"
    echo "查看状态: pm2 status"
    echo "查看日志: pm2 logs $SERVICE_NAME"
    echo "重启服务: pm2 restart $SERVICE_NAME"
    echo "停止服务: pm2 stop $SERVICE_NAME"
    echo "重新加载: pm2 reload $SERVICE_NAME"
    echo ""
    echo "访问地址: http://localhost:$SERVICE_PORT"
    echo "API文档: http://localhost:$SERVICE_PORT/api"
}

# 主函数
main() {
    log_info "开始部署赫本平台后端..."
    
    # 执行部署步骤
    check_system_requirements
    stop_existing_service
    backup_existing_deployment
    download_backend_package
    extract_and_deploy
    install_dependencies
    configure_environment
    setup_pm2
    create_systemd_service
    start_service
    verify_deployment
    show_deployment_info
    
    log_success "后端部署完成！"
}

# 处理命令行参数
case "${1:-}" in
    --help|-h)
        echo "赫本平台后端部署脚本"
        echo ""
        echo "用法: $0 [选项]"
        echo ""
        echo "选项:"
        echo "  --help, -h     显示此帮助信息"
        echo "  --force        强制部署，跳过确认"
        echo ""
        exit 0
        ;;
    --force)
        # 强制部署模式
        ;;
    *)
        # 默认模式，询问确认
        echo "即将部署赫本平台后端到 $DEPLOY_DIR"
        read -p "是否继续? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "部署已取消"
            exit 0
        fi
        ;;
esac

# 执行主函数
main "$@"
