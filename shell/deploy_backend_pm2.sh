#!/bin/bash

# 赫本平台后端 PM2 部署脚本
# 功能：专门使用 PM2 部署后端，避免权限问题

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
VERSION="$1"
if [ -z "$VERSION" ]; then
    echo "错误: 请指定版本号"
    echo "用法: $0 <version>"
    echo "示例: $0 20250608-150227"
    exit 1
fi

FILENAME="backend-${VERSION}.tar.gz"
DOWNLOAD_URL="https://storage.free-connect.sbs/backend/${FILENAME}"
DEPLOY_DIR="/opt/smart-video-backend"
SERVICE_NAME="smart-video-backend"
SERVICE_PORT="3002"
LOG_DIR="/var/log/smart-video-backend"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查 PM2
check_pm2() {
    if ! command -v pm2 &> /dev/null; then
        log_error "PM2 未安装，请先安装: npm install -g pm2"
        exit 1
    fi
    log_success "PM2 已安装"
}

# 停止现有服务
stop_existing_service() {
    log_info "停止现有服务..."
    pm2 stop $SERVICE_NAME 2>/dev/null || true
    pm2 delete $SERVICE_NAME 2>/dev/null || true
    log_success "现有服务已停止"
}

# 备份现有部署
backup_existing_deployment() {
    if [ -d "$DEPLOY_DIR" ]; then
        log_info "备份现有部署..."
        local backup_dir="${DEPLOY_DIR}.backup.$(date +%Y%m%d-%H%M%S)"
        mv "$DEPLOY_DIR" "$backup_dir"
        log_success "已备份到: $backup_dir"
    fi
}

# 下载和部署
download_and_deploy() {
    log_info "下载后端版本: ${VERSION}"
    
    local temp_dir=$(mktemp -d)
    cd "$temp_dir"
    
    # 下载文件
    if command -v wget &> /dev/null; then
        wget "$DOWNLOAD_URL" -O "$FILENAME"
    elif command -v curl &> /dev/null; then
        curl -L "$DOWNLOAD_URL" -o "$FILENAME"
    else
        log_error "需要 wget 或 curl 来下载文件"
        exit 1
    fi
    
    # 检查下载
    if [ ! -f "$FILENAME" ] || [ ! -s "$FILENAME" ]; then
        log_error "下载失败，请检查版本号"
        exit 1
    fi
    
    # 创建部署目录
    mkdir -p "$DEPLOY_DIR"
    mkdir -p "$LOG_DIR"
    
    # 解压文件
    tar --no-xattrs -xzf "$FILENAME" -C "$DEPLOY_DIR"
    
    # 设置权限
    chmod +x "$DEPLOY_DIR/start.sh"
    chmod -R 755 "$DEPLOY_DIR"
    
    # 清理临时文件
    cd /
    rm -rf "$temp_dir"
    
    log_success "应用部署完成"
}

# 安装依赖
install_dependencies() {
    log_info "安装应用依赖..."
    cd "$DEPLOY_DIR"
    npm ci --production --silent
    log_success "依赖安装完成"
}

# 配置环境
configure_environment() {
    log_info "配置环境..."
    cd "$DEPLOY_DIR"

    # 检查环境配置文件
    if [ -f ".env.production" ]; then
        log_info "发现 .env.production 文件，复制为 .env"
        cp .env.production .env
        log_success "生产环境配置已应用"
    elif [ -f ".env" ]; then
        log_success "发现 .env 文件，使用现有配置"
    elif [ -f ".env.example" ]; then
        log_info "创建 .env 文件..."
        cp .env.example .env
        log_warning "请编辑 .env 文件配置数据库连接等信息"
    else
        log_warning "未找到环境配置文件，请手动创建 .env 文件"
    fi

    # 确保 NODE_ENV 设置为 production
    if [ -f ".env" ]; then
        if ! grep -q "NODE_ENV=production" .env; then
            echo "NODE_ENV=production" >> .env
            log_info "已设置 NODE_ENV=production"
        fi
    fi

    # 更新 PM2 配置中的日志路径
    if [ -f "ecosystem.config.js" ]; then
        sed -i "s|./logs/|$LOG_DIR/|g" ecosystem.config.js
    fi

    log_success "环境配置完成"
}

# 启动服务
start_service() {
    log_info "使用 PM2 启动服务..."
    cd "$DEPLOY_DIR"
    
    # 启动服务
    if [ -f "ecosystem.config.js" ]; then
        pm2 start ecosystem.config.js
    else
        pm2 start ./start.sh --name "$SERVICE_NAME" --log "$LOG_DIR/combined.log"
    fi
    
    pm2 save

    # 等待启动
    sleep 5
    
    log_success "服务启动完成"
}

# 验证部署
verify_deployment() {
    log_info "验证部署..."

    # 检查 PM2 状态
    if pm2 list | grep -q "$SERVICE_NAME.*online"; then
        log_success "PM2 服务运行正常"
    else
        log_warning "PM2 服务状态异常"
        pm2 logs $SERVICE_NAME --lines 10
        return 1
    fi

    # 等待应用完全启动
    log_info "等待应用完全启动..."
    sleep 10

    # 检查端口 (多次尝试)
    local port_check=false
    for i in {1..5}; do
        if command -v lsof &> /dev/null && lsof -i:$SERVICE_PORT &> /dev/null; then
            port_check=true
            break
        elif command -v netstat &> /dev/null && netstat -ln | grep ":$SERVICE_PORT " &> /dev/null; then
            port_check=true
            break
        elif command -v ss &> /dev/null && ss -ln | grep ":$SERVICE_PORT " &> /dev/null; then
            port_check=true
            break
        fi
        log_info "等待端口启动... ($i/5)"
        sleep 3
    done

    if [ "$port_check" = true ]; then
        log_success "服务正在监听端口 $SERVICE_PORT"
    else
        log_warning "端口检查失败，但 PM2 进程正常运行"
        log_info "请检查应用配置和日志: pm2 logs $SERVICE_NAME"
    fi

    log_success "部署验证完成"
}

# 显示部署信息
show_deployment_info() {
    echo ""
    echo "=================================="
    echo "🎉 后端部署完成！"
    echo "=================================="
    echo "服务名称: $SERVICE_NAME"
    echo "部署目录: $DEPLOY_DIR"
    echo "服务端口: $SERVICE_PORT"
    echo "日志目录: $LOG_DIR"
    echo "=================================="
    echo ""
    echo "📋 常用命令:"
    echo "查看状态: pm2 status"
    echo "查看日志: pm2 logs $SERVICE_NAME"
    echo "重启服务: pm2 restart $SERVICE_NAME"
    echo "停止服务: pm2 stop $SERVICE_NAME"
    echo "重新加载: pm2 reload $SERVICE_NAME"
    echo ""
    echo "🌐 访问地址: http://localhost:$SERVICE_PORT"
    echo "📊 监控面板: pm2 monit"
}

# 主函数
main() {
    log_info "开始 PM2 部署后端..."
    
    check_pm2
    stop_existing_service
    backup_existing_deployment
    download_and_deploy
    install_dependencies
    configure_environment
    start_service
    verify_deployment
    show_deployment_info
}

# 执行主函数
main "$@"
