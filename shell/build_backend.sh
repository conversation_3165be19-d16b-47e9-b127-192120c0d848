#!/bin/bash

# 赫本平台后端构建脚本
# 功能：构建后端应用，打包并上传到云存储

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查必要的工具
check_dependencies() {
    log_info "检查构建依赖..."
    
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装，请先安装 Node.js"
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        log_error "npm 未安装，请先安装 npm"
        exit 1
    fi
    
    if ! command -v tar &> /dev/null; then
        log_error "tar 未安装，请先安装 tar"
        exit 1
    fi
    
    log_success "依赖检查完成"
}

# 清理旧的构建文件
cleanup_old_builds() {
    log_info "清理旧的构建文件..."
    
    cd backend
    
    # 清理旧的构建目录
    rm -rf dist/
    rm -rf build/
    rm -rf *.zip
    rm -rf *.tar.gz
    
    # 清理 node_modules 中的缓存
    if [ -d "node_modules" ]; then
        log_info "清理 node_modules 缓存..."
        rm -rf node_modules/.cache/
    fi
    
    cd ..
    
    log_success "清理完成"
}

# 安装依赖
install_dependencies() {
    log_info "安装后端依赖..."
    
    cd backend
    
    # 检查 package.json 是否存在
    if [ ! -f "package.json" ]; then
        log_error "package.json 不存在，请检查后端目录"
        exit 1
    fi
    
    # 安装依赖
    npm ci --production=false
    
    cd ..
    
    log_success "依赖安装完成"
}

# 构建 TypeScript
build_typescript() {
    log_info "构建 TypeScript..."
    
    cd backend
    
    # 检查 tsconfig.json 是否存在
    if [ ! -f "tsconfig.json" ]; then
        log_warning "tsconfig.json 不存在，跳过 TypeScript 构建"
        cd ..
        return
    fi
    
    # 构建 TypeScript
    npx tsc
    
    cd ..
    
    log_success "TypeScript 构建完成"
}

# 创建生产环境包
create_production_package() {
    log_info "创建生产环境包..."
    
    cd backend
    
    # 创建构建目录
    mkdir -p build
    
    # 复制必要文件到构建目录
    log_info "复制应用文件..."
    
    # 复制源代码（如果有 dist 目录则复制 dist，否则复制 src）
    if [ -d "dist" ]; then
        cp -r dist/* build/
        log_info "复制编译后的文件"
    else
        cp -r src/* build/
        log_info "复制源代码文件"
    fi
    
    # 复制配置文件
    [ -f "package.json" ] && cp package.json build/
    [ -f "package-lock.json" ] && cp package-lock.json build/
    [ -f ".env.example" ] && cp .env.example build/
    [ -f ".env.production" ] && cp .env.production build/
    [ -f ".env" ] && cp .env build/
    [ -f "ecosystem.config.js" ] && cp ecosystem.config.js build/
    
    # 复制其他必要文件
    [ -d "public" ] && cp -r public build/
    [ -d "uploads" ] && cp -r uploads build/
    [ -d "docs" ] && cp -r docs build/
    [ -d "scripts" ] && cp -r scripts build/
    [ -d "data" ] && cp -r data build/
    
    # 创建启动脚本
    cat > build/start.sh << 'EOF'
#!/bin/bash

# 赫本平台后端启动脚本

# 检查 Node.js
if ! command -v node &> /dev/null; then
    echo "错误: Node.js 未安装"
    exit 1
fi

# 检查 npm
if ! command -v npm &> /dev/null; then
    echo "错误: npm 未安装"
    exit 1
fi

# 安装生产依赖
echo "安装生产依赖..."
npm ci --production

# 设置环境变量
export NODE_ENV=production

# 启动应用
echo "启动赫本平台后端..."
if [ -f "dist/index.js" ]; then
    node dist/index.js
elif [ -f "index.js" ]; then
    node index.js
elif [ -f "src/index.js" ]; then
    node src/index.js
elif [ -f "app.js" ]; then
    node app.js
else
    echo "错误: 找不到入口文件"
    exit 1
fi
EOF
    
    chmod +x build/start.sh
    
    # 创建 PM2 配置文件
    cat > build/ecosystem.config.js << 'EOF'
module.exports = {
  apps: [{
    name: 'smart-video-backend',
    script: './start.sh',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
      PORT: 3002
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true
  }]
};
EOF
    
    # 创建日志目录
    mkdir -p build/logs
    
    cd ..
    
    log_success "生产环境包创建完成"
}

# 打包应用
package_application() {
    log_info "打包应用..."

    cd backend

    # 生成时间戳
    TIMESTAMP=$(date +"%Y%m%d-%H%M%S")
    PACKAGE_NAME="backend-${TIMESTAMP}.tar.gz"

    # 创建压缩包
    cd build
    tar --no-xattrs -czf "../${PACKAGE_NAME}" --exclude="*.log" --exclude="logs/*" --exclude="node_modules/*" .
    cd ..

    # 检查压缩包大小
    PACKAGE_SIZE=$(du -h "${PACKAGE_NAME}" | cut -f1)
    log_success "打包完成: ${PACKAGE_NAME} (${PACKAGE_SIZE})"

    # 保存包名到环境变量
    export BACKEND_PACKAGE_NAME="${PACKAGE_NAME}"

    cd ..
}

# 上传到云存储
upload_to_storage() {
    log_info "上传到云存储..."
    
    if [ ! -f "./shell/r2_upload.sh" ]; then
        log_error "r2_upload.sh 脚本不存在"
        exit 1
    fi
    
    # 上传后端包
    ./shell/r2_upload.sh "backend/${BACKEND_PACKAGE_NAME}" "backend/${BACKEND_PACKAGE_NAME}"
    
    if [ $? -eq 0 ]; then
        log_success "后端包上传成功"
        
        # 清理本地包文件
        rm -f "backend/${BACKEND_PACKAGE_NAME}"
        log_info "清理本地包文件"
    else
        log_error "后端包上传失败"
        exit 1
    fi
}

# 生成部署信息
generate_deploy_info() {
    log_info "生成部署信息..."
    
    cat > backend-deploy-info.txt << EOF
赫本平台后端部署信息
========================

构建时间: $(date)
包名称: ${BACKEND_PACKAGE_NAME}
下载地址: https://storage.free-connect.sbs/backend/${BACKEND_PACKAGE_NAME}

部署步骤:
1. 下载后端包: wget https://storage.free-connect.sbs/backend/${BACKEND_PACKAGE_NAME}
2. 解压: tar -xzf ${BACKEND_PACKAGE_NAME}
3. 进入目录: cd backend
4. 运行启动脚本: ./start.sh

或使用 PM2:
1. 安装 PM2: npm install -g pm2
2. 启动应用: pm2 start ecosystem.config.js
3. 查看状态: pm2 status
4. 查看日志: pm2 logs smart-video-backend

环境要求:
- Node.js >= 16.0.0
- npm >= 8.0.0
- MySQL 数据库
- 端口 3002 可用

配置文件:
- 复制 .env.example 为 .env
- 修改数据库连接信息
- 设置其他环境变量

注意事项:
- 确保数据库已创建并可连接
- 检查防火墙设置
- 建议使用 PM2 进行进程管理
EOF
    
    log_success "部署信息已生成: backend-deploy-info.txt"
}

# 主函数
main() {
    log_info "开始构建赫本平台后端..."
    
    # 检查当前目录
    if [ ! -d "backend" ]; then
        log_error "backend 目录不存在，请在项目根目录运行此脚本"
        exit 1
    fi
    
    # 执行构建步骤
    check_dependencies
    cleanup_old_builds
    install_dependencies
    build_typescript
    create_production_package
    package_application
    upload_to_storage
    generate_deploy_info
    
    log_success "后端构建完成！"
    log_info "部署信息请查看: backend-deploy-info.txt"
    log_info "下载地址: https://storage.free-connect.sbs/backend/${BACKEND_PACKAGE_NAME}"

    # 提取时间戳用于部署命令
    VERSION=$(echo "${BACKEND_PACKAGE_NAME}" | sed 's/backend-\(.*\)\.tar\.gz/\1/')

    echo ""
    echo -e "${GREEN}🎉 后端构建完成！${NC}"
    echo -e "${BLUE}📦 包名: ${BACKEND_PACKAGE_NAME}${NC}"
    echo -e "${BLUE}🌐 下载地址: https://storage.free-connect.sbs/backend/${BACKEND_PACKAGE_NAME}${NC}"
    echo ""
    echo -e "${YELLOW}📋 部署命令 (复制下面的命令直接执行):${NC}"
    echo -e "${GREEN}==================================================${NC}"
    echo -e "${GREEN}# PM2 部署 (推荐)${NC}"
    echo -e "${GREEN}./shell/deploy_backend_pm2.sh ${VERSION}${NC}"
    echo ""
    echo -e "${GREEN}# 标准部署${NC}"
    echo -e "${GREEN}./shell/deploy_backend.sh ${VERSION}${NC}"
    echo ""
    echo -e "${GREEN}# 如果部署后有问题，运行诊断:${NC}"
    echo -e "${GREEN}./shell/diagnose_backend.sh${NC}"
    echo -e "${GREEN}==================================================${NC}"
    echo ""
    echo -e "${BLUE}💡 提示: 推荐使用 PM2 部署脚本，如有问题请运行诊断工具${NC}"
}

# 执行主函数
main "$@"
