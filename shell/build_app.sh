#!/bin/bash

# 生成时间戳
TIMESTAMP=$(date +"%Y%m%d-%H%M%S")
PACKAGE_NAME="frontend-${TIMESTAMP}.tar.gz"

echo "🚀 开始构建前端应用..."
echo "📦 包名: ${PACKAGE_NAME}"
echo "⏰ 构建时间: $(date)"
echo ""

cd frontend && npm run build && tar --no-xattrs -czf "${PACKAGE_NAME}" -C dist . && cd ../ && ./shell/r2_upload.sh "frontend/${PACKAGE_NAME}" "frontend/${PACKAGE_NAME}" && rm "frontend/${PACKAGE_NAME}"

# 检查构建是否成功
if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 前端构建完成！"
    echo "📦 包名: ${PACKAGE_NAME}"
    echo "🌐 下载地址: https://storage.free-connect.sbs/${PACKAGE_NAME}"
    echo ""
    echo "📋 部署命令 (复制下面的命令直接执行):"
    echo "=================================================="
    echo "./shell/deploy_app.sh ${TIMESTAMP}"
    echo "=================================================="
    echo ""
    echo "💡 提示: 复制上面的命令到服务器执行即可完成部署"
else
    echo ""
    echo "❌ 前端构建失败！"
    exit 1
fi
