# 赫本平台部署脚本

这个目录包含了赫本平台的构建和部署脚本，支持前端和后端的自动化构建、打包和部署。

## 📁 脚本文件说明

### 构建脚本

| 脚本名称 | 功能描述 | 用途 |
|---------|---------|------|
| `build_app.sh` | 前端构建脚本 | 构建React前端应用并上传到云存储 |
| `build_backend.sh` | 后端构建脚本 | 构建Node.js后端应用并上传到云存储 |
| `build_admin.sh` | 管理后台构建脚本 | 构建管理后台前端和后端应用 |
| `build_all.sh` | 全栈构建脚本 | 同时构建前端和后端应用 |

### 部署脚本

| 脚本名称 | 功能描述 | 用途 |
|---------|---------|------|
| `deploy_app.sh` | 前端部署脚本 | 下载并部署前端应用到Web服务器 |
| `deploy_backend.sh` | 后端部署脚本 | 下载并部署后端应用到服务器 |

### 工具脚本

| 脚本名称 | 功能描述 | 用途 |
|---------|---------|------|
| `version_manager.sh` | 版本管理和回滚工具 | 列出、下载、部署指定版本 |
| `r2_upload.sh` | 云存储上传工具 | 上传文件到Cloudflare R2存储 |

## 🚀 快速开始

### 1. 构建应用

#### 构建前端
```bash
./shell/build_app.sh
```

#### 构建后端
```bash
./shell/build_backend.sh
```

#### 构建管理后台
```bash
./shell/build_admin.sh
```

#### 构建全栈应用
```bash
./shell/build_all.sh
```

### 2. 版本管理

#### 列出可用版本
```bash
./shell/version_manager.sh list frontend
./shell/version_manager.sh list backend
```

#### 下载指定版本
```bash
./shell/version_manager.sh download frontend 20250608-151349
./shell/version_manager.sh download backend 20250608-140315
```

#### 部署指定版本
```bash
./shell/version_manager.sh deploy frontend 20250608-151349
./shell/version_manager.sh deploy backend 20250608-140315
```

### 3. 部署应用

#### 部署前端 (需要指定版本)
```bash
./shell/deploy_app.sh 20250608-151349
```

#### 部署后端 (需要指定版本)
```bash
./shell/deploy_backend.sh 20250608-140315
```

## 📋 详细使用说明

### 构建脚本详解

## 📦 重要更新：新的打包格式

**🎉 最新变更**: 所有构建脚本现在使用 **tar.gz** 格式并添加时间戳！

### 文件命名格式
- **前端**: `frontend-YYYYMMDD-HHMMSS.tar.gz`
- **后端**: `backend-YYYYMMDD-HHMMSS.tar.gz`
- **管理后台前端**: `admin-frontend-YYYYMMDD-HHMMSS.tar.gz`
- **管理后台后端**: `admin-backend-YYYYMMDD-HHMMSS.tar.gz`

### 云存储文件名 (保留时间戳版本)
- **前端**: `frontend-YYYYMMDD-HHMMSS.tar.gz`
- **后端**: `backend-YYYYMMDD-HHMMSS.tar.gz`
- **管理后台前端**: `admin-frontend-YYYYMMDD-HHMMSS.tar.gz`
- **管理后台后端**: `admin-backend-YYYYMMDD-HHMMSS.tar.gz`

### 解压命令
```bash
# 解压到指定目录
tar -xzf package.tar.gz -C /target/directory/

# 解压到当前目录
tar -xzf package.tar.gz
```

#### `build_app.sh` - 前端构建
- **功能**: 构建React前端应用
- **输出**: `frontend-YYYYMMDD-HHMMSS.tar.gz` (保留时间戳)
- **下载地址**: `https://storage.free-connect.sbs/frontend-YYYYMMDD-HHMMSS.tar.gz`

```bash
# 执行前端构建
./shell/build_app.sh

# 构建过程:
# 1. 进入 frontend 目录
# 2. 执行 npm run build
# 3. 打包 dist 目录为 tar.gz (带时间戳)
# 4. 上传到云存储
# 5. 清理本地文件
```

#### `build_backend.sh` - 后端构建
- **功能**: 构建Node.js后端应用
- **输出**: `backend-YYYYMMDD-HHMMSS.tar.gz` (保留时间戳)
- **下载地址**: `https://storage.free-connect.sbs/backend-YYYYMMDD-HHMMSS.tar.gz`

#### `build_admin.sh` - 管理后台构建
- **功能**: 构建管理后台前端和后端
- **输出**:
  - `admin-frontend-YYYYMMDD-HHMMSS.tar.gz` (保留时间戳)
  - `admin-backend-YYYYMMDD-HHMMSS.tar.gz` (保留时间戳)
- **下载地址**:
  - `https://storage.free-connect.sbs/admin-frontend-YYYYMMDD-HHMMSS.tar.gz`
  - `https://storage.free-connect.sbs/admin-backend-YYYYMMDD-HHMMSS.tar.gz`

```bash
# 执行后端构建
./shell/build_backend.sh

# 构建过程:
# 1. 检查依赖和环境
# 2. 清理旧构建文件
# 3. 安装依赖
# 4. 编译TypeScript
# 5. 创建生产环境包
# 6. 打包并上传
# 7. 生成部署信息
```

#### `build_all.sh` - 全栈构建
- **功能**: 同时构建前端和后端
- **支持参数**:
  - `--frontend-only`: 仅构建前端
  - `--backend-only`: 仅构建后端
  - `--clean`: 清理构建文件
  - `--help`: 显示帮助信息

```bash
# 构建全栈应用
./shell/build_all.sh

# 仅构建前端
./shell/build_all.sh --frontend-only

# 仅构建后端
./shell/build_all.sh --backend-only

# 清理构建文件
./shell/build_all.sh --clean
```

### 版本管理工具详解

#### `version_manager.sh` - 版本管理和回滚工具

**🎯 核心功能**: 支持版本回滚和历史版本管理

```bash
# 查看帮助
./shell/version_manager.sh --help

# 列出组件的可用版本
./shell/version_manager.sh list <component>

# 下载指定版本到本地
./shell/version_manager.sh download <component> <version> [target_dir]

# 直接部署指定版本
./shell/version_manager.sh deploy <component> <version>
```

**支持的组件**:
- `frontend` - 前端应用
- `backend` - 后端应用
- `admin-frontend` - 管理后台前端
- `admin-backend` - 管理后台后端

**版本格式**: `YYYYMMDD-HHMMSS` (例如: `20250608-151349`)

**使用示例**:
```bash
# 列出前端可用版本
./shell/version_manager.sh list frontend

# 下载后端指定版本到当前目录
./shell/version_manager.sh download backend 20250608-140315

# 回滚前端到指定版本
./shell/version_manager.sh deploy frontend 20250607-235959

# 下载管理后台前端到指定目录
./shell/version_manager.sh download admin-frontend 20250608-120000 ./downloads/
```

**部署流程**:
1. 自动下载指定版本
2. 停止现有服务 (后端组件)
3. 备份当前部署
4. 解压新版本到目标目录
5. 安装依赖 (后端组件)
6. 启动服务 (后端组件)
7. 清理临时文件

### 部署脚本详解

#### `deploy_app.sh` - 前端部署
- **功能**: 部署前端到Web服务器
- **目标目录**: `/var/www/html/`
- **要求**: 需要sudo权限

```bash
# 部署前端
./shell/deploy_app.sh

# 部署过程:
# 1. 下载 dist.tar.gz
# 2. 清理旧文件
# 3. 解压到Web目录
# 4. 清理临时文件
```

#### `deploy_backend.sh` - 后端部署
- **功能**: 部署后端到服务器
- **目标目录**: `/opt/smart-video-backend/`
- **服务端口**: `3002`
- **支持参数**:
  - `--help`: 显示帮助信息
  - `--force`: 强制部署，跳过确认

```bash
# 部署后端
./shell/deploy_backend.sh

# 强制部署
./shell/deploy_backend.sh --force

# 部署过程:
# 1. 检查系统要求
# 2. 停止现有服务
# 3. 备份现有部署
# 4. 下载后端包
# 5. 解压和部署
# 6. 安装依赖
# 7. 配置环境
# 8. 设置PM2/systemd服务
# 9. 启动服务
# 10. 验证部署
```

## ⚙️ 环境要求

### 构建环境
- **Node.js**: >= 16.0.0
- **npm**: >= 8.0.0
- **系统工具**: tar, gzip, curl/wget

### 部署环境

#### 前端部署
- **Web服务器**: Nginx/Apache
- **系统权限**: sudo权限
- **磁盘空间**: >= 100MB

#### 后端部署
- **Node.js**: >= 16.0.0
- **数据库**: MySQL
- **系统权限**: sudo权限
- **磁盘空间**: >= 500MB
- **内存**: >= 1GB
- **端口**: 3002可用

## 🔧 配置说明

### 云存储配置
编辑 `r2_upload.sh` 中的配置：
```bash
ACCOUNT_ID="your-account-id"
ACCESS_KEY="your-access-key"
SECRET_KEY="your-secret-key"
BUCKET_NAME="your-bucket-name"
CUSTOM_DOMAIN="https://your-domain.com"
```

### 后端环境配置
部署后需要配置 `.env` 文件：
```bash
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=hepburn
DB_USER=root
DB_PASSWORD=your-password

# 应用配置
NODE_ENV=production
PORT=3002
```

## 🚨 故障排除

### 常见问题

#### 构建失败
1. **Node.js版本过低**: 升级到16.0.0以上
2. **依赖安装失败**: 清理node_modules重新安装
3. **TypeScript编译错误**: 检查代码语法

#### 部署失败
1. **权限不足**: 确保有sudo权限
2. **端口被占用**: 检查端口3002是否被占用
3. **数据库连接失败**: 检查数据库配置

#### 服务启动失败
1. **检查日志**: `pm2 logs` 或 `journalctl -u smart-video-backend`
2. **检查配置**: 验证.env文件配置
3. **检查端口**: 确保端口3002可用

### 日志查看
```bash
# PM2日志
pm2 logs smart-video-backend

# systemd日志
sudo journalctl -u smart-video-backend -f

# 应用日志
tail -f /var/log/smart-video-backend/*.log
```

## 📞 技术支持

如遇到问题，请：
1. 检查系统要求是否满足
2. 查看相关日志文件
3. 确认网络连接正常
4. 验证配置文件正确性

## 🔄 更新流程

### 更新前端
```bash
./shell/build_app.sh
./shell/deploy_app.sh
```

### 更新后端
```bash
./shell/build_backend.sh
./shell/deploy_backend.sh
```

### 更新全栈
```bash
./shell/build_all.sh
# 然后分别部署前端和后端
```

---

**最后更新**: 2025年6月8日
**版本**: 2.0.0 (新增 tar.gz 格式和时间戳)
**维护者**: 赫本平台开发团队
