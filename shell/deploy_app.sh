#!/bin/bash

# 前端部署脚本 - 支持版本参数
# 用法: ./deploy_app.sh [version]
# 示例: ./deploy_app.sh 20250608-150227

VERSION="$1"

if [ -z "$VERSION" ]; then
    echo "错误: 请指定版本号"
    echo "用法: $0 <version>"
    echo "示例: $0 20250608-150227"
    echo ""
    echo "提示: 使用 ./shell/version_manager.sh list frontend 查看可用版本"
    exit 1
fi

FILENAME="frontend-${VERSION}.tar.gz"
DOWNLOAD_URL="https://storage.free-connect.sbs/frontend/${FILENAME}"

echo "下载前端版本: ${VERSION}"
echo "下载地址: ${DOWNLOAD_URL}"

wget "${DOWNLOAD_URL}"

if [ $? -eq 0 ]; then
    echo "部署前端版本: ${VERSION}"

    DEPLOY_DIR="/www/wwwroot/heben"

    # 检查部署目录是否存在
    if [ ! -d "$DEPLOY_DIR" ]; then
        echo "错误: 部署目录 $DEPLOY_DIR 不存在"
        exit 1
    fi

    # 备份当前部署 (如果存在文件)
    if [ "$(ls -A $DEPLOY_DIR 2>/dev/null)" ]; then
        BACKUP_DIR="${DEPLOY_DIR}.backup.$(date +%Y%m%d-%H%M%S)"
        echo "备份当前部署到: $BACKUP_DIR"
        sudo cp -r "$DEPLOY_DIR" "$BACKUP_DIR"
    fi

    # 清理旧文件并部署新版本
    echo "清理旧文件..."
    sudo rm -rf ${DEPLOY_DIR}/*

    echo "解压新版本到: $DEPLOY_DIR"
    sudo tar --no-xattrs -xzf "${FILENAME}" -C "$DEPLOY_DIR"

    # 设置正确的文件权限
    sudo chown -R www-data:www-data "$DEPLOY_DIR" 2>/dev/null || sudo chown -R nginx:nginx "$DEPLOY_DIR" 2>/dev/null || echo "警告: 无法设置文件所有者"
    sudo chmod -R 755 "$DEPLOY_DIR"

    # 清理下载的文件
    rm -f "${FILENAME}"

    echo "✅ 前端版本 ${VERSION} 部署完成！"
    echo "📁 部署目录: $DEPLOY_DIR"
    echo "🌐 访问地址: http://31.58.152.180"

    # 验证部署
    if [ -f "$DEPLOY_DIR/index.html" ]; then
        echo "✅ 部署验证成功: index.html 存在"
    else
        echo "❌ 部署验证失败: index.html 不存在"
        exit 1
    fi

else
    echo "错误: 下载失败，请检查版本号是否正确"
    exit 1
fi
