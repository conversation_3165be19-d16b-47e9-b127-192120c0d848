#!/bin/bash

# 赫本平台全栈构建脚本
# 功能：构建前端和后端应用，打包并上传到云存储

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

# 显示横幅
show_banner() {
    echo -e "${PURPLE}"
    echo "=================================================="
    echo "    赫本平台全栈构建脚本"
    echo "    Smart Video Platform Full Stack Builder"
    echo "=================================================="
    echo -e "${NC}"
}

# 检查项目结构
check_project_structure() {
    log_info "检查项目结构..."
    
    local missing_dirs=()
    
    if [ ! -d "frontend" ]; then
        missing_dirs+=("frontend")
    fi
    
    if [ ! -d "backend" ]; then
        missing_dirs+=("backend")
    fi
    
    if [ ! -d "shell" ]; then
        missing_dirs+=("shell")
    fi
    
    if [ ${#missing_dirs[@]} -ne 0 ]; then
        log_error "缺少必要目录: ${missing_dirs[*]}"
        log_error "请在项目根目录运行此脚本"
        exit 1
    fi
    
    # 检查构建脚本
    if [ ! -f "shell/build_app.sh" ]; then
        log_error "前端构建脚本不存在: shell/build_app.sh"
        exit 1
    fi
    
    if [ ! -f "shell/build_backend.sh" ]; then
        log_error "后端构建脚本不存在: shell/build_backend.sh"
        exit 1
    fi
    
    log_success "项目结构检查通过"
}

# 检查构建工具
check_build_tools() {
    log_info "检查构建工具..."
    
    local missing_tools=()
    
    if ! command -v node &> /dev/null; then
        missing_tools+=("node")
    fi
    
    if ! command -v npm &> /dev/null; then
        missing_tools+=("npm")
    fi
    
    if ! command -v tar &> /dev/null; then
        missing_tools+=("tar")
    fi
    
    if [ ${#missing_tools[@]} -ne 0 ]; then
        log_error "缺少必要工具: ${missing_tools[*]}"
        exit 1
    fi
    
    # 显示版本信息
    log_info "Node.js 版本: $(node --version)"
    log_info "npm 版本: $(npm --version)"
    
    log_success "构建工具检查通过"
}

# 清理旧的构建文件
cleanup_old_builds() {
    log_info "清理旧的构建文件..."
    
    # 清理前端构建文件
    if [ -d "frontend/dist" ]; then
        rm -rf frontend/dist
        log_info "清理前端 dist 目录"
    fi
    
    if [ -f "frontend/frontend-*.tar.gz" ]; then
        rm -f frontend/frontend-*.tar.gz
        log_info "清理前端压缩包"
    fi
    
    # 清理后端构建文件
    if [ -d "backend/dist" ]; then
        rm -rf backend/dist
        log_info "清理后端 dist 目录"
    fi
    
    if [ -d "backend/build" ]; then
        rm -rf backend/build
        log_info "清理后端 build 目录"
    fi
    
    # 清理根目录的构建文件
    rm -f *.zip *.tar.gz
    rm -f *-deploy-info.txt
    
    log_success "清理完成"
}

# 构建前端
build_frontend() {
    log_step "开始构建前端..."
    
    # 检查前端构建脚本权限
    chmod +x shell/build_app.sh
    
    # 执行前端构建
    if ./shell/build_app.sh; then
        log_success "前端构建完成"
    else
        log_error "前端构建失败"
        exit 1
    fi
}

# 构建后端
build_backend() {
    log_step "开始构建后端..."
    
    # 检查后端构建脚本权限
    chmod +x shell/build_backend.sh
    
    # 执行后端构建
    if ./shell/build_backend.sh; then
        log_success "后端构建完成"
    else
        log_error "后端构建失败"
        exit 1
    fi
}

# 生成部署包信息
generate_deployment_info() {
    log_info "生成部署包信息..."
    
    local timestamp=$(date)
    
    cat > deployment-info.txt << EOF
赫本平台部署包信息
====================

构建时间: $timestamp
构建机器: $(hostname)
操作系统: $(uname -s) $(uname -r)
Node.js版本: $(node --version)
npm版本: $(npm --version)

下载地址:
- 前端包: 查看构建日志获取具体文件名
- 后端包: 查看构建日志获取具体文件名

部署步骤:

1. 前端部署:
   # 使用具体的时间戳文件名
   wget https://storage.free-connect.sbs/frontend-YYYYMMDD-HHMMSS.tar.gz
   rm -rf /var/www/html/* && tar -xzf frontend-*.tar.gz -C /var/www/html/ && rm -f frontend-*.tar.gz

2. 后端部署:
   # 使用具体的时间戳文件名
   wget https://storage.free-connect.sbs/backend-YYYYMMDD-HHMMSS.tar.gz
   # 详细步骤请参考 backend-deploy-info.txt

快速部署命令:
- 前端: curl -sSL https://raw.githubusercontent.com/your-repo/main/shell/deploy_app.sh | bash
- 后端: curl -sSL https://raw.githubusercontent.com/your-repo/main/shell/deploy_backend.sh | bash

注意事项:
1. 确保服务器已安装 Node.js >= 16.0.0
2. 确保 MySQL 数据库已配置
3. 检查防火墙设置，开放必要端口
4. 建议使用 PM2 进行进程管理

技术栈:
- 前端: React 19.1.0 + TypeScript + Vite
- 后端: Node.js + TypeScript + Koa + Sequelize
- 数据库: MySQL
- 缓存: IndexedDB (前端)
- 视频播放: HLS.js

联系信息:
如有问题请检查日志文件或联系技术支持
EOF
    
    log_success "部署信息已生成: deployment-info.txt"
}

# 显示构建摘要
show_build_summary() {
    log_step "构建摘要"

    echo -e "${GREEN}=================================="
    echo "构建完成摘要"
    echo "=================================="
    echo -e "${NC}"

    echo -e "✅ 前端包: ${GREEN}构建完成${NC}"
    echo -e "✅ 后端包: ${GREEN}构建完成${NC}"

    echo ""
    echo -e "${YELLOW}📋 部署命令 (复制下面的命令直接执行):${NC}"
    echo -e "${GREEN}==================================================${NC}"

    # 尝试从构建日志中提取版本信息
    if [ -f "frontend-deploy-info.txt" ]; then
        FRONTEND_VERSION=$(grep -o "frontend-[0-9]\{8\}-[0-9]\{6\}" frontend-deploy-info.txt | head -1 | sed 's/frontend-//')
        if [ ! -z "$FRONTEND_VERSION" ]; then
            echo -e "${GREEN}# 部署前端${NC}"
            echo -e "${GREEN}./shell/deploy_app.sh ${FRONTEND_VERSION}${NC}"
            echo ""
        fi
    fi

    if [ -f "backend-deploy-info.txt" ]; then
        BACKEND_VERSION=$(grep -o "backend-[0-9]\{8\}-[0-9]\{6\}" backend-deploy-info.txt | head -1 | sed 's/backend-//')
        if [ ! -z "$BACKEND_VERSION" ]; then
            echo -e "${GREEN}# 部署后端${NC}"
            echo -e "${GREEN}./shell/deploy_backend.sh ${BACKEND_VERSION}${NC}"
            echo ""
        fi
    fi

    # 如果无法提取版本，显示通用提示
    if [ -z "$FRONTEND_VERSION" ] && [ -z "$BACKEND_VERSION" ]; then
        echo -e "${YELLOW}# 请查看上面的构建日志获取具体版本号${NC}"
        echo -e "${YELLOW}# 格式: ./shell/deploy_app.sh YYYYMMDD-HHMMSS${NC}"
        echo -e "${YELLOW}# 格式: ./shell/deploy_backend.sh YYYYMMDD-HHMMSS${NC}"
    fi

    echo -e "${GREEN}==================================================${NC}"
    echo ""
    echo -e "${BLUE}💡 提示: 复制上面的命令到服务器执行即可完成部署${NC}"
    echo ""
    echo -e "${BLUE}📋 文档:${NC}"
    echo "- deployment-info.txt (总体部署信息)"
    echo "- backend-deploy-info.txt (后端详细信息)"
    echo ""
    echo -e "${GREEN}构建完成时间: $(date)${NC}"
}

# 主函数
main() {
    show_banner
    
    log_info "开始全栈构建..."
    
    # 检查环境
    check_project_structure
    check_build_tools
    
    # 清理和构建
    cleanup_old_builds
    
    # 并行构建（如果支持）
    if command -v parallel &> /dev/null; then
        log_info "使用并行构建..."
        parallel -j2 ::: build_frontend build_backend
    else
        # 串行构建
        build_frontend
        build_backend
    fi
    
    # 生成信息和摘要
    generate_deployment_info
    show_build_summary
    
    log_success "全栈构建完成！"
}

# 处理命令行参数
case "${1:-}" in
    --help|-h)
        echo "赫本平台全栈构建脚本"
        echo ""
        echo "用法: $0 [选项]"
        echo ""
        echo "选项:"
        echo "  --help, -h        显示此帮助信息"
        echo "  --frontend-only   仅构建前端"
        echo "  --backend-only    仅构建后端"
        echo "  --clean           仅清理构建文件"
        echo ""
        echo "示例:"
        echo "  $0                构建前端和后端"
        echo "  $0 --frontend-only 仅构建前端"
        echo "  $0 --backend-only  仅构建后端"
        echo "  $0 --clean         清理构建文件"
        echo ""
        exit 0
        ;;
    --frontend-only)
        show_banner
        check_project_structure
        check_build_tools
        cleanup_old_builds
        build_frontend
        log_success "前端构建完成！"
        exit 0
        ;;
    --backend-only)
        show_banner
        check_project_structure
        check_build_tools
        cleanup_old_builds
        build_backend
        log_success "后端构建完成！"
        exit 0
        ;;
    --clean)
        show_banner
        cleanup_old_builds
        log_success "清理完成！"
        exit 0
        ;;
esac

# 执行主函数
main "$@"
