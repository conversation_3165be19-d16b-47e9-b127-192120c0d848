#!/bin/bash

# 赫本平台管理后台构建脚本
# 功能：构建管理后台前端和后端，打包并上传到云存储

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查项目结构
check_project_structure() {
    log_info "检查管理后台项目结构..."
    
    if [ ! -d "admin-system" ]; then
        log_error "admin-system 目录不存在，请在项目根目录运行此脚本"
        exit 1
    fi
    
    if [ ! -d "admin-system/frontend" ]; then
        log_error "admin-system/frontend 目录不存在"
        exit 1
    fi
    
    if [ ! -d "admin-system/backend" ]; then
        log_error "admin-system/backend 目录不存在"
        exit 1
    fi
    
    log_success "项目结构检查通过"
}

# 构建管理后台
build_admin_system() {
    log_info "构建管理后台..."
    
    cd admin-system
    
    # 构建管理后台
    npm run build
    
    cd ..
    
    log_success "管理后台构建完成"
}

# 打包管理后台
package_admin_system() {
    log_info "打包管理后台..."
    
    # 生成时间戳
    TIMESTAMP=$(date +"%Y%m%d-%H%M%S")
    
    # 打包前端
    FRONTEND_PACKAGE="admin-frontend-${TIMESTAMP}.tar.gz"
    cd admin-system/frontend
    tar --no-xattrs -czf "../../${FRONTEND_PACKAGE}" -C dist .
    cd ../..

    # 打包后端
    BACKEND_PACKAGE="admin-backend-${TIMESTAMP}.tar.gz"
    cd admin-system/backend
    tar --no-xattrs -czf "../../${BACKEND_PACKAGE}" -C dist .
    cd ../..
    
    log_success "管理后台打包完成"
    log_info "前端包: ${FRONTEND_PACKAGE}"
    log_info "后端包: ${BACKEND_PACKAGE}"
    
    # 保存包名到环境变量
    export ADMIN_FRONTEND_PACKAGE="${FRONTEND_PACKAGE}"
    export ADMIN_BACKEND_PACKAGE="${BACKEND_PACKAGE}"
}

# 上传到云存储
upload_admin_packages() {
    log_info "上传管理后台包到云存储..."
    
    if [ ! -f "./shell/r2_upload.sh" ]; then
        log_error "r2_upload.sh 脚本不存在"
        exit 1
    fi
    
    # 上传前端包
    ./shell/r2_upload.sh "${ADMIN_FRONTEND_PACKAGE}" "${ADMIN_FRONTEND_PACKAGE}"

    # 上传后端包
    ./shell/r2_upload.sh "${ADMIN_BACKEND_PACKAGE}" "${ADMIN_BACKEND_PACKAGE}"
    
    if [ $? -eq 0 ]; then
        log_success "管理后台包上传成功"
        
        # 清理本地包文件
        rm -f "${ADMIN_FRONTEND_PACKAGE}"
        rm -f "${ADMIN_BACKEND_PACKAGE}"
        log_info "清理本地包文件"
    else
        log_error "管理后台包上传失败"
        exit 1
    fi
}

# 生成部署信息
generate_admin_deploy_info() {
    log_info "生成管理后台部署信息..."
    
    cat > admin-deploy-info.txt << EOF
赫本平台管理后台部署信息
==========================

构建时间: $(date)
前端包名: ${ADMIN_FRONTEND_PACKAGE}
后端包名: ${ADMIN_BACKEND_PACKAGE}

下载地址:
- 前端包: https://storage.free-connect.sbs/${ADMIN_FRONTEND_PACKAGE}
- 后端包: https://storage.free-connect.sbs/${ADMIN_BACKEND_PACKAGE}

部署步骤:

1. 前端部署:
   wget https://storage.free-connect.sbs/${ADMIN_FRONTEND_PACKAGE}
   rm -rf /var/www/admin/* && tar -xzf ${ADMIN_FRONTEND_PACKAGE} -C /var/www/admin/ && rm -f ${ADMIN_FRONTEND_PACKAGE}

2. 后端部署:
   wget https://storage.free-connect.sbs/${ADMIN_BACKEND_PACKAGE}
   mkdir -p /opt/admin-backend && tar -xzf ${ADMIN_BACKEND_PACKAGE} -C /opt/admin-backend/
   cd /opt/admin-backend && npm ci --production && pm2 start index.js --name admin-backend

环境要求:
- Node.js >= 16.0.0
- npm >= 8.0.0
- MySQL 数据库
- 前端端口: 3001
- 后端端口: 3004

配置文件:
- 后端需要配置数据库连接信息
- 前端需要配置API地址

注意事项:
- 管理后台需要连接到主项目的数据库
- 确保防火墙开放相应端口
- 建议使用 PM2 进行进程管理
EOF
    
    log_success "管理后台部署信息已生成: admin-deploy-info.txt"
}

# 主函数
main() {
    log_info "开始构建赫本平台管理后台..."
    
    check_project_structure
    build_admin_system
    package_admin_system
    upload_admin_packages
    generate_admin_deploy_info
    
    log_success "管理后台构建完成！"
    log_info "部署信息请查看: admin-deploy-info.txt"
    log_info "前端下载地址: https://storage.free-connect.sbs/${ADMIN_FRONTEND_PACKAGE}"
    log_info "后端下载地址: https://storage.free-connect.sbs/${ADMIN_BACKEND_PACKAGE}"

    # 提取时间戳用于部署命令
    FRONTEND_VERSION=$(echo "${ADMIN_FRONTEND_PACKAGE}" | sed 's/admin-frontend-\(.*\)\.tar\.gz/\1/')
    BACKEND_VERSION=$(echo "${ADMIN_BACKEND_PACKAGE}" | sed 's/admin-backend-\(.*\)\.tar\.gz/\1/')

    echo ""
    echo -e "${GREEN}🎉 管理后台构建完成！${NC}"
    echo -e "${BLUE}📦 前端包: ${ADMIN_FRONTEND_PACKAGE}${NC}"
    echo -e "${BLUE}📦 后端包: ${ADMIN_BACKEND_PACKAGE}${NC}"
    echo ""
    echo -e "${YELLOW}📋 部署命令 (复制下面的命令直接执行):${NC}"
    echo -e "${GREEN}==================================================${NC}"
    echo -e "${GREEN}# 部署管理后台前端${NC}"
    echo -e "${GREEN}./shell/version_manager.sh deploy admin-frontend ${FRONTEND_VERSION}${NC}"
    echo ""
    echo -e "${GREEN}# 部署管理后台后端${NC}"
    echo -e "${GREEN}./shell/version_manager.sh deploy admin-backend ${BACKEND_VERSION}${NC}"
    echo -e "${GREEN}==================================================${NC}"
    echo ""
    echo -e "${BLUE}💡 提示: 复制上面的命令到服务器执行即可完成部署${NC}"
}

# 执行主函数
main "$@"
