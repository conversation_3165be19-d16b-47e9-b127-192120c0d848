# 💳 充值系统完整部署指南

## 🎯 系统概述

完整的第三方充值系统，支持简化的一键充值和传统的分步充值两种模式，移除了对用户联系方式的依赖，直接使用用户账号ID进行充值。

## 🏗️ 系统架构

### 前端架构
```
充值页面 (RechargePage.tsx)
    ↓
统一API服务 (unifiedApiService.ts)
    ↓
缓存API服务 (cachedApiService.ts)
    ↓
后端API接口
```

### 后端架构
```
路由层 (thirdPartyRecharge.ts)
    ↓
服务层 (ThirdPartyRechargeService.ts)
    ↓
支付服务 (PaymentService.ts)
    ↓
数据模型 (ThirdPartySku, ThirdPartyOrder, RechargeRecord)
```

## 🚀 部署步骤

### 1. 环境配置

#### 后端环境变量 (.env.production)
```bash
# 第三方支付配置
THIRD_PARTY_PROJECT_ID=67cf0c9cacfcf1db5e546914
THIRD_PARTY_SECRET=sk_62513e688e98d9bd4c5f6171b323a6d0
THIRD_PARTY_CALLBACK_URL=https://yourdomain.com/api/third-party-recharge/callback
THIRD_PARTY_REDIRECT_URL=https://yourdomain.com/orders
```

#### 前端环境变量
```bash
# API基础URL
REACT_APP_API_BASE_URL=https://yourdomain.com/api
```

### 2. 数据库初始化

#### 初始化充值套餐数据
```bash
cd backend
npm run init-skus init
```

#### 查看创建的套餐
```bash
npm run init-skus list
```

#### 管理套餐状态
```bash
# 激活套餐
npm run init-skus activate sku_coins_100

# 停用套餐
npm run init-skus deactivate sku_coins_100

# 清理所有套餐（谨慎使用）
npm run init-skus clear
```

### 3. 功能测试

#### 测试环境变量
```bash
npm run test-recharge env
```

#### 测试SKU验证
```bash
npm run test-recharge sku
```

#### 测试完整充值流程
```bash
npm run test-recharge flow
```

#### 运行所有测试
```bash
npm run test-recharge all
```

## 📋 API接口文档

### 1. 获取充值套餐列表
```http
POST /api/third-party-recharge/skus
Authorization: Bearer <token>
```

**响应示例:**
```json
{
  "success": true,
  "data": [
    {
      "id": "1",
      "skuid": "sku_coins_100",
      "name": "100金币",
      "description": "充值100金币，观看更多精彩视频",
      "price": 10.00,
      "coins": 100,
      "bonus": 0,
      "popular": false
    }
  ]
}
```

### 2. 一键创建订单并支付（推荐）
```http
POST /api/third-party-recharge/create-and-pay
Authorization: Bearer <token>
Content-Type: application/json

{
  "skuId": "sku_coins_100"
}
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "orderId": "order_123456",
    "payUrl": "https://pay.example.com/pay?id=123456",
    "amount": 10.00,
    "coins": 100
  }
}
```

### 3. 分离式API（兼容旧版本）

#### 3.1 创建订单
```http
POST /api/third-party-recharge/create-order-only
Authorization: Bearer <token>
Content-Type: application/json

{
  "skuId": "sku_coins_100"
}
```

#### 3.2 发起支付
```http
POST /api/third-party-recharge/pay-order-only
Authorization: Bearer <token>
Content-Type: application/json

{
  "orderId": "order_123456"
}
```

#### 3.3 获取订单信息
```http
POST /api/third-party-recharge/order-info
Authorization: Bearer <token>
Content-Type: application/json

{
  "orderId": "order_123456"
}
```

### 4. 支付回调处理
```http
POST /api/third-party-recharge/callback
Content-Type: application/json

{
  "orderId": "order_123456",
  "status": "DONE",
  "amount": 10.00
}
```

## 🔧 前端使用示例

### 1. 简化充值流程
```typescript
import { unifiedApiService } from '../services/unifiedApiService';

const handleRecharge = async (skuId: string) => {
  try {
    // 一键创建订单并支付
    const result = await unifiedApiService.createAndPayRechargeOrder(skuId);
    
    // 打开支付页面
    window.open(result.payUrl, '_blank');
    
    // 开始轮询支付状态
    pollPaymentStatus(result.orderId);
  } catch (error) {
    console.error('充值失败:', error);
  }
};
```

### 2. 传统分步流程
```typescript
const handleTraditionalRecharge = async (skuId: string) => {
  try {
    // 1. 创建订单
    const orderResult = await unifiedApiService.createThirdPartyOrder(skuId);
    
    // 2. 发起支付
    const payResult = await unifiedApiService.payThirdPartyOrder(orderResult.orderId);
    
    // 3. 打开支付页面
    window.open(payResult.payUrl, '_blank');
  } catch (error) {
    console.error('充值失败:', error);
  }
};
```

### 3. 支付状态轮询
```typescript
const pollPaymentStatus = async (orderId: string) => {
  const maxAttempts = 60; // 最多轮询60次（5分钟）
  let attempts = 0;
  
  const checkStatus = async (): Promise<boolean> => {
    try {
      const orderInfo = await unifiedApiService.getThirdPartyOrderInfo(orderId);
      return orderInfo.status === 'DONE';
    } catch (error) {
      return false;
    }
  };
  
  const poll = async () => {
    if (attempts >= maxAttempts) {
      toast.error('支付超时，请检查支付状态');
      return;
    }
    
    const isPaid = await checkStatus();
    if (isPaid) {
      toast.success('支付成功！');
      // 刷新用户信息
      await updateUser();
    } else {
      attempts++;
      setTimeout(poll, 5000); // 5秒后再次检查
    }
  };
  
  poll();
};
```

## 🧪 测试功能

### 1. 前端测试页面
访问 `/test/recharge` 页面进行功能测试：
- 获取SKU列表
- 创建订单
- 发起支付
- 获取订单信息
- 一键创建并支付（新功能）

### 2. 后端测试脚本
```bash
# 测试完整流程
npm run test-recharge all

# 单独测试各个功能
npm run test-recharge env    # 环境变量
npm run test-recharge sku    # SKU验证
npm run test-recharge flow   # 充值流程
```

## 🔒 安全考虑

### 1. 用户验证
- 所有API都需要有效的JWT token
- 用户ID通过token自动获取，无法伪造
- 订单与用户强绑定

### 2. 订单安全
- 30分钟内相同SKU的订单可重用，防止重复创建
- 支付回调验证订单状态
- 数据库事务确保数据一致性

### 3. 参数验证
- 严格验证SKU ID的有效性
- 检查SKU状态（active/inactive）
- 验证支付方式的合法性

## 📊 监控和日志

### 1. 关键日志
- 订单创建日志
- 支付发起日志
- 支付回调日志
- 充值成功日志
- 错误和异常日志

### 2. 监控指标
- 订单创建成功率
- 支付成功率
- 平均支付时间
- 用户充值频率
- 系统错误率

## 🚨 故障排除

### 1. 常见问题

#### 问题：按钮点击无反应
**解决方案：**
- 检查用户是否已登录
- 确认已选择充值套餐
- 查看浏览器控制台错误信息

#### 问题：API调用失败
**解决方案：**
- 检查网络连接
- 验证API服务器状态
- 确认环境变量配置正确

#### 问题：支付页面无法打开
**解决方案：**
- 检查第三方支付服务状态
- 验证项目ID和密钥配置
- 确认回调URL可访问

### 2. 调试工具
- 前端测试页面：`/test/recharge`
- 后端测试脚本：`npm run test-recharge`
- 日志查看：检查服务器日志文件

## 🔄 版本升级

### 从旧版本升级
1. 更新代码到最新版本
2. 运行数据库迁移（如需要）
3. 初始化SKU数据：`npm run init-skus init`
4. 测试新功能：`npm run test-recharge all`
5. 更新前端缓存配置

### 向后兼容性
- 保留所有旧的API端点
- 新增的简化API不影响现有功能
- 渐进式部署，可以逐步切换到新API

---

**🎉 充值系统部署完成！用户现在可以享受简化的一键充值体验！**
