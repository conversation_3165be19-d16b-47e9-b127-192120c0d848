# 📜 滚动位置管理系统使用指南

## 🎯 问题解决

**问题**：在单页应用中，用户向下滚动页面后跳转到另一个页面，新页面的滚动位置还停留在原处，而不是从顶部开始。

**解决方案**：实现了一套完整的滚动位置管理系统，自动处理路由跳转时的滚动重置，同时提供灵活的配置选项。

## 🚀 核心功能

### ✅ 自动滚动重置
- 路由跳转时自动重置滚动位置到顶部
- 支持延迟重置，确保页面内容已渲染
- 可配置特定路径保持滚动位置

### ✅ 智能滚动管理
- 支持滚动位置记忆和恢复
- 提供手动滚动控制方法
- 支持容器级别的滚动管理

### ✅ 用户体验增强
- 回到顶部按钮（可配置显示阈值）
- 滚动进度条显示
- 平滑滚动动画

## 🛠️ 组件和Hook

### 1. ScrollRestoration 组件

全局滚动恢复组件，自动处理路由跳转时的滚动重置：

```tsx
import ScrollRestoration from '../components/ScrollRestoration';

// 在App.tsx中使用
<ScrollRestoration 
  preserveScrollPaths={['/shorts']} // 短视频页面保持滚动位置
  delay={50} // 稍微延迟重置，确保页面内容已渲染
/>
```

### 2. useScrollReset Hook

基础滚动重置Hook：

```tsx
import { useScrollReset } from '../hooks/useScrollReset';

const { resetScroll } = useScrollReset({
  enabled: true,
  delay: 0,
  preserveScrollPaths: ['/shorts'],
  behavior: 'auto'
});

// 手动重置滚动
resetScroll();
```

### 3. useScrollPosition Hook

滚动位置记忆Hook：

```tsx
import { useScrollPosition } from '../hooks/useScrollReset';

const {
  saveScrollPosition,
  restoreScrollPosition,
  clearScrollPosition,
  getCurrentScrollPosition
} = useScrollPosition('unique-key');
```

### 4. useSmartScroll Hook

智能滚动管理Hook，结合重置和记忆功能：

```tsx
import { useSmartScroll } from '../hooks/useScrollReset';

const smartScroll = useSmartScroll({
  resetOnRouteChange: true,
  rememberPositions: true,
  preserveScrollPaths: ['/shorts'],
  positionKey: 'custom-key'
});
```

## 🎨 UI组件

### 1. ScrollToTopButton 回到顶部按钮

```tsx
import { ScrollToTopButton } from '../components/ScrollRestoration';

<ScrollToTopButton 
  threshold={300} // 滚动超过300px后显示
  className="custom-class"
>
  <CustomIcon />
</ScrollToTopButton>
```

### 2. ScrollProgressBar 滚动进度条

```tsx
import { ScrollProgressBar } from '../components/ScrollRestoration';

<ScrollProgressBar 
  color="#ec4899" 
  height={3}
  className="custom-class"
/>
```

### 3. ScrollContainer 智能滚动容器

```tsx
import { ScrollContainer } from '../components/ScrollRestoration';

<ScrollContainer
  resetOnRouteChange={true}
  rememberPosition={true}
  positionKey="container-key"
  className="h-40 overflow-auto"
>
  <div>容器内容</div>
</ScrollContainer>
```

## ⚙️ 配置选项

### ScrollRestoration 配置

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `enabled` | boolean | true | 是否启用滚动重置 |
| `preserveScrollPaths` | string[] | [] | 保持滚动位置的路径列表 |
| `delay` | number | 0 | 重置延迟时间（毫秒） |
| `behavior` | ScrollBehavior | 'auto' | 滚动行为 |
| `onlyOnPathnameChange` | boolean | false | 仅在路径名变化时重置 |

### useScrollReset 配置

```tsx
const options = {
  enabled: true,                    // 启用滚动重置
  delay: 50,                       // 延迟50ms重置
  preserveScrollPaths: ['/shorts'], // 短视频页面保持位置
  behavior: 'smooth',              // 平滑滚动
  onlyOnPathnameChange: false      // 查询参数变化也重置
};
```

## 📱 实际应用

### 1. 全局配置（App.tsx）

```tsx
import ScrollRestoration from './components/ScrollRestoration';

function App() {
  return (
    <Router>
      <ScrollRestoration 
        preserveScrollPaths={['/shorts']} 
        delay={50} 
      />
      <Routes>
        {/* 路由配置 */}
      </Routes>
    </Router>
  );
}
```

### 2. 页面级使用

```tsx
import { ScrollToTopButton } from '../components/ScrollRestoration';

const HomePage: React.FC = () => {
  return (
    <div>
      {/* 页面内容 */}
      
      {/* 回到顶部按钮 */}
      <ScrollToTopButton threshold={200} />
    </div>
  );
};
```

### 3. 列表页面优化

```tsx
import { ScrollToTopButton, ScrollProgressBar } from '../components/ScrollRestoration';

const VideoListPage: React.FC = () => {
  return (
    <div>
      {/* 滚动进度条 */}
      <ScrollProgressBar />
      
      {/* 列表内容 */}
      <div className="video-list">
        {/* 视频列表 */}
      </div>
      
      {/* 回到顶部按钮 */}
      <ScrollToTopButton threshold={300} />
    </div>
  );
};
```

## 🔧 高级用法

### 1. 自定义滚动行为

```tsx
const CustomScrollComponent: React.FC = () => {
  const { resetScroll } = useScrollReset({
    enabled: false, // 禁用自动重置
    behavior: 'smooth'
  });

  const handleCustomReset = () => {
    // 自定义重置逻辑
    setTimeout(() => {
      resetScroll();
    }, 100);
  };

  return (
    <button onClick={handleCustomReset}>
      自定义重置
    </button>
  );
};
```

### 2. 条件性滚动管理

```tsx
const ConditionalScrollPage: React.FC = () => {
  const [shouldReset, setShouldReset] = useState(true);

  useScrollReset({
    enabled: shouldReset,
    preserveScrollPaths: shouldReset ? [] : ['/current-path']
  });

  return (
    <div>
      <button onClick={() => setShouldReset(!shouldReset)}>
        切换滚动重置: {shouldReset ? '开启' : '关闭'}
      </button>
    </div>
  );
};
```

### 3. 滚动位置同步

```tsx
const SyncScrollPage: React.FC = () => {
  const { saveScrollPosition, restoreScrollPosition } = useScrollPosition('sync-key');

  useEffect(() => {
    // 页面卸载时保存位置
    return () => {
      saveScrollPosition();
    };
  }, [saveScrollPosition]);

  useEffect(() => {
    // 页面加载时恢复位置
    const timer = setTimeout(() => {
      restoreScrollPosition();
    }, 100);

    return () => clearTimeout(timer);
  }, [restoreScrollPosition]);

  return <div>内容</div>;
};
```

## 🧪 测试页面

访问以下测试页面验证功能：

- `/test/scroll` - 滚动功能测试页面
- `/test/cache` - 缓存功能测试页面

### 测试步骤

1. **基础滚动重置测试**：
   - 在测试页面向下滚动
   - 点击跳转按钮到其他页面
   - 验证新页面是否从顶部开始

2. **保持滚动位置测试**：
   - 跳转到短视频页面（/shorts）
   - 向下滚动
   - 跳转到其他页面再返回
   - 验证滚动位置是否保持

3. **回到顶部按钮测试**：
   - 向下滚动超过阈值
   - 验证按钮是否显示
   - 点击按钮验证是否平滑滚动到顶部

## 📈 性能优化

### 1. 事件监听优化

```tsx
// 使用 passive 监听器
window.addEventListener('scroll', handleScroll, { passive: true });
```

### 2. 防抖处理

```tsx
const debouncedSave = useMemo(
  () => debounce(saveScrollPosition, 100),
  [saveScrollPosition]
);
```

### 3. 内存管理

```tsx
useEffect(() => {
  return () => {
    // 清理滚动位置缓存
    clearScrollPosition();
  };
}, [clearScrollPosition]);
```

## 🚨 注意事项

1. **延迟设置**：设置适当的延迟确保页面内容已渲染
2. **路径配置**：正确配置需要保持滚动位置的路径
3. **性能考虑**：避免过于频繁的滚动位置保存
4. **浏览器兼容性**：确保滚动API在目标浏览器中可用

---

**🎯 通过完善的滚动位置管理系统，为用户提供更自然、更流畅的页面导航体验！**
