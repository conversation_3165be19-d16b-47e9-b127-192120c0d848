#!/bin/bash

# SmartVideoWeb 全项目一键启动脚本
# 作者: SmartVideoWeb Team
# 描述: 同时启动主项目和管理系统的所有服务

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    echo -e "${2}${1}${NC}"
}

# 检查命令是否存在
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# 检查端口是否被占用
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        return 0  # 端口被占用
    else
        return 1  # 端口空闲
    fi
}

# 杀死占用端口的进程
kill_port() {
    local port=$1
    local pids=$(lsof -ti:$port)
    if [ ! -z "$pids" ]; then
        print_message "正在杀死占用端口 $port 的进程..." $YELLOW
        echo $pids | xargs kill -9
        sleep 2
    fi
}

# 检查并处理端口占用
check_and_handle_ports() {
    local ports=("3002" "5173" "3003" "3001")
    local names=("主项目后端" "主项目前端" "管理系统后端" "管理系统前端")
    
    for i in "${!ports[@]}"; do
        local port=${ports[$i]}
        local name=${names[$i]}
        
        if check_port $port; then
            print_message "⚠️  警告: 端口 $port ($name) 已被占用" $YELLOW
            read -p "是否要杀死占用进程并继续? (y/N): " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                kill_port $port
            else
                print_message "❌ 启动已取消" $RED
                exit 1
            fi
        fi
    done
}

# 检查依赖安装
check_dependencies() {
    print_message "📦 检查依赖安装状态..." $BLUE
    
    # 检查根目录依赖
    if [ ! -d "node_modules" ]; then
        print_message "正在安装根目录依赖..." $BLUE
        npm install
    fi
    
    # 检查主项目依赖
    if [ ! -d "backend/node_modules" ]; then
        print_message "正在安装主项目后端依赖..." $BLUE
        cd backend && npm install && cd ..
    fi
    
    if [ ! -d "frontend/node_modules" ]; then
        print_message "正在安装主项目前端依赖..." $BLUE
        cd frontend && npm install && cd ..
    fi
    
    # 检查管理系统依赖
    if [ ! -d "admin-system/node_modules" ]; then
        print_message "正在安装管理系统根目录依赖..." $BLUE
        cd admin-system && npm install && cd ..
    fi
    
    if [ ! -d "admin-system/backend/node_modules" ]; then
        print_message "正在安装管理系统后端依赖..." $BLUE
        cd admin-system/backend && npm install && cd ../..
    fi
    
    if [ ! -d "admin-system/frontend/node_modules" ]; then
        print_message "正在安装管理系统前端依赖..." $BLUE
        cd admin-system/frontend && npm install && cd ../..
    fi
}

# 主函数
main() {
    print_message "🚀 SmartVideoWeb 全项目一键启动脚本" $CYAN
    print_message "========================================" $CYAN
    
    # 检查 Node.js
    if ! command_exists node; then
        print_message "❌ 错误: 未找到 Node.js，请先安装 Node.js" $RED
        exit 1
    fi
    
    # 检查 npm
    if ! command_exists npm; then
        print_message "❌ 错误: 未找到 npm，请先安装 npm" $RED
        exit 1
    fi
    
    print_message "✅ Node.js 版本: $(node --version)" $GREEN
    print_message "✅ npm 版本: $(npm --version)" $GREEN
    echo
    
    # 检查是否在项目根目录
    if [ ! -f "package.json" ] || [ ! -d "backend" ] || [ ! -d "frontend" ] || [ ! -d "admin-system" ]; then
        print_message "❌ 错误: 请在项目根目录运行此脚本" $RED
        exit 1
    fi
    
    # 检查端口占用
    check_and_handle_ports
    
    # 检查依赖
    check_dependencies
    
    print_message "🎯 正在启动所有服务..." $PURPLE
    print_message "主项目后端服务器: http://localhost:3002" $GREEN
    print_message "主项目前端服务器: http://localhost:5173" $GREEN
    print_message "管理系统后端服务器: http://localhost:3003" $GREEN
    print_message "管理系统前端服务器: http://localhost:3001" $GREEN
    echo
    print_message "按 Ctrl+C 停止所有服务器" $YELLOW
    echo
    
    # 启动所有服务器
    npm run dev:all
}

# 捕获 Ctrl+C 信号
trap 'print_message "\n🛑 正在停止所有服务器..." $YELLOW; kill 0; exit 0' INT

# 运行主函数
main "$@"
