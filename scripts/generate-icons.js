#!/usr/bin/env node

/**
 * 图标生成脚本
 * 用于生成iOS WebClip和PWA所需的各种尺寸图标
 */

const fs = require('fs');
const path = require('path');

// 图标尺寸配置
const iconSizes = {
  // Favicon
  'favicon-16x16.png': 16,
  'favicon-32x32.png': 32,
  
  // Apple Touch Icons
  'apple-touch-icon.png': 180, // 默认尺寸
  'apple-touch-icon-57x57.png': 57,
  'apple-touch-icon-60x60.png': 60,
  'apple-touch-icon-72x72.png': 72,
  'apple-touch-icon-76x76.png': 76,
  'apple-touch-icon-114x114.png': 114,
  'apple-touch-icon-120x120.png': 120,
  'apple-touch-icon-144x144.png': 144,
  'apple-touch-icon-152x152.png': 152,
  'apple-touch-icon-180x180.png': 180,
  
  // Android Chrome Icons
  'android-chrome-36x36.png': 36,
  'android-chrome-48x48.png': 48,
  'android-chrome-72x72.png': 72,
  'android-chrome-96x96.png': 96,
  'android-chrome-144x144.png': 144,
  'android-chrome-192x192.png': 192,
  'android-chrome-256x256.png': 256,
  'android-chrome-384x384.png': 384,
  'android-chrome-512x512.png': 512,
  
  // Microsoft Tiles
  'ms-icon-144x144.png': 144,
  
  // Shortcuts
  'shortcut-home.png': 96,
  'shortcut-shorts.png': 96,
  'shortcut-profile.png': 96,
  'shortcut-search.png': 96,
  
  // Social Media
  'og-image.png': 1200, // 1200x630 for Open Graph
  'twitter-image.png': 1200 // 1200x600 for Twitter Card
};

// 启动屏幕尺寸配置
const launchScreens = {
  'launch-640x1136.png': { width: 640, height: 1136 }, // iPhone 5/5s/5c/SE
  'launch-750x1334.png': { width: 750, height: 1334 }, // iPhone 6/6s/7/8
  'launch-1242x2208.png': { width: 1242, height: 2208 }, // iPhone 6+/6s+/7+/8+
  'launch-1125x2436.png': { width: 1125, height: 2436 }, // iPhone X/XS/11 Pro
  'launch-1242x2688.png': { width: 1242, height: 2688 }, // iPhone XS Max/11 Pro Max
  'launch-828x1792.png': { width: 828, height: 1792 }, // iPhone XR/11
  'launch-1284x2778.png': { width: 1284, height: 2778 }, // iPhone 12/13/14 Pro Max
  'launch-1170x2532.png': { width: 1170, height: 2532 } // iPhone 12/13/14 Pro
};

// 创建目录
function ensureDir(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
    console.log(`📁 创建目录: ${dirPath}`);
  }
}

// 生成SVG图标
function generateSVGIcon(size, filename) {
  const svg = `<?xml version="1.0" encoding="UTF-8"?>
<svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#6366F1;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景圆角矩形 -->
  <rect width="${size}" height="${size}" rx="${size * 0.2}" ry="${size * 0.2}" fill="url(#gradient)" />
  
  <!-- 视频图标 -->
  <g transform="translate(${size * 0.25}, ${size * 0.25})">
    <rect width="${size * 0.5}" height="${size * 0.35}" rx="${size * 0.05}" ry="${size * 0.05}" fill="white" opacity="0.9" />
    <polygon points="${size * 0.15},${size * 0.1} ${size * 0.4},${size * 0.225} ${size * 0.15},${size * 0.35}" fill="#3B82F6" />
  </g>
  
  <!-- 应用名称 -->
  <text x="${size * 0.5}" y="${size * 0.85}" font-family="Arial, sans-serif" font-size="${size * 0.12}" font-weight="bold" text-anchor="middle" fill="white">赫本</text>
</svg>`;

  return svg;
}

// 生成启动屏幕SVG
function generateLaunchScreenSVG(width, height, filename) {
  const svg = `<?xml version="1.0" encoding="UTF-8"?>
<svg width="${width}" height="${height}" viewBox="0 0 ${width} ${height}" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#6366F1;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="${width}" height="${height}" fill="url(#bgGradient)" />
  
  <!-- 中心图标 -->
  <g transform="translate(${width * 0.5 - 60}, ${height * 0.5 - 60})">
    <rect width="120" height="120" rx="24" ry="24" fill="white" opacity="0.1" />
    <rect width="80" height="56" x="20" y="32" rx="8" ry="8" fill="white" opacity="0.9" />
    <polygon points="35,42 65,58 35,74" fill="#3B82F6" />
  </g>
  
  <!-- 应用名称 -->
  <text x="${width * 0.5}" y="${height * 0.5 + 100}" font-family="Arial, sans-serif" font-size="32" font-weight="bold" text-anchor="middle" fill="white">赫本视频</text>
  
  <!-- 加载提示 -->
  <text x="${width * 0.5}" y="${height * 0.5 + 140}" font-family="Arial, sans-serif" font-size="16" text-anchor="middle" fill="white" opacity="0.8">正在加载...</text>
</svg>`;

  return svg;
}

// 生成社交媒体图片SVG
function generateSocialImageSVG(width, height, filename) {
  const svg = `<?xml version="1.0" encoding="UTF-8"?>
<svg width="${width}" height="${height}" viewBox="0 0 ${width} ${height}" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="socialGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#6366F1;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="${width}" height="${height}" fill="url(#socialGradient)" />
  
  <!-- 主要内容区域 -->
  <g transform="translate(${width * 0.1}, ${height * 0.2})">
    <!-- 图标 -->
    <rect width="120" height="120" rx="24" ry="24" fill="white" opacity="0.1" />
    <rect width="80" height="56" x="20" y="32" rx="8" ry="8" fill="white" opacity="0.9" />
    <polygon points="35,42 65,58 35,74" fill="#3B82F6" />
    
    <!-- 标题 -->
    <text x="160" y="40" font-family="Arial, sans-serif" font-size="48" font-weight="bold" fill="white">赫本视频</text>
    
    <!-- 描述 -->
    <text x="160" y="80" font-family="Arial, sans-serif" font-size="24" fill="white" opacity="0.9">高品质视频内容平台</text>
    <text x="160" y="110" font-family="Arial, sans-serif" font-size="20" fill="white" opacity="0.8">提供丰富的视频资源和优质的观看体验</text>
  </g>
</svg>`;

  return svg;
}

// 主函数
function main() {
  console.log('🎨 开始生成WebClip图标...');
  
  const iconsDir = path.join(__dirname, '../frontend/public/icons');
  ensureDir(iconsDir);
  
  // 生成普通图标
  Object.entries(iconSizes).forEach(([filename, size]) => {
    const filePath = path.join(iconsDir, filename);
    let svg;
    
    if (filename.includes('og-image') || filename.includes('twitter-image')) {
      // 社交媒体图片使用特殊尺寸
      const height = filename.includes('twitter-image') ? 600 : 630;
      svg = generateSocialImageSVG(size, height, filename);
    } else {
      svg = generateSVGIcon(size, filename);
    }
    
    fs.writeFileSync(filePath, svg);
    console.log(`✅ 生成图标: ${filename} (${size}x${size})`);
  });
  
  // 生成启动屏幕
  Object.entries(launchScreens).forEach(([filename, dimensions]) => {
    const filePath = path.join(iconsDir, filename);
    const svg = generateLaunchScreenSVG(dimensions.width, dimensions.height, filename);
    
    fs.writeFileSync(filePath, svg);
    console.log(`✅ 生成启动屏幕: ${filename} (${dimensions.width}x${dimensions.height})`);
  });
  
  // 生成README文件
  const readmePath = path.join(iconsDir, 'README.md');
  const readme = `# WebClip 图标说明

这个目录包含了iOS WebClip和PWA所需的所有图标文件。

## 图标类型

### Apple Touch Icons
- 用于iOS设备添加到主屏幕时显示的图标
- 支持多种尺寸以适配不同设备

### Android Chrome Icons  
- 用于Android设备的PWA图标
- 包含maskable图标支持

### 启动屏幕
- iOS WebClip启动时显示的启动画面
- 支持多种iPhone尺寸

### 社交媒体图片
- Open Graph和Twitter Card使用的分享图片

## 自动生成

所有图标都是通过 \`scripts/generate-icons.js\` 脚本自动生成的SVG文件。

如需修改图标样式，请编辑生成脚本后重新运行：

\`\`\`bash
node scripts/generate-icons.js
\`\`\`

## 注意事项

- SVG格式确保图标在所有尺寸下都保持清晰
- 使用了品牌色彩渐变 (#3B82F6 到 #6366F1)
- 图标设计简洁明了，适合小尺寸显示
`;

  fs.writeFileSync(readmePath, readme);
  console.log('📝 生成README文件');
  
  console.log('🎉 WebClip图标生成完成！');
  console.log(`📁 图标目录: ${iconsDir}`);
  console.log(`📊 生成图标数量: ${Object.keys(iconSizes).length + Object.keys(launchScreens).length}`);
}

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = {
  generateSVGIcon,
  generateLaunchScreenSVG,
  generateSocialImageSVG,
  iconSizes,
  launchScreens
};
