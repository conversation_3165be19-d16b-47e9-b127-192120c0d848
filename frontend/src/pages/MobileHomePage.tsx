import React, { useEffect, useState, useRef } from "react";
import { useNavigate } from "react-router-dom";
import MobileVideoCard from "../components/MobileVideoCard";
import { ScrollToTopButton } from "../components/ScrollRestoration";

import { unifiedApiService } from "../services/unifiedApiService";
import { homePageStateService } from "../services/homePageStateService";
import { useAuthStore } from "../store/authStore";
import { toast } from "../store/toastStore";
import type { Video } from "../types";
import { checkAndNavigateToIntendedPath } from "../components/RouteGuard";

const MobileHomePage: React.FC = () => {
  const navigate = useNavigate();
  const { user, isAuthenticated, updateUser } = useAuthStore();
  const [recommendedVideos, setRecommendedVideos] = useState<Video[]>([]);
  const [hotVideos, setHotVideos] = useState<Video[]>([]);
  const [categories, setCategories] = useState<string[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>("");
  const [categoryVideos, setCategoryVideos] = useState<Video[]>([]);
  const [loading, setLoading] = useState(true);
  const [showPreviewEndModal, setShowPreviewEndModal] = useState(false);
  const [selectedVideo, setSelectedVideo] = useState<Video | null>(null);
  const [stateRestored, setStateRestored] = useState(false);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    initializePage();
    // 检查是否有用户原本想访问的路径，并在适当时候导航过去
    checkAndNavigateToIntendedPath(navigate);
  }, [navigate]);

  // 监听滚动位置变化
  useEffect(() => {
    const handleScroll = () => {
      if (stateRestored) {
        const scrollPosition = window.pageYOffset || document.documentElement.scrollTop;
        homePageStateService.updateScrollPosition(scrollPosition);
      }
    };

    const throttledScroll = throttle(handleScroll, 1000); // 1秒节流
    window.addEventListener('scroll', throttledScroll);
    return () => window.removeEventListener('scroll', throttledScroll);
  }, [stateRestored]);

  // 节流函数
  const throttle = (func: Function, delay: number) => {
    let timeoutId: number;
    let lastExecTime = 0;
    return function (...args: any[]) {
      const currentTime = Date.now();

      if (currentTime - lastExecTime > delay) {
        func(...args);
        lastExecTime = currentTime;
      } else {
        clearTimeout(timeoutId);
        timeoutId = window.setTimeout(() => {
          func(...args);
          lastExecTime = Date.now();
        }, delay - (currentTime - lastExecTime));
      }
    };
  };

  const initializePage = async () => {
    try {
      // 1. 先恢复状态
      await restorePageState();

      // 2. 加载数据
      await loadData();

      // 3. 恢复滚动位置
      await restoreScrollPosition();
    } catch (error) {
      console.error('页面初始化失败:', error);
    }
  };

  const restorePageState = async () => {
    try {
      const savedState = await homePageStateService.loadState();

      if (homePageStateService.shouldRestoreState()) {
        console.log('🔄 恢复首页状态:', savedState);

        // 恢复选中的分类
        if (savedState.selectedCategory) {
          setSelectedCategory(savedState.selectedCategory);
        }

        // 恢复分类视频
        if (savedState.categoryVideos && savedState.categoryVideos.length > 0) {
          setCategoryVideos(savedState.categoryVideos);
        }

        setStateRestored(true);
        console.log('✅ 首页状态恢复完成');
      } else {
        console.log('⏰ 状态过期，使用默认状态');
        setStateRestored(true);
      }
    } catch (error) {
      console.error('❌ 恢复首页状态失败:', error);
      setStateRestored(true);
    }
  };

  const restoreScrollPosition = async () => {
    if (!stateRestored) return;

    try {
      const savedState = homePageStateService.getCurrentState();
      if (savedState.scrollPosition > 0) {
        // 延迟恢复滚动位置，确保页面已渲染
        setTimeout(() => {
          window.scrollTo({
            top: savedState.scrollPosition,
            behavior: 'smooth'
          });
          console.log('📜 滚动位置已恢复:', savedState.scrollPosition);
        }, 500);
      }
    } catch (error) {
      console.error('❌ 恢复滚动位置失败:', error);
    }
  };

  const loadData = async () => {
    try {
      setLoading(true);

      // 使用缓存优先策略并行加载数据
      const promises = [
        // 推荐视频 - 缓存优先
        unifiedApiService.getRecommendedVideos(
          6,
          (cachedData) => {
            console.log("✅ 缓存命中：推荐视频", cachedData);
            setRecommendedVideos(Array.isArray(cachedData) ? cachedData : []);
          },
          (freshData) => {
            console.log("🔄 数据更新：推荐视频", freshData);
            setRecommendedVideos(Array.isArray(freshData) ? freshData : []);
          }
        ),

        // 热门视频 - 缓存优先
        unifiedApiService.getHotVideos(
          6,
          (cachedData) => {
            console.log("✅ 缓存命中：热门视频", cachedData);
            setHotVideos(Array.isArray(cachedData) ? cachedData : []);
          },
          (freshData) => {
            console.log("🔄 数据更新：热门视频", freshData);
            setHotVideos(Array.isArray(freshData) ? freshData : []);
          }
        ),

        // 分类列表 - 缓存优先
        unifiedApiService.getCategories(
          (cachedData) => {
            console.log("✅ 缓存命中：分类列表", cachedData);
            setCategories(Array.isArray(cachedData) ? cachedData : []);
          },
          (freshData) => {
            console.log("🔄 数据更新：分类列表", freshData);
            setCategories(Array.isArray(freshData) ? freshData : []);
          }
        )
      ];

      await Promise.allSettled(promises);
    } catch (error) {
      console.error("加载数据失败:", error);
      // 确保在错误情况下状态也是数组
      setRecommendedVideos([]);
      setHotVideos([]);
      setCategories([]);
    } finally {
      setLoading(false);
    }
  };

  const handleCategoryClick = async (category: string) => {
    setSelectedCategory(category);

    try {
      const query: Record<string, number | string> = { page: 1, limit: 10 };
      if (category) {
        query.category = category;
      }

      // 使用缓存优先策略加载分类视频
      const result = await unifiedApiService.getVideos(
        query,
        (cachedData) => {
          console.log(`✅ 缓存命中：${category || '全部'}分类视频`, cachedData);
          const videos = Array.isArray(cachedData?.videos) ? cachedData.videos : [];
          setCategoryVideos(videos);

          // 保存状态到缓存
          if (stateRestored) {
            homePageStateService.updateSelectedCategory(category, videos);
          }
        },
        (freshData) => {
          console.log(`🔄 数据更新：${category || '全部'}分类视频`, freshData);
          const videos = Array.isArray(freshData?.videos) ? freshData.videos : [];
          setCategoryVideos(videos);

          // 保存状态到缓存
          if (stateRestored) {
            homePageStateService.updateSelectedCategory(category, videos);
          }
        }
      );

      // 确保状态被保存
      const videos = Array.isArray(result?.videos) ? result.videos : [];
      setCategoryVideos(videos);

      if (stateRestored) {
        await homePageStateService.updateSelectedCategory(category, videos);
        console.log(`💾 分类状态已保存: ${category}`);
      }
    } catch (error) {
      console.error("加载分类视频失败:", error);
      setCategoryVideos([]);
    }
  };

  const handleVideoClick = (video: Video) => {
    // 记录视频查看
    if (stateRestored) {
      homePageStateService.recordVideoView(video.id.toString());
      console.log(`👁️ 记录视频查看: ${video.title}`);
    }

    navigate(`/video/${video.id}`);
  };

  const handlePurchaseClick = (video: Video) => {
    setSelectedVideo(video);
    setShowPreviewEndModal(true);
  };

  const handlePurchase = async () => {
    if (!selectedVideo) return;

    // 移除认证检查，所有用户都可以购买
    try {
      // 创建购买订单
      const order = await unifiedApiService.purchaseVideo(selectedVideo.id);

      // 更新用户金币余额
      const updatedUser = await unifiedApiService.getUserInfo();
      updateUser(updatedUser);

      // 重新加载数据
      await loadData();
      setShowPreviewEndModal(false);
      toast.success(`购买成功！已获得《${selectedVideo.title}》观看权限`);
    } catch (error: any) {
      toast.error(error.message || "购买失败，请重试");
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="loading-spinner"></div>
        <span className="ml-2 text-gray-600">加载中...</span>
      </div>
    );
  }

  return (
    <div>
      {/* Banner区域 */}
      <section className="relative h-32 bg-gradient-to-r from-pink-500 to-purple-600 overflow-hidden">
        <div className="absolute inset-0 bg-black bg-opacity-10"></div>
        <div className="relative h-full flex items-center justify-center">
          <div className="text-white text-center">
            <h1 className="text-xl font-bold mb-1">赫本</h1>
            <p className="text-sm opacity-90">海量优质内容，精彩不停</p>
          </div>
        </div>
      </section>

      {/* 分类标签 - 平铺显示 */}
      {categories && categories.length > 0 && (
        <section className="bg-white px-4 py-4">
          <div className="grid grid-cols-4 gap-3">
            <button
              onClick={() => handleCategoryClick("")}
              className={`px-3 py-2 rounded-xl text-sm font-medium transition-all duration-200 active:scale-95 text-center ${
                selectedCategory === ""
                  ? "bg-pink-500 text-white shadow-md"
                  : "bg-gray-100 text-gray-700 active:bg-gray-200"
              }`}
            >
              全部
            </button>
            {(categories || []).map((category) => (
              <button
                key={category}
                onClick={() => handleCategoryClick(category)}
                className={`px-3 py-2 rounded-xl text-sm font-medium transition-all duration-200 active:scale-95 text-center ${
                  selectedCategory === category
                    ? "bg-pink-500 text-white shadow-md"
                    : "bg-gray-100 text-gray-700 active:bg-gray-200"
                }`}
              >
                {category}
              </button>
            ))}
          </div>
        </section>
      )}

      {/* 视频模块列表 */}
      <div className="pb-4">
        {/* 分类视频 */}
        {selectedCategory && categoryVideos && categoryVideos.length > 0 && (
          <section className="bg-white mt-2">
            <div className="flex items-center justify-between px-4 py-3 border-b border-gray-100">
              <h2 className="text-lg font-semibold text-gray-900">
                {selectedCategory}
              </h2>
              <button
                onClick={() =>
                  navigate(
                    `/videos?category=${selectedCategory}&title=${selectedCategory}`
                  )
                }
                className="text-pink-500 text-sm font-medium active:text-pink-600 px-2 py-1 rounded-lg active:bg-pink-50"
              >
                更多 →
              </button>
            </div>
            <div className="grid grid-cols-2 gap-3 p-3">
              {(categoryVideos || []).slice(0, 4).map((video) => (
                <MobileVideoCard
                  key={video.id}
                  video={video}
                  onClick={handleVideoClick}
                  onPurchaseClick={handlePurchaseClick}
                  isViewed={stateRestored && homePageStateService.isVideoViewed(video.id.toString())}
                  compact={true}
                />
              ))}
            </div>
          </section>
        )}

        {/* 推荐视频模块 */}
        {recommendedVideos && recommendedVideos.length > 0 && (
          <section className="bg-white mt-2">
            <div className="flex items-center justify-between px-4 py-3 border-b border-gray-100">
              <div className="flex items-center">
                <span className="text-lg mr-2">🔥</span>
                <h2 className="text-lg font-semibold text-gray-900">
                  推荐视频
                </h2>
              </div>
              <button
                onClick={() =>
                  navigate("/videos?type=recommended&title=推荐视频")
                }
                className="text-pink-500 text-sm font-medium active:text-pink-600 px-2 py-1 rounded-lg active:bg-pink-50"
              >
                更多 →
              </button>
            </div>
            <div className="grid grid-cols-2 gap-3 p-3">
              {(recommendedVideos || []).slice(0, 4).map((video) => (
                <MobileVideoCard
                  key={video.id}
                  video={video}
                  onClick={handleVideoClick}
                  onPurchaseClick={handlePurchaseClick}
                  isViewed={stateRestored && homePageStateService.isVideoViewed(video.id.toString())}
                  compact={true}
                />
              ))}
            </div>
          </section>
        )}

        {/* 热门视频模块 */}
        {hotVideos && hotVideos.length > 0 && (
          <section className="bg-white mt-2">
            <div className="flex items-center justify-between px-4 py-3 border-b border-gray-100">
              <div className="flex items-center">
                <span className="text-lg mr-2">🌟</span>
                <h2 className="text-lg font-semibold text-gray-900">
                  热门视频
                </h2>
              </div>
              <button
                onClick={() => navigate("/videos?type=hot&title=热门视频")}
                className="text-pink-500 text-sm font-medium active:text-pink-600 px-2 py-1 rounded-lg active:bg-pink-50"
              >
                更多 →
              </button>
            </div>
            <div className="grid grid-cols-2 gap-3 p-3">
              {hotVideos.slice(0, 4).map((video) => (
                <MobileVideoCard
                  key={video.id}
                  video={video}
                  onClick={handleVideoClick}
                  isViewed={stateRestored && homePageStateService.isVideoViewed(video.id.toString())}
                  compact={true}
                />
              ))}
            </div>
          </section>
        )}

        {/* 免费视频模块 */}
        <section className="bg-white mt-2">
          <div className="flex items-center justify-between px-4 py-3 border-b border-gray-100">
            <div className="flex items-center">
              <span className="text-lg mr-2">🆓</span>
              <h2 className="text-lg font-semibold text-gray-900">免费观看</h2>
            </div>
            <button
              onClick={() => navigate("/videos?type=free&title=免费观看")}
              className="text-pink-500 text-sm font-medium active:text-pink-600 px-2 py-1 rounded-lg active:bg-pink-50"
            >
              更多 →
            </button>
          </div>
          <div className="grid grid-cols-2 gap-3 p-3">
            {(recommendedVideos || []).slice(0, 4).map((video) => (
              <MobileVideoCard
                key={`free-${video.id}`}
                video={video}
                onClick={handleVideoClick}
                isViewed={stateRestored && homePageStateService.isVideoViewed(video.id.toString())}
                compact={true}
              />
            ))}
          </div>
        </section>

        {/* VIP专享模块 */}
        <section className="bg-white mt-2">
          <div className="flex items-center justify-between px-4 py-3 border-b border-gray-100">
            <div className="flex items-center">
              <span className="text-lg mr-2">👑</span>
              <h2 className="text-lg font-semibold text-gray-900">VIP专享</h2>
            </div>
            <button
              onClick={() => navigate("/videos?type=vip&title=VIP专享")}
              className="text-pink-500 text-sm font-medium active:text-pink-600 px-2 py-1 rounded-lg active:bg-pink-50"
            >
              更多 →
            </button>
          </div>
          <div className="grid grid-cols-2 gap-3 p-3">
            {(hotVideos || []).slice(0, 4).map((video) => (
              <MobileVideoCard
                key={`vip-${video.id}`}
                video={video}
                onClick={handleVideoClick}
                isViewed={stateRestored && homePageStateService.isVideoViewed(video.id.toString())}
                compact={true}
              />
            ))}
          </div>
        </section>

        {/* 最新上传模块 */}
        <section className="bg-white mt-2">
          <div className="flex items-center justify-between px-4 py-3 border-b border-gray-100">
            <div className="flex items-center">
              <span className="text-lg mr-2">🆕</span>
              <h2 className="text-lg font-semibold text-gray-900">最新上传</h2>
            </div>
            <button
              onClick={() => navigate("/videos?type=latest&title=最新上传")}
              className="text-pink-500 text-sm font-medium active:text-pink-600 px-2 py-1 rounded-lg active:bg-pink-50"
            >
              更多 →
            </button>
          </div>
          <div className="grid grid-cols-2 gap-3 p-3">
            {(recommendedVideos || []).slice(2, 6).map((video) => (
              <MobileVideoCard
                key={`latest-${video.id}`}
                video={video}
                onClick={handleVideoClick}
                isViewed={stateRestored && homePageStateService.isVideoViewed(video.id.toString())}
                compact={true}
              />
            ))}
          </div>
        </section>
      </div>

      {/* 全局购买弹窗 */}
      {showPreviewEndModal && selectedVideo && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 mx-4 max-w-sm w-full text-center">
            <div className="text-4xl mb-4">💰</div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              购买视频
            </h3>
            <p className="text-gray-600 mb-6">{selectedVideo.title}</p>
            <div className="space-y-3">
              <button
                onClick={handlePurchase}
                className="w-full bg-blue-600 text-white px-4 py-3 rounded-lg hover:bg-blue-700 transition-colors font-medium"
              >
                购买观看 {selectedVideo.price} 金币
              </button>
              <button
                onClick={() => {
                  setShowPreviewEndModal(false);
                  navigate("/vip");
                }}
                className="w-full bg-yellow-600 text-white px-4 py-3 rounded-lg hover:bg-yellow-700 transition-colors font-medium"
              >
                开通VIP免费观看
              </button>
              <button
                onClick={() => {
                  setShowPreviewEndModal(false);
                  setSelectedVideo(null);
                }}
                className="w-full bg-gray-300 text-gray-700 px-4 py-3 rounded-lg hover:bg-gray-400 transition-colors"
              >
                关闭
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 回到顶部按钮 */}
      <ScrollToTopButton threshold={200} />
    </div>
  );
};

export default MobileHomePage;
