import React, { useState, useEffect } from 'react';
import MobileLayout from '../components/MobileLayout';
import { secureDeviceManager, getSecureDeviceId } from '../utils/secureDeviceManager';
import { deviceFingerprintGenerator } from '../utils/deviceFingerprint';

const DeviceIdTestPage: React.FC = () => {
  const [deviceInfo, setDeviceInfo] = useState<any>(null);
  const [registryStats, setRegistryStats] = useState<any>(null);
  const [testResults, setTestResults] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const addTestResult = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setTestResults(prev => [`[${timestamp}] ${message}`, ...prev.slice(0, 19)]);
  };

  // 加载设备信息
  const loadDeviceInfo = async () => {
    try {
      const info = secureDeviceManager.getDeviceInfo();
      const stats = secureDeviceManager.getRegistryStats();
      setDeviceInfo(info);
      setRegistryStats(stats);
      addTestResult('✅ 设备信息加载完成');
    } catch (error) {
      addTestResult(`❌ 加载设备信息失败: ${error}`);
    }
  };

  // 测试设备ID唯一性
  const testDeviceIdUniqueness = async () => {
    setIsLoading(true);
    addTestResult('🧪 开始测试设备ID唯一性...');

    try {
      const ids = new Set<string>();
      const iterations = 10;

      for (let i = 0; i < iterations; i++) {
        const id = getSecureDeviceId();
        if (id) {
          ids.add(id);
          addTestResult(`第${i + 1}次获取: ${id.substring(0, 20)}...`);
        }
        
        // 短暂延迟
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      if (ids.size === 1) {
        addTestResult(`✅ 唯一性测试通过: ${iterations}次获取得到相同ID`);
      } else {
        addTestResult(`❌ 唯一性测试失败: ${iterations}次获取得到${ids.size}个不同ID`);
      }
    } catch (error) {
      addTestResult(`❌ 唯一性测试出错: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  // 测试指纹稳定性
  const testFingerprintStability = async () => {
    setIsLoading(true);
    addTestResult('🧪 开始测试指纹稳定性...');

    try {
      const fingerprints = new Set<string>();
      const iterations = 5;

      for (let i = 0; i < iterations; i++) {
        const fingerprint = await deviceFingerprintGenerator.generateFingerprint();
        fingerprints.add(fingerprint);
        addTestResult(`第${i + 1}次指纹: ${fingerprint.substring(0, 16)}...`);
        
        await new Promise(resolve => setTimeout(resolve, 200));
      }

      if (fingerprints.size === 1) {
        addTestResult(`✅ 指纹稳定性测试通过: ${iterations}次生成得到相同指纹`);
      } else {
        addTestResult(`❌ 指纹稳定性测试失败: ${iterations}次生成得到${fingerprints.size}个不同指纹`);
      }
    } catch (error) {
      addTestResult(`❌ 指纹稳定性测试出错: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  // 测试设备验证
  const testDeviceVerification = async () => {
    setIsLoading(true);
    addTestResult('🧪 开始测试设备验证...');

    try {
      const isConsistent = await secureDeviceManager.manualVerify();
      if (isConsistent) {
        addTestResult('✅ 设备验证通过: 设备环境一致');
      } else {
        addTestResult('⚠️ 设备验证失败: 检测到环境变化');
      }
    } catch (error) {
      addTestResult(`❌ 设备验证出错: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  // 强制重新生成设备ID
  const regenerateDeviceId = async () => {
    setIsLoading(true);
    addTestResult('🔄 开始重新生成设备ID...');

    try {
      const oldId = getSecureDeviceId();
      const newId = await secureDeviceManager.regenerateDeviceId();
      
      addTestResult(`旧ID: ${oldId?.substring(0, 20)}...`);
      addTestResult(`新ID: ${newId.substring(0, 20)}...`);
      
      if (oldId !== newId) {
        addTestResult('✅ 设备ID重新生成成功');
      } else {
        addTestResult('⚠️ 设备ID未发生变化');
      }
      
      await loadDeviceInfo();
    } catch (error) {
      addTestResult(`❌ 重新生成设备ID失败: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  // 清除所有数据
  const clearAllData = () => {
    try {
      localStorage.removeItem('secure_device_info');
      localStorage.removeItem('device_id_registry');
      localStorage.removeItem('deviceId');
      localStorage.removeItem('device_manager_info');
      
      addTestResult('🧹 所有设备数据已清除');
      addTestResult('🔄 请刷新页面重新初始化');
      
      setDeviceInfo(null);
      setRegistryStats(null);
    } catch (error) {
      addTestResult(`❌ 清除数据失败: ${error}`);
    }
  };

  // 运行完整测试套件
  const runFullTestSuite = async () => {
    setTestResults([]);
    addTestResult('🚀 开始运行完整测试套件...');
    
    await testDeviceIdUniqueness();
    await new Promise(resolve => setTimeout(resolve, 500));
    
    await testFingerprintStability();
    await new Promise(resolve => setTimeout(resolve, 500));
    
    await testDeviceVerification();
    
    addTestResult('🎉 完整测试套件运行完成');
  };

  useEffect(() => {
    loadDeviceInfo();
  }, []);

  return (
    <MobileLayout title="设备ID测试" showBack>
      <div className="p-4 space-y-6">
        {/* 设备信息 */}
        <div className="bg-white rounded-lg p-4 shadow-sm">
          <h2 className="text-lg font-semibold mb-4">设备信息</h2>
          
          {deviceInfo ? (
            <div className="space-y-3 text-sm">
              <div>
                <span className="text-gray-600">设备ID：</span>
                <span className="font-mono text-xs break-all">{deviceInfo.deviceId}</span>
              </div>
              <div>
                <span className="text-gray-600">硬件哈希：</span>
                <span className="font-mono text-xs">{deviceInfo.hardwareHash}</span>
              </div>
              <div>
                <span className="text-gray-600">版本：</span>
                <span className="font-medium">v{deviceInfo.version}</span>
              </div>
              <div>
                <span className="text-gray-600">一致性：</span>
                <span className={`font-medium ${deviceInfo.isConsistent ? 'text-green-600' : 'text-red-600'}`}>
                  {deviceInfo.isConsistent ? '✅ 一致' : '❌ 不一致'}
                </span>
              </div>
              <div>
                <span className="text-gray-600">最后验证：</span>
                <span className="text-xs">{new Date(deviceInfo.lastVerified).toLocaleString()}</span>
              </div>
            </div>
          ) : (
            <div className="text-gray-500">设备信息加载中...</div>
          )}
        </div>

        {/* 注册表统计 */}
        {registryStats && (
          <div className="bg-white rounded-lg p-4 shadow-sm">
            <h3 className="text-lg font-semibold mb-3">注册表统计</h3>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600">设备总数：</span>
                <span className="font-medium">{registryStats.totalDevices}</span>
              </div>
              <div>
                <span className="text-gray-600">最早注册：</span>
                <span className="text-xs">
                  {registryStats.oldestDevice ? new Date(registryStats.oldestDevice).toLocaleDateString() : '无'}
                </span>
              </div>
            </div>
          </div>
        )}

        {/* 控制面板 */}
        <div className="bg-white rounded-lg p-4 shadow-sm">
          <h3 className="text-lg font-semibold mb-4">测试控制面板</h3>
          
          <div className="grid grid-cols-2 gap-3 mb-4">
            <button
              onClick={runFullTestSuite}
              disabled={isLoading}
              className="bg-blue-500 text-white px-4 py-2 rounded-lg disabled:opacity-50 text-sm"
            >
              {isLoading ? '测试中...' : '完整测试'}
            </button>
            
            <button
              onClick={testDeviceIdUniqueness}
              disabled={isLoading}
              className="bg-green-500 text-white px-4 py-2 rounded-lg disabled:opacity-50 text-sm"
            >
              唯一性测试
            </button>
            
            <button
              onClick={testFingerprintStability}
              disabled={isLoading}
              className="bg-purple-500 text-white px-4 py-2 rounded-lg disabled:opacity-50 text-sm"
            >
              稳定性测试
            </button>
            
            <button
              onClick={testDeviceVerification}
              disabled={isLoading}
              className="bg-orange-500 text-white px-4 py-2 rounded-lg disabled:opacity-50 text-sm"
            >
              验证测试
            </button>
          </div>

          <div className="grid grid-cols-2 gap-3 mb-4">
            <button
              onClick={regenerateDeviceId}
              disabled={isLoading}
              className="bg-yellow-500 text-white px-4 py-2 rounded-lg disabled:opacity-50 text-sm"
            >
              重新生成ID
            </button>
            
            <button
              onClick={loadDeviceInfo}
              disabled={isLoading}
              className="bg-indigo-500 text-white px-4 py-2 rounded-lg disabled:opacity-50 text-sm"
            >
              刷新信息
            </button>
          </div>

          <button
            onClick={clearAllData}
            className="w-full bg-red-500 text-white px-4 py-2 rounded-lg text-sm"
          >
            清除所有数据
          </button>
        </div>

        {/* 测试结果 */}
        <div className="bg-white rounded-lg p-4 shadow-sm">
          <h3 className="text-lg font-semibold mb-3">测试结果</h3>
          <div className="space-y-1 max-h-64 overflow-y-auto">
            {testResults.length > 0 ? (
              testResults.map((result, index) => (
                <div key={index} className="text-xs font-mono p-2 bg-gray-50 rounded">
                  {result}
                </div>
              ))
            ) : (
              <div className="text-gray-500 text-sm">暂无测试结果</div>
            )}
          </div>
        </div>

        {/* 说明 */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <h3 className="text-lg font-semibold text-yellow-800 mb-2">📋 测试说明</h3>
          <ul className="text-sm text-yellow-700 space-y-1">
            <li>• <strong>唯一性测试</strong>：验证多次获取是否返回相同设备ID</li>
            <li>• <strong>稳定性测试</strong>：验证指纹生成是否稳定一致</li>
            <li>• <strong>验证测试</strong>：检查设备环境是否发生变化</li>
            <li>• <strong>重新生成ID</strong>：强制生成新的设备ID（会更新注册表）</li>
            <li>• <strong>注册表</strong>：防止相同硬件生成重复ID的机制</li>
          </ul>
        </div>
      </div>
    </MobileLayout>
  );
};

export default DeviceIdTestPage;
