import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { unifiedApiService } from '../services/unifiedApiService';
import { useAuthStore } from '../store/authStore';
import MobileLayout from '../components/MobileLayout';

const CommissionListPage: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuthStore();
  const [loading, setLoading] = useState(true);
  const [commissions, setCommissions] = useState<any[]>([]);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  useEffect(() => {
    // 检查邮箱绑定状态
    if (!user?.email) {
      navigate('/link-email');
      return;
    }
    loadCommissions();
  }, [page, user?.email, navigate]);

  const loadCommissions = async () => {
    try {
      setLoading(true);
      const data = await unifiedApiService.getCommissionList(page, 10);
      setCommissions(data.commissions);
      setTotalPages(data.totalPages);
    } catch (error) {
      console.error('加载返佣记录失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    const colors = {
      pending: 'text-yellow-600 bg-yellow-100',
      paid: 'text-green-600 bg-green-100',
      cancelled: 'text-red-600 bg-red-100'
    };
    return colors[status as keyof typeof colors] || 'text-gray-600 bg-gray-100';
  };

  const getStatusText = (status: string) => {
    const texts = {
      pending: '待发放',
      paid: '已发放',
      cancelled: '已取消'
    };
    return texts[status as keyof typeof texts] || '未知状态';
  };

  const getTypeIcon = (type: string) => {
    const icons = {
      recharge: '💰',
      vip_purchase: '👑',
      video_purchase: '🎬'
    };
    return icons[type as keyof typeof icons] || '💰';
  };

  const getTypeText = (type: string) => {
    const texts = {
      recharge: '充值返佣',
      vip_purchase: 'VIP购买返佣',
      video_purchase: '视频购买返佣'
    };
    return texts[type as keyof typeof texts] || '未知类型';
  };

  const getLevelBadge = (level: number) => {
    const colors = [
      'bg-red-100 text-red-600',
      'bg-orange-100 text-orange-600', 
      'bg-yellow-100 text-yellow-600',
      'bg-green-100 text-green-600',
      'bg-blue-100 text-blue-600'
    ];
    return colors[level - 1] || 'bg-gray-100 text-gray-600';
  };

  if (loading && page === 1) {
    return (
      <MobileLayout title="返佣记录" showBack showTabBar={false}>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">加载中...</p>
          </div>
        </div>
      </MobileLayout>
    );
  }

  return (
    <MobileLayout title="返佣记录" showBack showTabBar={false}>
      <div className="max-w-md mx-auto p-4">
        {commissions.length === 0 ? (
          <div className="bg-white rounded-lg p-8 text-center">
            <div className="text-6xl mb-4">💰</div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">暂无返佣记录</h3>
            <p className="text-gray-600 mb-4">
              当您推广的用户充值消费时，返佣记录会显示在这里
            </p>
            <button
              onClick={() => navigate('/invite')}
              className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              去推广赚钱
            </button>
          </div>
        ) : (
          <div className="space-y-3">
            {commissions.map((commission) => (
              <div key={commission.id} className="bg-white rounded-lg p-4 shadow-sm">
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    <div className="text-2xl">{getTypeIcon(commission.type)}</div>
                    <div>
                      <h3 className="font-semibold text-gray-900">
                        {getTypeText(commission.type)}
                      </h3>
                      <p className="text-sm text-gray-600">
                        来自: {commission.fromUser?.nickname || '未知用户'}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="flex items-center space-x-2 mb-1">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getLevelBadge(commission.level)}`}>
                        {commission.level}级
                      </span>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(commission.status)}`}>
                        {getStatusText(commission.status)}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4 text-sm mb-3">
                  <div>
                    <span className="text-gray-500">原始金额:</span>
                    <div className="font-medium">{commission.originalAmount} 金币</div>
                  </div>
                  <div>
                    <span className="text-gray-500">返佣比例:</span>
                    <div className="font-medium">{(commission.commissionRate * 100).toFixed(1)}%</div>
                  </div>
                </div>

                <div className="flex items-center justify-between pt-3 border-t border-gray-100">
                  <span className="text-sm text-gray-600">返佣金额:</span>
                  <span className="text-lg font-bold text-green-600">
                    +{commission.commissionAmount} 金币
                  </span>
                </div>

                <div className="mt-2 text-xs text-gray-500">
                  {commission.status === 'paid' && commission.paidAt && (
                    <div>发放时间: {new Date(commission.paidAt).toLocaleString()}</div>
                  )}
                  <div>创建时间: {new Date(commission.createdAt).toLocaleString()}</div>
                </div>

                {commission.order && (
                  <div className="mt-2 text-xs text-gray-500">
                    订单号: {commission.order.id}
                  </div>
                )}
              </div>
            ))}

            {/* 分页 */}
            {totalPages > 1 && (
              <div className="flex justify-center space-x-2 mt-6">
                <button
                  onClick={() => setPage(Math.max(1, page - 1))}
                  disabled={page === 1}
                  className="px-4 py-2 bg-white border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                >
                  上一页
                </button>
                <span className="px-4 py-2 bg-blue-600 text-white rounded-lg">
                  {page} / {totalPages}
                </span>
                <button
                  onClick={() => setPage(Math.min(totalPages, page + 1))}
                  disabled={page === totalPages}
                  className="px-4 py-2 bg-white border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                >
                  下一页
                </button>
              </div>
            )}
          </div>
        )}

        {/* 返佣统计 */}
        {commissions.length > 0 && (
          <div className="mt-6 bg-white rounded-lg p-4">
            <h3 className="font-semibold text-gray-900 mb-3">返佣统计</h3>
            <div className="grid grid-cols-2 gap-4 text-center">
              <div className="bg-green-50 rounded-lg p-3">
                <div className="text-lg font-bold text-green-600">
                  {commissions.filter(c => c.status === 'paid').reduce((sum, c) => sum + c.commissionAmount, 0)}
                </div>
                <div className="text-sm text-green-600">已到账金币</div>
              </div>
              <div className="bg-yellow-50 rounded-lg p-3">
                <div className="text-lg font-bold text-yellow-600">
                  {commissions.filter(c => c.status === 'pending').reduce((sum, c) => sum + c.commissionAmount, 0)}
                </div>
                <div className="text-sm text-yellow-600">待发放金币</div>
              </div>
            </div>
          </div>
        )}
      </div>
    </MobileLayout>
  );
};

export default CommissionListPage;
