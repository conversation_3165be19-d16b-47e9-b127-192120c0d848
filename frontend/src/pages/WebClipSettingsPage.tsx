import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  detectWebClipEnvironment, 
  getInstallInstructions, 
  shouldShowInstallPrompt,
  dismissInstallPrompt,
  resetInstallPromptState
} from '../utils/webClipHelper';
import { showIOSInstallPrompt, isIOSWebClip, isIOSDevice } from '../components/IOSInstallPrompt';
import { toast } from '../store/toastStore';

const WebClipSettingsPage: React.FC = () => {
  const navigate = useNavigate();
  const [webClipInfo, setWebClipInfo] = useState<any>(null);
  const [installInstructions, setInstallInstructions] = useState<string[]>([]);

  useEffect(() => {
    const info = detectWebClipEnvironment();
    setWebClipInfo(info);
    setInstallInstructions(getInstallInstructions(info.platform));
  }, []);

  const handleShowInstallPrompt = () => {
    if (webClipInfo?.platform === 'ios') {
      const success = showIOSInstallPrompt();
      if (!success) {
        toast.warning('请在Safari浏览器中打开此页面以安装App');
      }
    } else {
      toast.info('当前设备不支持WebClip安装');
    }
  };

  const handleResetPromptState = () => {
    resetInstallPromptState();
    toast.success('安装提示状态已重置');
  };

  const handleDismissPrompt = () => {
    dismissInstallPrompt(true);
    toast.info('已永久关闭安装提示');
  };

  if (!webClipInfo) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">加载中...</div>
      </div>
    );
  }

  return (
    <div className="max-w-md mx-auto bg-white min-h-screen">
      {/* 头部 */}
      <div className="bg-gradient-to-r from-blue-500 to-indigo-600 text-white px-4 py-6">
        <div className="flex items-center space-x-3">
          <button
            onClick={() => navigate(-1)}
            className="w-10 h-10 bg-white bg-opacity-20 rounded-full flex items-center justify-center"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <div>
            <h1 className="text-xl font-bold">App安装设置</h1>
            <p className="text-blue-100 text-sm">WebClip配置和管理</p>
          </div>
        </div>
      </div>

      {/* 当前状态 */}
      <div className="p-4">
        <div className="bg-gray-50 rounded-lg p-4 mb-6">
          <h2 className="font-semibold text-gray-900 mb-3">当前状态</h2>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600">设备平台:</span>
              <span className="font-medium">{webClipInfo.platform.toUpperCase()}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">浏览器:</span>
              <span className="font-medium">{webClipInfo.browserName}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">显示模式:</span>
              <span className="font-medium">{webClipInfo.displayMode}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">WebClip模式:</span>
              <span className={`font-medium ${webClipInfo.isWebClip ? 'text-green-600' : 'text-gray-600'}`}>
                {webClipInfo.isWebClip ? '✅ 已启用' : '❌ 未启用'}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">可安装:</span>
              <span className={`font-medium ${webClipInfo.canInstall ? 'text-green-600' : 'text-gray-600'}`}>
                {webClipInfo.canInstall ? '✅ 支持' : '❌ 不支持'}
              </span>
            </div>
          </div>
        </div>

        {/* WebClip已启用状态 */}
        {webClipInfo.isWebClip && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                <span className="text-2xl">🎉</span>
              </div>
              <div>
                <h3 className="font-semibold text-green-800">App已安装</h3>
                <p className="text-green-600 text-sm">您正在使用WebClip模式，享受原生App体验！</p>
              </div>
            </div>
            
            <div className="mt-4 space-y-2">
              <div className="flex items-center space-x-2 text-sm text-green-700">
                <span>🚀</span>
                <span>快速启动</span>
              </div>
              <div className="flex items-center space-x-2 text-sm text-green-700">
                <span>📱</span>
                <span>全屏体验</span>
              </div>
              <div className="flex items-center space-x-2 text-sm text-green-700">
                <span>🔔</span>
                <span>消息通知支持</span>
              </div>
            </div>
          </div>
        )}

        {/* 安装指南 */}
        {!webClipInfo.isWebClip && webClipInfo.canInstall && (
          <div className="mb-6">
            <h2 className="font-semibold text-gray-900 mb-3">安装指南</h2>
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="space-y-2">
                {installInstructions.map((instruction, index) => (
                  <div key={index} className="flex items-start space-x-3">
                    <span className="flex-shrink-0 w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-xs font-bold">
                      {index + 1}
                    </span>
                    <span className="text-blue-800 text-sm">{instruction}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* 操作按钮 */}
        <div className="space-y-3">
          {!webClipInfo.isWebClip && webClipInfo.canInstall && (
            <button
              onClick={handleShowInstallPrompt}
              className="w-full bg-blue-500 hover:bg-blue-600 text-white py-3 px-4 rounded-lg font-medium transition-colors"
            >
              📱 显示安装提示
            </button>
          )}

          <button
            onClick={handleResetPromptState}
            className="w-full bg-gray-500 hover:bg-gray-600 text-white py-3 px-4 rounded-lg font-medium transition-colors"
          >
            🔄 重置提示状态
          </button>

          {shouldShowInstallPrompt() && (
            <button
              onClick={handleDismissPrompt}
              className="w-full bg-red-500 hover:bg-red-600 text-white py-3 px-4 rounded-lg font-medium transition-colors"
            >
              🚫 永久关闭提示
            </button>
          )}
        </div>

        {/* 帮助信息 */}
        <div className="mt-8 bg-gray-50 rounded-lg p-4">
          <h3 className="font-semibold text-gray-900 mb-2">什么是WebClip？</h3>
          <p className="text-gray-600 text-sm leading-relaxed mb-3">
            WebClip是iOS的一项功能，允许您将网站添加到主屏幕，就像原生App一样使用。
            添加后，网站将以全屏模式运行，没有浏览器界面，提供更好的用户体验。
          </p>
          
          <h3 className="font-semibold text-gray-900 mb-2">优势特性</h3>
          <ul className="text-gray-600 text-sm space-y-1">
            <li>• 快速启动，无需打开浏览器</li>
            <li>• 全屏体验，沉浸式界面</li>
            <li>• 支持推送通知</li>
            <li>• 离线缓存支持</li>
            <li>• 与原生App相似的体验</li>
          </ul>
        </div>

        {/* 技术信息 */}
        <div className="mt-6 bg-gray-50 rounded-lg p-4">
          <h3 className="font-semibold text-gray-900 mb-2">技术信息</h3>
          <div className="text-xs text-gray-500 space-y-1">
            <div>User Agent: {navigator.userAgent}</div>
            <div>Display Mode: {webClipInfo.displayMode}</div>
            <div>Standalone: {webClipInfo.isStandalone ? 'true' : 'false'}</div>
            <div>Platform: {webClipInfo.platform}</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WebClipSettingsPage;
