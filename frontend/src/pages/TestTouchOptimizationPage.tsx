import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  isIOSDevice, 
  isIOSSafari, 
  isWebClipMode, 
  applyIOSTouchOptimization, 
  removeIOSTouchOptimization,
  optimizeElementTouch 
} from '../utils/iosTouchOptimizer';
import { toast } from '../store/toastStore';

const TestTouchOptimizationPage: React.FC = () => {
  const navigate = useNavigate();
  const [deviceInfo, setDeviceInfo] = useState({
    isIOS: false,
    isSafari: false,
    isWebClip: false,
    userAgent: ''
  });
  const [optimizationEnabled, setOptimizationEnabled] = useState(true);

  useEffect(() => {
    const info = {
      isIOS: isIOSDevice(),
      isSafari: isIOSSafari(),
      isWebClip: isWebClipMode(),
      userAgent: navigator.userAgent
    };
    setDeviceInfo(info);
  }, []);

  const toggleOptimization = () => {
    if (optimizationEnabled) {
      removeIOSTouchOptimization();
      toast.info('iOS触摸优化已禁用');
    } else {
      applyIOSTouchOptimization();
      toast.success('iOS触摸优化已启用');
    }
    setOptimizationEnabled(!optimizationEnabled);
  };

  const testCustomFeedback = (event: React.MouseEvent<HTMLButtonElement>) => {
    const button = event.currentTarget;
    optimizeElementTouch(button, {
      enableCustomFeedback: true
    });
    toast.success('自定义触摸反馈已应用到此按钮');
  };

  return (
    <div className="max-w-md mx-auto bg-white min-h-screen">
      {/* 头部 */}
      <div className="bg-gradient-to-r from-purple-500 to-pink-600 text-white px-4 py-6 navbar-safe-top">
        <div className="flex items-center space-x-3 safe-area-left safe-area-right">
          <button
            onClick={() => navigate(-1)}
            className="w-10 h-10 bg-white bg-opacity-20 rounded-full flex items-center justify-center touch-button"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <div>
            <h1 className="text-xl font-bold">触摸优化测试</h1>
            <p className="text-purple-100 text-sm">iOS点击闪烁消除验证</p>
          </div>
        </div>
      </div>

      {/* 内容区域 */}
      <div className="p-4 space-y-4">
        {/* 设备信息 */}
        <div className="bg-gray-50 rounded-lg p-4">
          <h2 className="font-semibold text-gray-900 mb-3">设备信息</h2>
          
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600">iOS设备:</span>
              <span className={`font-medium ${deviceInfo.isIOS ? 'text-green-600' : 'text-red-600'}`}>
                {deviceInfo.isIOS ? '✅ 是' : '❌ 否'}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Safari浏览器:</span>
              <span className={`font-medium ${deviceInfo.isSafari ? 'text-green-600' : 'text-orange-600'}`}>
                {deviceInfo.isSafari ? '✅ 是' : '❌ 否'}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">WebClip模式:</span>
              <span className={`font-medium ${deviceInfo.isWebClip ? 'text-green-600' : 'text-gray-600'}`}>
                {deviceInfo.isWebClip ? '✅ 是' : '❌ 否'}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">触摸优化:</span>
              <span className={`font-medium ${optimizationEnabled ? 'text-green-600' : 'text-red-600'}`}>
                {optimizationEnabled ? '✅ 启用' : '❌ 禁用'}
              </span>
            </div>
          </div>
          
          <div className="mt-3 p-2 bg-gray-100 rounded text-xs text-gray-600 break-all">
            <strong>User Agent:</strong><br />
            {deviceInfo.userAgent}
          </div>
        </div>

        {/* 控制面板 */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h2 className="font-semibold text-blue-900 mb-3">控制面板</h2>
          
          <button
            onClick={toggleOptimization}
            className={`w-full py-3 px-4 rounded-lg font-medium transition-colors ${
              optimizationEnabled
                ? 'bg-red-500 hover:bg-red-600 text-white'
                : 'bg-green-500 hover:bg-green-600 text-white'
            }`}
          >
            {optimizationEnabled ? '🚫 禁用触摸优化' : '✅ 启用触摸优化'}
          </button>
        </div>

        {/* 测试按钮区域 */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <h2 className="font-semibold text-yellow-900 mb-3">点击测试区域</h2>
          <p className="text-yellow-700 text-sm mb-4">
            在iOS设备上点击以下按钮，观察是否有闪烁效果：
          </p>
          
          <div className="space-y-3">
            {/* 普通按钮 */}
            <button
              onClick={() => toast.info('普通按钮被点击')}
              className="w-full bg-blue-500 hover:bg-blue-600 text-white py-3 px-4 rounded-lg font-medium transition-colors"
            >
              🔵 普通按钮
            </button>
            
            {/* 带图标的按钮 */}
            <button
              onClick={() => toast.info('图标按钮被点击')}
              className="w-full bg-green-500 hover:bg-green-600 text-white py-3 px-4 rounded-lg font-medium transition-colors flex items-center justify-center space-x-2"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
              <span>🟢 带图标按钮</span>
            </button>
            
            {/* 自定义反馈按钮 */}
            <button
              onClick={testCustomFeedback}
              className="w-full bg-purple-500 hover:bg-purple-600 text-white py-3 px-4 rounded-lg font-medium transition-colors"
            >
              🟣 自定义反馈按钮
            </button>
            
            {/* 链接样式按钮 */}
            <button
              onClick={() => toast.info('链接样式按钮被点击')}
              className="w-full text-blue-600 underline py-3 px-4 rounded-lg font-medium hover:bg-blue-50 transition-colors"
            >
              🔗 链接样式按钮
            </button>
          </div>
        </div>

        {/* 图标测试区域 */}
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <h2 className="font-semibold text-green-900 mb-3">图标测试区域</h2>
          <p className="text-green-700 text-sm mb-4">
            点击以下图标，观察是否有闪烁效果：
          </p>
          
          <div className="grid grid-cols-4 gap-4">
            {/* SVG图标 */}
            <button
              onClick={() => toast.info('SVG图标被点击')}
              className="flex flex-col items-center justify-center p-4 bg-white rounded-lg border hover:bg-gray-50 transition-colors"
            >
              <svg className="w-8 h-8 text-blue-500 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
              </svg>
              <span className="text-xs text-gray-600">SVG</span>
            </button>
            
            {/* Emoji图标 */}
            <button
              onClick={() => toast.info('Emoji图标被点击')}
              className="flex flex-col items-center justify-center p-4 bg-white rounded-lg border hover:bg-gray-50 transition-colors"
            >
              <span className="text-3xl mb-2">❤️</span>
              <span className="text-xs text-gray-600">Emoji</span>
            </button>
            
            {/* 文字图标 */}
            <button
              onClick={() => toast.info('文字图标被点击')}
              className="flex flex-col items-center justify-center p-4 bg-white rounded-lg border hover:bg-gray-50 transition-colors"
            >
              <span className="text-2xl font-bold text-purple-500 mb-2">A</span>
              <span className="text-xs text-gray-600">文字</span>
            </button>
            
            {/* 组合图标 */}
            <button
              onClick={() => toast.info('组合图标被点击')}
              className="flex flex-col items-center justify-center p-4 bg-white rounded-lg border hover:bg-gray-50 transition-colors"
            >
              <div className="w-8 h-8 bg-gradient-to-r from-pink-500 to-purple-500 rounded-full mb-2 flex items-center justify-center">
                <span className="text-white text-sm font-bold">+</span>
              </div>
              <span className="text-xs text-gray-600">组合</span>
            </button>
          </div>
        </div>

        {/* 测试说明 */}
        <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
          <h2 className="font-semibold text-purple-900 mb-3">测试说明</h2>
          <ul className="text-purple-700 text-sm space-y-2">
            <li>• <strong>正常情况</strong>：点击按钮和图标时不应该有灰色闪烁效果</li>
            <li>• <strong>异常情况</strong>：如果看到灰色高亮闪烁，说明触摸优化未生效</li>
            <li>• <strong>自定义反馈</strong>：应该看到轻微的缩放和透明度变化</li>
            <li>• <strong>WebClip模式</strong>：在添加到主屏幕后测试效果更佳</li>
            <li>• <strong>Safari测试</strong>：在iOS Safari中测试效果最准确</li>
          </ul>
        </div>

        {/* 底部间距 */}
        <div className="h-20"></div>
      </div>
    </div>
  );
};

export default TestTouchOptimizationPage;
