import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuthStore } from '../store/authStore';
import { toast } from '../store/toastStore';
import { detectWebClipEnvironment } from '../utils/webClipHelper';

const SettingsPage: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuthStore();

  // 检测WebClip环境
  const webClipInfo = detectWebClipEnvironment();

  const settingsItems = [
    {
      icon: '👤',
      title: '个人信息',
      subtitle: '修改昵称、查看邮箱等基本信息',
      action: () => navigate('/settings/profile'),
      showArrow: true
    },
    // 只在移动设备上显示WebClip设置
    ...(webClipInfo.isMobile ? [{
      icon: '📱',
      title: 'App安装',
      subtitle: webClipInfo.isWebClip ? 'WebClip已启用，管理App设置' : '将网站添加到主屏幕',
      action: () => navigate('/settings/webclip'),
      showArrow: true,
      highlight: !webClipInfo.isWebClip && webClipInfo.canInstall
    }] : []),
    {
      icon: '❓',
      title: '帮助与反馈',
      subtitle: '获取帮助或提交反馈',
      action: () => {
        // TODO: 实现帮助页面
        toast.info('帮助与反馈功能正在开发中，敬请期待');
      },
      showArrow: true
    },
    {
      icon: 'ℹ️',
      title: '关于我们',
      subtitle: '应用版本和信息',
      action: () => {
        // TODO: 实现关于页面
        toast.info('赫本 v1.0.0');
      },
      showArrow: true
    }
  ];

  return (
    <div className="bg-gray-50 min-h-screen">
      {/* 设置选项 */}
      <div className="px-4 py-4 space-y-2">
        
        {settingsItems.map((item, index) => (
          <button
            key={index}
            onClick={item.action}
            className={`w-full bg-white rounded-xl p-4 flex items-center space-x-4 active:scale-98 transition-all duration-150 text-gray-900 ${
              (item as any).highlight ? 'ring-2 ring-blue-400 ring-opacity-50' : ''
            }`}
          >
            <div className={`w-10 h-10 rounded-full flex items-center justify-center text-lg ${
              (item as any).highlight ? 'bg-blue-100' : 'bg-gray-100'
            }`}>
              {item.icon}
            </div>
            <div className="flex-1 text-left">
              <h3 className="font-medium text-gray-900">
                {item.title}
                {(item as any).highlight && (
                  <span className="ml-2 text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded-full">
                    推荐
                  </span>
                )}
              </h3>
              <p className="text-sm text-gray-500">
                {item.subtitle}
              </p>
            </div>
            {item.showArrow && (
              <div className="text-gray-400">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </div>
            )}
          </button>
        ))}
      </div>

      {/* 版本信息 */}
      <div className="px-4 py-6">
        <div className="bg-white rounded-xl p-4 text-center">
          <div className="text-4xl mb-2">📺</div>
          <h3 className="font-semibold text-gray-900 mb-1">赫本</h3>
          <p className="text-sm text-gray-500">版本 1.0.0</p>
        </div>
      </div>
    </div>
  );
};

export default SettingsPage;
