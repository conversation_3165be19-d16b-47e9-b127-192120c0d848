import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Mail, Gift, Users, Clock, CheckCircle, AlertCircle } from 'lucide-react';
import { unifiedApiService } from '../services/unifiedApiService';
import { useAuthStore } from '../store/authStore';
import MobileLayout from '../components/MobileLayout';

const LinkEmailPage: React.FC = () => {
  const navigate = useNavigate();
  const { user, updateUser } = useAuthStore();
  const [email, setEmail] = useState('');
  const [verificationCode, setVerificationCode] = useState('');
  const [step, setStep] = useState<'input' | 'verify' | 'success'>('input');
  const [loading, setLoading] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const [error, setError] = useState('');
  const [rewardInfo, setRewardInfo] = useState<{
    vipGift?: { granted: boolean; duration: string; expireAt: Date };
    coinsReward?: { granted: boolean; amount: number; newTotal: number };
    mergedData?: any;
  } | null>(null);

  const startCountdown = () => {
    setCountdown(60);
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  const handleSendCode = async () => {
    if (!email.trim()) {
      setError('请输入邮箱地址');
      return;
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setError('请输入有效的邮箱地址');
      return;
    }

    try {
      setLoading(true);
      setError('');

      await unifiedApiService.sendEmailVerification({ email });

      setStep('verify');
      startCountdown();
    } catch (error: any) {
      setError(error.response?.data?.message || '发送验证码失败');
    } finally {
      setLoading(false);
    }
  };

  const handleVerifyAndLink = async () => {
    if (!verificationCode.trim()) {
      setError('请输入验证码');
      return;
    }

    try {
      setLoading(true);
      setError('');

      console.log('🔍 开始验证邮箱验证码:', { email, code: verificationCode });

      const response = await unifiedApiService.verifyEmailCode({
        email,
        code: verificationCode
      });

      console.log('✅ 邮箱验证成功，响应数据:', response);

      if (response.user) {
        console.log('🔄 更新用户信息:', response.user);
        updateUser(response.user);
      }

      // 保存奖励信息
      setRewardInfo({
        vipGift: response.vipGift,
        coinsReward: response.coinsReward,
        mergedData: response.mergedData
      });

      // 检查是否是账号合并
      if (response.mergedData) {
        console.log('🔄 检测到账号合并:', response.mergedData);
        // 可以在这里添加特殊的合并成功提示
      }

      setStep('success');
    } catch (error: any) {
      console.error('❌ 邮箱验证失败:', error);
      console.error('❌ 错误详情:', error.response?.data);
      setError(error.response?.data?.message || '验证失败');
    } finally {
      setLoading(false);
    }
  };

  const handleGoBack = () => {
    navigate(-1);
  };

  // 如果已经关联了邮箱，显示提示
  if (user?.email) {
    return (
      <MobileLayout
        title="绑定邮箱"
        showBack={true}
        showTabBar={false}
      >
        <div className="px-4 md:px-6 py-6 md:py-8">
          <div className="bg-white rounded-lg shadow-sm p-6 text-center">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Mail className="w-8 h-8 text-blue-500" />
            </div>
            <h2 className="text-xl font-semibold text-gray-900 mb-4">已关联邮箱</h2>
            <p className="text-gray-600 mb-2">您已经关联了邮箱：</p>
            <p className="text-blue-600 font-semibold mb-4">{user.email}</p>

            {user.emailVerified ? (
              <div className="bg-green-50 border border-green-200 rounded-lg p-3 mb-4">
                <div className="flex items-center justify-center">
                  <CheckCircle className="w-4 h-4 text-green-600 mr-2" />
                  <span className="text-green-600 text-sm">邮箱已验证</span>
                </div>
              </div>
            ) : (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mb-4">
                <div className="flex items-center justify-center">
                  <AlertCircle className="w-4 h-4 text-yellow-600 mr-2" />
                  <span className="text-yellow-600 text-sm">请查收验证邮件并完成验证</span>
                </div>
              </div>
            )}

            <div className="bg-red-50 border border-red-200 rounded-lg p-3 mb-6">
              <p className="text-red-600 text-sm">
                <strong>注意：</strong>邮箱一旦绑定验证后无法更换，如需更换请联系客服。
              </p>
            </div>

            <button
              onClick={handleGoBack}
              className="w-full bg-blue-500 text-white py-3 px-4 rounded-lg hover:bg-blue-600 transition-colors font-medium"
            >
              返回
            </button>
          </div>
        </div>
      </MobileLayout>
    );
  }



  return (
    <MobileLayout
      title="绑定邮箱"
      showBack={true}
      showTabBar={false}
    >
      <div className="px-4 md:px-6 py-6 md:py-8">
        {step !== 'success' && (
          <>
            {/* 奖励说明 */}
            <div className="bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg p-6 mb-6 text-white">
              <div className="flex items-center mb-4">
                <Gift className="w-6 h-6 mr-2" />
                <h2 className="text-lg font-semibold">绑定邮箱奖励</h2>
              </div>
              <div className="space-y-2 text-sm">
                <div className="flex items-center">
                  <Gift className="w-4 h-4 mr-2 flex-shrink-0" />
                  <span>立即获得100金币奖励</span>
                </div>
                <div className="flex items-center">
                  <Users className="w-4 h-4 mr-2 flex-shrink-0" />
                  <span>邀请好友充值可获得50%佣金</span>
                </div>
                <div className="flex items-center">
                  <Mail className="w-4 h-4 mr-2 flex-shrink-0" />
                  <span>支持最多5级邀请关系</span>
                </div>
                <div className="flex items-center">
                  <Mail className="w-4 h-4 mr-2 flex-shrink-0" />
                  <span>可通过邮箱找回账号</span>
                </div>
              </div>
            </div>

            {/* 步骤指示器 */}
            <div className="flex items-center justify-center mb-6">
              <div className="flex items-center">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                  step === 'input' ? 'bg-blue-500 text-white' : 'bg-green-500 text-white'
                }`}>
                  {step === 'input' ? '1' : <CheckCircle className="w-4 h-4" />}
                </div>
                <div className="w-12 h-0.5 bg-gray-300 mx-2"></div>
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                  step === 'verify' ? 'bg-blue-500 text-white' : 'bg-gray-300 text-gray-500'
                }`}>
                  2
                </div>
              </div>
            </div>
          </>
        )}

        {step === 'input' && (
          <div className="bg-white rounded-lg p-6 shadow-sm">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">输入邮箱地址</h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  邮箱地址
                </label>
                <input
                  type="email"
                  value={email}
                  onChange={(e) => {
                    setEmail(e.target.value);
                    setError('');
                  }}
                  placeholder="请输入您的邮箱地址"
                  className="w-full px-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              {error && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                  <div className="text-red-600 text-sm">{error}</div>
                </div>
              )}

              <button
                onClick={handleSendCode}
                disabled={loading || !email.trim()}
                className="w-full bg-blue-500 text-white py-3 px-4 rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium"
              >
                {loading ? '发送中...' : '发送验证码'}
              </button>
            </div>
          </div>
        )}

        {step === 'verify' && (
          <div className="bg-white rounded-lg p-6 shadow-sm">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">输入验证码</h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  验证码
                </label>
                <p className="text-sm text-gray-600 mb-3">
                  验证码已发送至 <span className="font-medium text-blue-600">{email}</span>
                </p>
                <input
                  type="text"
                  value={verificationCode}
                  onChange={(e) => {
                    setVerificationCode(e.target.value.replace(/\D/g, ''));
                    setError('');
                  }}
                  placeholder="请输入6位验证码"
                  maxLength={6}
                  className="w-full px-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-center text-lg tracking-widest"
                />
              </div>

              {error && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                  <div className="text-red-600 text-sm">{error}</div>
                </div>
              )}

              <div className="flex space-x-3">
                <button
                  onClick={() => {
                    setStep('input');
                    setVerificationCode('');
                    setError('');
                  }}
                  className="flex-1 bg-gray-100 text-gray-700 py-3 px-4 rounded-lg hover:bg-gray-200 transition-colors font-medium"
                >
                  重新输入
                </button>
                <button
                  onClick={handleSendCode}
                  disabled={loading || countdown > 0}
                  className="flex-1 bg-gray-100 text-gray-700 py-3 px-4 rounded-lg hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium"
                >
                  {countdown > 0 ? `${countdown}s后重发` : '重新发送'}
                </button>
              </div>

              <button
                onClick={handleVerifyAndLink}
                disabled={loading || verificationCode.length !== 6}
                className="w-full bg-blue-500 text-white py-3 px-4 rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium"
              >
                {loading ? '验证中...' : '确认绑定'}
              </button>
            </div>
          </div>
        )}

        {step === 'success' && (
          <div className="text-center py-8">
            <div className="bg-white rounded-lg p-8 shadow-sm">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <CheckCircle className="w-8 h-8 text-green-500" />
              </div>

              <h3 className="text-xl font-semibold text-gray-900 mb-2">绑定成功！</h3>
              <p className="text-gray-600 mb-6">
                恭喜您成功绑定邮箱 <span className="font-medium text-blue-600">{email}</span>
              </p>

              <div className="space-y-4 mb-6">
                {/* 金币奖励 */}
                {rewardInfo?.coinsReward?.granted && (
                  <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <div className="flex items-center justify-center mb-2">
                      <Gift className="w-5 h-5 text-yellow-600 mr-2" />
                      <span className="font-medium text-yellow-800">金币奖励</span>
                    </div>
                    <p className="text-sm text-yellow-700">
                      您已获得{rewardInfo.coinsReward.amount}金币！当前余额：{rewardInfo.coinsReward.newTotal}金币
                    </p>
                  </div>
                )}

                {/* 账号合并提示 */}
                {rewardInfo?.mergedData && (
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div className="flex items-center justify-center mb-2">
                      <Users className="w-5 h-5 text-blue-600 mr-2" />
                      <span className="font-medium text-blue-800">账号合并成功</span>
                    </div>
                    <p className="text-sm text-blue-700">
                      已将您的历史数据合并到当前账号，包括收藏、购买记录等
                    </p>
                  </div>
                )}

                {/* 绑定成功提示 */}
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <div className="flex items-center justify-center mb-2">
                    <CheckCircle className="w-5 h-5 text-green-600 mr-2" />
                    <span className="font-medium text-green-800">邮箱绑定成功</span>
                  </div>
                  <p className="text-sm text-green-700">
                    您现在可以通过邮箱找回账号，并享受邀请奖励功能
                  </p>
                </div>
              </div>

              <button
                onClick={handleGoBack}
                className="w-full bg-blue-500 text-white py-3 px-4 rounded-lg hover:bg-blue-600 transition-colors font-medium"
              >
                完成
              </button>
            </div>
          </div>
        )}

        {step !== 'success' && (
          <div className="mt-6 text-center text-sm text-gray-500">
            <p>绑定邮箱后，您可以通过邮箱找回账号</p>
            <p>同时享受邀请奖励和VIP特权</p>
          </div>
        )}
      </div>
    </MobileLayout>
  );
};

export default LinkEmailPage;
