import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuthStore } from '../store/authStore';
import { unifiedApiService } from '../services/unifiedApiService';

const MergeAccountPage: React.FC = () => {
  const navigate = useNavigate();
  const { user, updateUser } = useAuthStore();
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const [mergeResult, setMergeResult] = useState<any>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email.trim()) {
      setError('请输入邮箱地址');
      return;
    }

    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      setError('请输入有效的邮箱地址');
      return;
    }

    try {
      setLoading(true);
      setError('');

      const result = await unifiedApiService.mergeAccount(email);
      
      // 更新用户信息
      updateUser(result.user);
      
      setMergeResult(result.mergedData);
      setSuccess(true);
    } catch (error: any) {
      setError(error.message || '账号合并失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  if (success && mergeResult) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <div className="bg-white rounded-lg shadow-md p-6 w-full max-w-md text-center">
          <div className="text-green-600 text-5xl mb-4">🎉</div>
          <h2 className="text-xl font-semibold text-gray-900 mb-4">账号合并成功</h2>
          
          <div className="space-y-4 mb-6">
            {mergeResult.vipInherited && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                <div className="flex items-center justify-center space-x-2 text-yellow-700">
                  <span className="text-lg">👑</span>
                  <span className="text-sm font-semibold">已继承VIP会员权限</span>
                </div>
              </div>
            )}
            
            {mergeResult.coinsInherited > 0 && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                <div className="flex items-center justify-center space-x-2 text-blue-700">
                  <span className="text-lg">💰</span>
                  <span className="text-sm font-semibold">已继承 {mergeResult.coinsInherited} 金币</span>
                </div>
              </div>
            )}
            
            {mergeResult.dataTransferred && (
              <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                <div className="flex items-center justify-center space-x-2 text-green-700">
                  <span className="text-lg">📋</span>
                  <span className="text-sm font-semibold">已转移收藏和购买记录</span>
                </div>
              </div>
            )}
          </div>
          
          <p className="text-gray-600 mb-6">
            您的账号数据已成功合并，现在可以享受之前的会员权限和数据了。
          </p>
          
          <button
            onClick={() => navigate('/profile')}
            className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors"
          >
            返回个人中心
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-md p-6 w-full max-w-md">
        <div className="text-center mb-6">
          <div className="text-blue-600 text-5xl mb-4">🔄</div>
          <h1 className="text-2xl font-bold text-gray-900">合并账号</h1>
          <p className="text-gray-600 mt-2">
            通过邮箱找回并合并之前的会员信息
          </p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
              邮箱地址
            </label>
            <input
              type="email"
              id="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                error ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="请输入之前绑定的邮箱地址"
              required
            />
            {error && (
              <p className="text-red-500 text-sm mt-1">{error}</p>
            )}
          </div>

          <button
            type="submit"
            disabled={loading}
            className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 disabled:bg-blue-400 disabled:cursor-not-allowed transition-colors"
          >
            {loading ? '合并中...' : '合并账号'}
          </button>
        </form>

        <div className="mt-6 text-center">
          <button
            onClick={() => navigate('/profile')}
            className="text-gray-600 hover:text-gray-800 text-sm"
          >
            返回个人中心
          </button>
        </div>

        <div className="mt-4 text-xs text-gray-500 text-center">
          <p className="mb-2">合并账号后，您将获得：</p>
          <ul className="space-y-1">
            <li>• 之前账号的VIP会员权限</li>
            <li>• 金币余额（取较大值）</li>
            <li>• 收藏和购买记录</li>
            <li>• 观看历史记录</li>
          </ul>
        </div>

        <div className="mt-4 bg-yellow-50 border border-yellow-200 rounded-lg p-3">
          <div className="flex items-start space-x-2">
            <div className="text-yellow-600 mt-0.5">⚠️</div>
            <div className="text-xs text-yellow-800">
              <div className="font-semibold mb-1">注意事项</div>
              <div>只能合并已验证邮箱的账号，合并后原账号将被标记为已合并状态。</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MergeAccountPage;
