import React, { useState, useEffect } from 'react';
import MobileLayout from '../components/MobileLayout';
import { useAuthStore } from '../store/authStore';
import { unifiedApiService } from '../services/unifiedApiService';
import { Gift, Coins, User, Mail, CheckCircle, AlertCircle } from 'lucide-react';

const CoinRewardTestPage: React.FC = () => {
  const { user, updateUser } = useAuthStore();
  const [testResults, setTestResults] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const addTestResult = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setTestResults(prev => [`[${timestamp}] ${message}`, ...prev.slice(0, 19)]);
  };

  // 测试新用户创建
  const testNewUserCreation = async () => {
    setIsLoading(true);
    addTestResult('🧪 开始测试新用户创建...');

    try {
      // 清除当前用户状态
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      localStorage.removeItem('accountSetup');
      
      // 重置认证状态
      useAuthStore.setState({
        user: null,
        token: null,
        isAuthenticated: false,
        isGuest: true,
      });

      // 创建新用户
      const { initializeGuest } = useAuthStore.getState();
      await initializeGuest();

      const newUser = useAuthStore.getState().user;
      if (newUser) {
        addTestResult(`✅ 新用户创建成功: ID ${newUser.id}`);
        addTestResult(`💰 初始金币: ${newUser.coins} (应该为0)`);
        
        if (newUser.coins === 0) {
          addTestResult('✅ 新用户默认金币测试通过');
        } else {
          addTestResult(`❌ 新用户默认金币测试失败: 期望0，实际${newUser.coins}`);
        }
      } else {
        addTestResult('❌ 新用户创建失败');
      }
    } catch (error) {
      addTestResult(`❌ 新用户创建测试出错: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  // 测试邮箱绑定奖励
  const testEmailBindingReward = async () => {
    setIsLoading(true);
    addTestResult('🧪 开始测试邮箱绑定奖励...');

    try {
      if (!user) {
        addTestResult('❌ 请先创建用户');
        return;
      }

      const testEmail = `test_${Date.now()}@example.com`;
      addTestResult(`📧 使用测试邮箱: ${testEmail}`);

      // 发送验证码
      await unifiedApiService.sendEmailVerification({ email: testEmail });
      addTestResult('✅ 验证码发送成功');

      // 模拟验证码验证（在实际环境中需要真实的验证码）
      addTestResult('⚠️ 注意：在实际环境中需要真实的验证码');
      addTestResult('💡 可以在后端日志中查看验证码，或使用测试邮箱');

    } catch (error: any) {
      if (error.response?.data?.message?.includes('邮箱已被使用')) {
        addTestResult('⚠️ 邮箱已被使用，这是正常的测试结果');
      } else {
        addTestResult(`❌ 邮箱绑定测试出错: ${error.response?.data?.message || error.message}`);
      }
    } finally {
      setIsLoading(false);
    }
  };

  // 模拟邮箱验证成功
  const simulateEmailVerification = async () => {
    setIsLoading(true);
    addTestResult('🧪 模拟邮箱验证成功...');

    try {
      if (!user) {
        addTestResult('❌ 请先创建用户');
        return;
      }

      const oldCoins = user.coins || 0;
      addTestResult(`💰 验证前金币: ${oldCoins}`);

      // 这里应该调用真实的验证接口，但为了测试我们模拟结果
      addTestResult('⚠️ 这是模拟测试，实际需要真实的验证码');
      addTestResult('💡 预期结果: 获得100金币（不再赠送VIP）');
      addTestResult(`💡 预期金币: ${oldCoins} + 100 = ${oldCoins + 100}`);

    } catch (error) {
      addTestResult(`❌ 模拟验证出错: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  // 检查当前用户状态
  const checkCurrentUserStatus = () => {
    addTestResult('🔍 检查当前用户状态...');
    
    if (user) {
      addTestResult(`👤 用户ID: ${user.id}`);
      addTestResult(`📧 邮箱: ${user.email || '未绑定'}`);
      addTestResult(`✅ 邮箱验证: ${user.emailVerified ? '已验证' : '未验证'}`);
      addTestResult(`💰 金币余额: ${user.coins}`);
      addTestResult(`👑 VIP状态: ${user.isVip ? '是' : '否'}`);
      if (user.vipExpireAt) {
        addTestResult(`⏰ VIP到期: ${new Date(user.vipExpireAt).toLocaleString()}`);
      }
      addTestResult(`🎮 游客模式: ${user.isGuest ? '是' : '否'}`);
    } else {
      addTestResult('❌ 当前无用户登录');
    }
  };

  // 清除测试数据
  const clearTestData = () => {
    try {
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      localStorage.removeItem('accountSetup');
      
      // 重置认证状态
      useAuthStore.setState({
        user: null,
        token: null,
        isAuthenticated: false,
        isGuest: true,
      });

      addTestResult('🧹 测试数据已清除');
      addTestResult('🔄 请刷新页面重新开始测试');
    } catch (error) {
      addTestResult(`❌ 清除数据失败: ${error}`);
    }
  };

  // 运行完整测试套件
  const runFullTestSuite = async () => {
    setTestResults([]);
    addTestResult('🚀 开始运行完整测试套件...');
    
    // 1. 检查当前状态
    checkCurrentUserStatus();
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 2. 测试新用户创建
    await testNewUserCreation();
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 3. 测试邮箱绑定
    await testEmailBindingReward();
    
    addTestResult('🎉 完整测试套件运行完成');
    addTestResult('💡 请查看后端日志获取验证码进行真实测试');
  };

  useEffect(() => {
    checkCurrentUserStatus();
  }, [user]);

  return (
    <MobileLayout title="金币奖励测试" showBack>
      <div className="p-4 space-y-6">
        {/* 当前用户状态 */}
        <div className="bg-white rounded-lg p-4 shadow-sm">
          <h2 className="text-lg font-semibold mb-4 flex items-center">
            <User className="w-5 h-5 mr-2" />
            当前用户状态
          </h2>
          
          {user ? (
            <div className="space-y-3 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">用户ID：</span>
                <span className="font-medium">{user.id}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">邮箱：</span>
                <span className="font-medium">{user.email || '未绑定'}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">邮箱验证：</span>
                <span className={`font-medium ${user.emailVerified ? 'text-green-600' : 'text-red-600'}`}>
                  {user.emailVerified ? '✅ 已验证' : '❌ 未验证'}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">金币余额：</span>
                <span className="font-medium text-yellow-600 flex items-center">
                  <Coins className="w-4 h-4 mr-1" />
                  {user.coins}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">VIP状态：</span>
                <span className={`font-medium ${user.isVip ? 'text-purple-600' : 'text-gray-600'}`}>
                  {user.isVip ? '👑 VIP用户' : '普通用户'}
                </span>
              </div>
              {user.vipExpireAt && (
                <div className="flex justify-between">
                  <span className="text-gray-600">VIP到期：</span>
                  <span className="text-xs">{new Date(user.vipExpireAt).toLocaleString()}</span>
                </div>
              )}
            </div>
          ) : (
            <div className="text-gray-500">当前无用户登录</div>
          )}
        </div>

        {/* 测试控制面板 */}
        <div className="bg-white rounded-lg p-4 shadow-sm">
          <h3 className="text-lg font-semibold mb-4 flex items-center">
            <Gift className="w-5 h-5 mr-2" />
            测试控制面板
          </h3>
          
          <div className="grid grid-cols-2 gap-3 mb-4">
            <button
              onClick={runFullTestSuite}
              disabled={isLoading}
              className="bg-blue-500 text-white px-4 py-2 rounded-lg disabled:opacity-50 text-sm"
            >
              {isLoading ? '测试中...' : '完整测试'}
            </button>
            
            <button
              onClick={checkCurrentUserStatus}
              disabled={isLoading}
              className="bg-green-500 text-white px-4 py-2 rounded-lg disabled:opacity-50 text-sm"
            >
              检查状态
            </button>
            
            <button
              onClick={testNewUserCreation}
              disabled={isLoading}
              className="bg-purple-500 text-white px-4 py-2 rounded-lg disabled:opacity-50 text-sm"
            >
              测试新用户
            </button>
            
            <button
              onClick={testEmailBindingReward}
              disabled={isLoading}
              className="bg-orange-500 text-white px-4 py-2 rounded-lg disabled:opacity-50 text-sm"
            >
              测试邮箱绑定
            </button>
          </div>

          <div className="grid grid-cols-2 gap-3">
            <button
              onClick={simulateEmailVerification}
              disabled={isLoading}
              className="bg-yellow-500 text-white px-4 py-2 rounded-lg disabled:opacity-50 text-sm"
            >
              模拟验证成功
            </button>
            
            <button
              onClick={clearTestData}
              className="bg-red-500 text-white px-4 py-2 rounded-lg text-sm"
            >
              清除测试数据
            </button>
          </div>
        </div>

        {/* 测试结果 */}
        <div className="bg-white rounded-lg p-4 shadow-sm">
          <h3 className="text-lg font-semibold mb-3">测试结果</h3>
          <div className="space-y-1 max-h-64 overflow-y-auto">
            {testResults.length > 0 ? (
              testResults.map((result, index) => (
                <div key={index} className="text-xs font-mono p-2 bg-gray-50 rounded">
                  {result}
                </div>
              ))
            ) : (
              <div className="text-gray-500 text-sm">暂无测试结果</div>
            )}
          </div>
        </div>

        {/* 测试说明 */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="text-lg font-semibold text-blue-800 mb-2 flex items-center">
            <AlertCircle className="w-5 h-5 mr-2" />
            📋 测试说明
          </h3>
          <ul className="text-sm text-blue-700 space-y-1">
            <li>• <strong>新用户测试</strong>：验证新用户默认金币为0</li>
            <li>• <strong>首次绑定测试</strong>：验证首次绑定邮箱获得100金币</li>
            <li>• <strong>账号合并测试</strong>：验证邮箱已被绑定时的合并逻辑（不给奖励）</li>
            <li>• <strong>防刷机制测试</strong>：验证通过合并账号无法刷取金币</li>
            <li>• <strong>注意</strong>：真实测试需要使用有效的邮箱验证码</li>
          </ul>
        </div>

        {/* 预期结果 */}
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <h3 className="text-lg font-semibold text-green-800 mb-2 flex items-center">
            <CheckCircle className="w-5 h-5 mr-2" />
            ✅ 预期结果
          </h3>
          <ul className="text-sm text-green-700 space-y-1">
            <li>• 新用户创建时金币余额为 <strong>0</strong></li>
            <li>• 首次绑定邮箱后金币余额增加 <strong>100</strong></li>
            <li>• 账号合并时只保留较大金币数量，<strong>不额外给100金币</strong></li>
            <li>• 邮箱已被绑定过的情况下，合并账号不会获得奖励</li>
            <li>• 防止通过创建多个游客账号刷取金币的漏洞</li>
          </ul>
        </div>
      </div>
    </MobileLayout>
  );
};

export default CoinRewardTestPage;
