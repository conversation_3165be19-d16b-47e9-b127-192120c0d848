import React, { useState } from 'react';
import MobileLayout from '../components/MobileLayout';
import PurchaseModal from '../components/PurchaseModal';
import { useAuthStore } from '../store/authStore';
import { Smartphone, Tablet, Monitor, User, Mail, Coins, Crown } from 'lucide-react';

const PurchaseModalTestPage: React.FC = () => {
  const { user } = useAuthStore();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [testScenario, setTestScenario] = useState<'sufficient' | 'insufficient' | 'no-email'>('insufficient');

  // 模拟视频数据
  const mockVideo = {
    id: 1,
    title: '测试视频 - 购买弹框演示',
    price: 50,
    hasPurchased: false,
    thumbnail: '',
    duration: 1800,
    category: '测试',
    description: '这是一个用于测试购买弹框的模拟视频',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };

  const handlePurchase = async (video: any) => {
    console.log('购买视频:', video);
    alert(`模拟购买视频: ${video.title}`);
    setIsModalOpen(false);
  };

  const openModal = (scenario: 'sufficient' | 'insufficient' | 'no-email') => {
    setTestScenario(scenario);
    setIsModalOpen(true);
  };

  // 根据测试场景模拟用户状态
  const getMockUser = () => {
    const baseUser = user || {
      id: 1,
      nickname: '测试用户',
      coins: 0,
      isVip: false,
      email: null,
      emailVerified: false,
      isGuest: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    switch (testScenario) {
      case 'sufficient':
        return { ...baseUser, coins: 100, email: '<EMAIL>' };
      case 'insufficient':
        return { ...baseUser, coins: 20, email: '<EMAIL>' };
      case 'no-email':
        return { ...baseUser, coins: 20, email: null };
      default:
        return baseUser;
    }
  };

  // 临时替换用户状态用于测试
  const mockUser = getMockUser();

  return (
    <MobileLayout title="购买弹框测试" showBack>
      <div className="p-4 space-y-6">
        {/* 功能说明 */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h2 className="text-lg font-semibold text-blue-800 mb-2 flex items-center">
            <Smartphone className="w-5 h-5 mr-2" />
            移动应用风格购买弹框
          </h2>
          <p className="text-sm text-blue-700">
            重新设计的购买弹框，采用移动应用风格的底部弹窗设计，并增加邮箱绑定奖励提示。
          </p>
        </div>

        {/* 当前用户状态 */}
        <div className="bg-white rounded-lg p-4 shadow-sm">
          <h3 className="text-lg font-semibold mb-3 flex items-center">
            <User className="w-5 h-5 mr-2" />
            当前用户状态
          </h3>
          <div className="space-y-2 text-sm">
            <div className="flex items-center justify-between">
              <span className="text-gray-600">用户昵称:</span>
              <span className="font-medium">{user?.nickname || '测试用户'}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-600">金币余额:</span>
              <span className="font-medium text-blue-600 flex items-center">
                <Coins className="w-4 h-4 mr-1" />
                {user?.coins || 0}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-600">VIP状态:</span>
              <span className={`font-medium flex items-center ${user?.isVip ? 'text-yellow-600' : 'text-gray-500'}`}>
                <Crown className="w-4 h-4 mr-1" />
                {user?.isVip ? '已开通' : '未开通'}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-600">邮箱状态:</span>
              <span className={`font-medium flex items-center ${user?.email ? 'text-green-600' : 'text-orange-600'}`}>
                <Mail className="w-4 h-4 mr-1" />
                {user?.email ? '已绑定' : '未绑定'}
              </span>
            </div>
          </div>
        </div>

        {/* 测试场景 */}
        <div className="bg-white rounded-lg p-4 shadow-sm">
          <h3 className="text-lg font-semibold mb-4">测试场景</h3>
          
          <div className="space-y-3">
            {/* 余额充足场景 */}
            <button
              onClick={() => openModal('sufficient')}
              className="w-full p-4 bg-green-50 border border-green-200 rounded-lg text-left hover:bg-green-100 transition-colors"
            >
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium text-green-800">余额充足</h4>
                  <p className="text-sm text-green-600">金币: 100 | 视频价格: 50 | 已绑定邮箱</p>
                </div>
                <div className="text-green-600">
                  <Coins className="w-5 h-5" />
                </div>
              </div>
            </button>

            {/* 余额不足但已绑定邮箱场景 */}
            <button
              onClick={() => openModal('insufficient')}
              className="w-full p-4 bg-orange-50 border border-orange-200 rounded-lg text-left hover:bg-orange-100 transition-colors"
            >
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium text-orange-800">余额不足（已绑定邮箱）</h4>
                  <p className="text-sm text-orange-600">金币: 20 | 视频价格: 50 | 已绑定邮箱</p>
                </div>
                <div className="text-orange-600">
                  <Mail className="w-5 h-5" />
                </div>
              </div>
            </button>

            {/* 余额不足且未绑定邮箱场景 */}
            <button
              onClick={() => openModal('no-email')}
              className="w-full p-4 bg-red-50 border border-red-200 rounded-lg text-left hover:bg-red-100 transition-colors"
            >
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium text-red-800">余额不足（未绑定邮箱）</h4>
                  <p className="text-sm text-red-600">金币: 20 | 视频价格: 50 | 未绑定邮箱</p>
                  <p className="text-xs text-red-500 mt-1">💡 会显示邮箱绑定奖励提示</p>
                </div>
                <div className="text-red-600">
                  <User className="w-5 h-5" />
                </div>
              </div>
            </button>
          </div>
        </div>

        {/* 设计特点 */}
        <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
          <h3 className="text-lg font-semibold text-purple-800 mb-3">新设计特点</h3>
          <div className="space-y-2 text-sm text-purple-700">
            <div className="flex items-start">
              <span className="w-2 h-2 bg-purple-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
              <span><strong>底部弹窗：</strong>采用移动应用常见的底部滑入弹窗</span>
            </div>
            <div className="flex items-start">
              <span className="w-2 h-2 bg-purple-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
              <span><strong>手势交互：</strong>支持下拉手势关闭</span>
            </div>
            <div className="flex items-start">
              <span className="w-2 h-2 bg-purple-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
              <span><strong>渐变设计：</strong>使用现代化的渐变色彩和圆角设计</span>
            </div>
            <div className="flex items-start">
              <span className="w-2 h-2 bg-purple-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
              <span><strong>邮箱奖励：</strong>未绑定邮箱时显示100金币+VIP奖励提示</span>
            </div>
            <div className="flex items-start">
              <span className="w-2 h-2 bg-purple-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
              <span><strong>触摸优化：</strong>按钮点击有缩放反馈，提升触摸体验</span>
            </div>
            <div className="flex items-start">
              <span className="w-2 h-2 bg-purple-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
              <span><strong>图标丰富：</strong>使用Lucide图标增强视觉效果</span>
            </div>
          </div>
        </div>

        {/* 手势交互说明 */}
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <h3 className="text-lg font-semibold text-green-800 mb-3">手势交互</h3>
          <div className="space-y-3">
            <div className="bg-white rounded-lg p-3">
              <h4 className="font-medium text-green-800 mb-2">👆 下拉关闭</h4>
              <p className="text-sm text-green-700">
                在弹窗顶部拖拽指示器或任意位置按住并向下拖拽超过100px即可关闭弹窗
              </p>
            </div>

            <div className="bg-white rounded-lg p-3">
              <h4 className="font-medium text-green-800 mb-2">📱 移动端优化</h4>
              <p className="text-sm text-green-700">
                支持触摸和鼠标操作，拖拽时有透明度反馈，松手时自动回弹或关闭
              </p>
            </div>
          </div>
        </div>

        {/* 响应式测试 */}
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
          <h3 className="text-lg font-semibold text-gray-800 mb-3">响应式适配</h3>
          <div className="grid grid-cols-3 gap-3 text-center text-sm">
            <div className="flex flex-col items-center p-3 bg-white rounded-lg">
              <Smartphone className="w-6 h-6 text-blue-500 mb-2" />
              <span className="font-medium">手机</span>
              <span className="text-xs text-gray-500">&lt; 768px</span>
            </div>
            <div className="flex flex-col items-center p-3 bg-white rounded-lg">
              <Tablet className="w-6 h-6 text-green-500 mb-2" />
              <span className="font-medium">平板</span>
              <span className="text-xs text-gray-500">768px+</span>
            </div>
            <div className="flex flex-col items-center p-3 bg-white rounded-lg">
              <Monitor className="w-6 h-6 text-purple-500 mb-2" />
              <span className="font-medium">桌面</span>
              <span className="text-xs text-gray-500">1024px+</span>
            </div>
          </div>
        </div>

        {/* 邮箱绑定奖励说明 */}
        <div className="bg-gradient-to-r from-orange-50 to-yellow-50 border border-orange-200 rounded-lg p-4">
          <h3 className="text-lg font-semibold text-orange-800 mb-2 flex items-center">
            <Mail className="w-5 h-5 mr-2" />
            邮箱绑定奖励
          </h3>
          <div className="space-y-2 text-sm text-orange-700">
            <p><strong>触发条件：</strong>用户未绑定邮箱 且 金币余额不足购买视频</p>
            <p><strong>奖励内容：</strong>100金币 + 1天VIP会员</p>
            <p><strong>显示位置：</strong>购买弹框中的专门提示区域</p>
            <p><strong>操作按钮：</strong>直接跳转到邮箱绑定页面</p>
          </div>
        </div>
      </div>

      {/* 购买弹框 */}
      <PurchaseModal
        isOpen={isModalOpen}
        video={mockVideo}
        onClose={() => setIsModalOpen(false)}
        onPurchase={handlePurchase}
        previewDuration={1}
        mockUser={mockUser}
      />

      {/* 临时覆盖用户状态用于测试 */}
      <style>{`
        /* 确保弹窗在最顶层 */
        .fixed.inset-0.z-50 {
          z-index: 9999;
        }
      `}</style>
    </MobileLayout>
  );
};

export default PurchaseModalTestPage;
