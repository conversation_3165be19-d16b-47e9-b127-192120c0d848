import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuthStore } from '../store/authStore';
import { unifiedApiService } from '../services/unifiedApiService';
import MobileLayout from '../components/MobileLayout';

const InvitePage: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuthStore();
  const [loading, setLoading] = useState(true);
  const [inviteData, setInviteData] = useState<any>(null);
  const [copied, setCopied] = useState(false);

  useEffect(() => {
    loadInviteData();
  }, []);

  const loadInviteData = async () => {
    try {
      setLoading(true);

      // 直接调用API获取邀请信息
      const data = await unifiedApiService.getInviteInfo();
      console.log('� 获取邀请信息:', data);
      setInviteData(data);
    } catch (error) {
      console.error('加载邀请数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const copyInviteLink = async () => {
    // 检查邮箱绑定状态
    if (!user?.email) {
      navigate('/link-email');
      return;
    }

    try {
      const linkData = await unifiedApiService.getInviteLink();
      await navigator.clipboard.writeText(linkData.inviteLink);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('复制邀请链接失败:', error);
    }
  };

  const shareInviteLink = async () => {
    // 检查邮箱绑定状态
    if (!user?.email) {
      navigate('/link-email');
      return;
    }

    try {
      const linkData = await unifiedApiService.getInviteLink();
      if (navigator.share) {
        await navigator.share({
          title: '推荐您加入视频平台',
          text: '注册即可获得免费VIP会员，还有更多精彩内容等您发现！',
          url: linkData.inviteLink,
        });
      } else {
        // 降级到复制链接
        await copyInviteLink();
      }
    } catch (error) {
      console.error('分享邀请链接失败:', error);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">加载中...</p>
        </div>
      </div>
    );
  }

  const stats = inviteData?.stats || {};
  const commissionRates = [
    { level: 1, rate: '50%', description: '直接邀请' },
    { level: 2, rate: '20%', description: '二级邀请' },
    { level: 3, rate: '10%', description: '三级邀请' },
    { level: 4, rate: '5%', description: '四级邀请' },
    { level: 5, rate: '2%', description: '五级邀请' },
  ];

  // 如果用户未绑定邮箱，显示邮箱绑定提示
  if (!user?.email) {
    return (
      <MobileLayout
        title="推广赚钱"
        showBack={true}
        showTabBar={false}
      >
        <div className="p-4 md:p-6">
          {/* 邮箱绑定提示卡片 */}
          <div className="bg-gradient-to-r from-orange-500 to-red-500 rounded-lg p-6 md:p-8 text-white">
            <div className="text-center mb-6">
              <div className="text-4xl mb-4">🔒</div>
              <h2 className="text-xl md:text-2xl font-bold mb-2">需要绑定邮箱</h2>
              <p className="text-orange-100 text-sm md:text-base leading-relaxed">
                为了确保推广系统的安全性和防止恶意推广，<br/>
                使用推广功能前需要先绑定邮箱验证身份
              </p>
            </div>

            <div className="bg-white/20 rounded-lg p-4 mb-6">
              <h3 className="font-semibold mb-3">绑定邮箱后可享受：</h3>
              <ul className="space-y-2 text-sm">
                <li className="flex items-center space-x-2">
                  <span>✅</span>
                  <span>生成专属推广码和推广链接</span>
                </li>
                <li className="flex items-center space-x-2">
                  <span>✅</span>
                  <span>用户绑定邮箱获得3天VIP奖励</span>
                </li>
                <li className="flex items-center space-x-2">
                  <span>✅</span>
                  <span>用户充值获得返佣奖励</span>
                </li>
                <li className="flex items-center space-x-2">
                  <span>✅</span>
                  <span>查看推广记录和收益明细</span>
                </li>
              </ul>
            </div>

            <button
              onClick={() => navigate('/link-email')}
              className="w-full bg-white text-orange-600 py-3 px-6 rounded-lg font-semibold hover:bg-gray-100 transition-colors active:scale-95"
            >
              立即绑定邮箱
            </button>
          </div>

          {/* 推广规则预览 */}
          <div className="mt-6 bg-white rounded-lg p-4">
            <h3 className="font-semibold text-gray-900 mb-3 flex items-center">
              <span className="text-blue-600 mr-2">📋</span>
              推广规则预览
            </h3>
            <div className="space-y-2">
              {[
                { level: 1, rate: '50%', description: '直接推广' },
                { level: 2, rate: '20%', description: '二级推广' },
                { level: 3, rate: '10%', description: '三级推广' },
                { level: 4, rate: '5%', description: '四级推广' },
                { level: 5, rate: '2%', description: '五级推广' },
              ].map((item) => (
                <div key={item.level} className="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-bold">
                      {item.level}
                    </div>
                    <span className="text-gray-700">{item.description}</span>
                  </div>
                  <span className="font-semibold text-blue-600">{item.rate}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </MobileLayout>
    );
  }

  return (
    <MobileLayout
      title="推广赚钱"
      showBack={true}
      showTabBar={false}
    >
      <div className="p-4 md:p-6 space-y-4 md:space-y-6">
        {/* 推广概览卡片 */}
        <div className="bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg p-6 md:p-8 text-white">
          <div className="text-center mb-4 md:mb-6">
            <div className="text-3xl md:text-4xl mb-2">💰</div>
            <h2 className="text-xl md:text-2xl font-bold mb-2">推广赚钱</h2>
            <p className="text-blue-100 text-sm md:text-base">
              用户绑定邮箱，您获得3天VIP<br/>
              用户充值消费，您获得返佣奖励
            </p>
          </div>

          <div className="grid grid-cols-2 gap-4 text-center">
            <div className="bg-white/20 rounded-lg p-3">
              <div className="text-2xl font-bold">{stats.totalInvites || 0}</div>
              <div className="text-sm text-blue-100">累计推广</div>
            </div>
            <div className="bg-white/20 rounded-lg p-3">
              <div className="text-2xl font-bold">{stats.totalCommission || 0}</div>
              <div className="text-sm text-blue-100">累计返佣</div>
            </div>
          </div>
        </div>

        {/* 推广操作 */}
        <div className="bg-white rounded-lg p-4 md:p-6 space-y-3 md:space-y-4">
          <h3 className="font-semibold text-gray-900 mb-3 md:text-lg">分享推广</h3>

          <div className="flex space-x-3 md:space-x-4">
            <button
              onClick={copyInviteLink}
              className="flex-1 bg-blue-600 text-white py-3 md:py-4 px-4 md:px-6 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center space-x-2 md:text-base"
            >
              <span>📋</span>
              <span>{copied ? '已复制' : '复制推广链接'}</span>
            </button>

            <button
              onClick={shareInviteLink}
              className="flex-1 bg-green-600 text-white py-3 md:py-4 px-4 md:px-6 rounded-lg hover:bg-green-700 transition-colors flex items-center justify-center space-x-2 md:text-base"
            >
              <span>📤</span>
              <span>分享推广</span>
            </button>
          </div>

          <div className="text-center">
            <p className="text-sm text-gray-600 mb-2">我的推广码</p>
            <div className="bg-gray-100 rounded-lg p-3 font-mono text-lg font-bold text-center">
              {inviteData?.inviteCode || '加载中...'}
            </div>
          </div>
        </div>

        {/* 返佣规则 */}
        <div className="bg-white rounded-lg p-4">
          <h3 className="font-semibold text-gray-900 mb-3">返佣规则</h3>
          <div className="space-y-2">
            {commissionRates.map((item) => (
              <div key={item.level} className="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-bold">
                    {item.level}
                  </div>
                  <span className="text-gray-700">{item.description}</span>
                </div>
                <span className="font-semibold text-blue-600">{item.rate}</span>
              </div>
            ))}
          </div>
        </div>

        {/* 奖励说明 */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <h3 className="font-semibold text-gray-900 mb-2 flex items-center">
            <span className="text-yellow-600 mr-2">💡</span>
            奖励说明
          </h3>
          <ul className="text-sm text-gray-700 space-y-1">
            <li>• 用户通过您的推广码注册并绑定邮箱，您获得3天VIP会员</li>
            <li>• 用户充值时，您获得对应比例的金币返佣</li>
            <li>• 支持五级返利，推广的用户再推广其他人，您也能获得返佣</li>
            <li>• 返佣金币实时到账，可用于购买VIP或观看付费视频</li>
          </ul>
        </div>

        {/* 推广统计 */}
        {stats.invitesByLevel && (
          <div className="bg-white rounded-lg p-4">
            <h3 className="font-semibold text-gray-900 mb-3">推广统计</h3>
            <div className="grid grid-cols-5 gap-2 text-center">
              {[1, 2, 3, 4, 5].map((level) => (
                <div key={level} className="bg-gray-50 rounded-lg p-2">
                  <div className="text-lg font-bold text-blue-600">
                    {stats.invitesByLevel[`level${level}`] || 0}
                  </div>
                  <div className="text-xs text-gray-600">{level}级</div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 快捷操作 */}
        <div className="grid grid-cols-2 gap-3">
          <button
            onClick={() => navigate('/invite/list')}
            className="bg-white border border-gray-200 rounded-lg p-4 text-center hover:bg-gray-50 transition-colors"
          >
            <div className="text-2xl mb-2">👥</div>
            <div className="font-semibold text-gray-900">推广记录</div>
            <div className="text-sm text-gray-600">查看推广详情</div>
          </button>
          
          <button
            onClick={() => navigate('/invite/commissions')}
            className="bg-white border border-gray-200 rounded-lg p-4 text-center hover:bg-gray-50 transition-colors"
          >
            <div className="text-2xl mb-2">💰</div>
            <div className="font-semibold text-gray-900">返佣记录</div>
            <div className="text-sm text-gray-600">查看收益明细</div>
          </button>
        </div>
      </div>
    </MobileLayout>
  );
};

export default InvitePage;
