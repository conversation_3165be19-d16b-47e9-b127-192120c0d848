import React, { useEffect, useState, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { unifiedApiService } from '../services/unifiedApiService';
import type { Order } from '../types';

const OrdersPage: React.FC = () => {
  const navigate = useNavigate();
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const observerRef = useRef<IntersectionObserver | null>(null);
  const loadMoreRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    loadOrders(1, true);
  }, []);

  // 设置无限滚动
  useEffect(() => {
    if (observerRef.current) {
      observerRef.current.disconnect();
    }

    observerRef.current = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && hasMore && !loadingMore && !loading) {
          loadOrders(currentPage + 1);
        }
      },
      { threshold: 0.1 }
    );

    if (loadMoreRef.current) {
      observerRef.current.observe(loadMoreRef.current);
    }

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [hasMore, loadingMore, loading, currentPage]);

  const loadOrders = async (page: number = currentPage, reset: boolean = false) => {
    try {
      if (reset) {
        setLoading(true);
      } else {
        setLoadingMore(true);
      }

      const response = await unifiedApiService.getUserOrders(page, 12);
      
      if (reset) {
        setOrders(response.orders);
      } else {
        setOrders(prev => [...prev, ...response.orders]);
      }
      
      setHasMore(response.orders.length === 12);
      setCurrentPage(page);
    } catch (error) {
      console.error('Failed to load orders:', error);
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  };

  const getOrderStatusText = (status: string) => {
    switch (status) {
      case 'pending': return '待支付';
      case 'paid': return '已支付';
      case 'cancelled': return '已取消';
      case 'refunded': return '已退款';
      default: return status;
    }
  };

  const getOrderTypeText = (type: string) => {
    switch (type) {
      case 'video_purchase': return '视频购买';
      case 'coin_recharge': return '金币充值';
      case 'vip_purchase': return 'VIP购买';
      default: return type;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'cancelled': return 'bg-gray-100 text-gray-800';
      case 'refunded': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN');
  };

  const handleOrderClick = (order: Order) => {
    // 如果是视频购买订单且有视频信息，跳转到视频详情页
    if (order.type === 'video_purchase' && order.video?.id) {
      navigate(`/video/${order.video.id}`);
    }
    // 其他类型的订单暂时不处理，可以后续扩展
  };

  const getOrderDescription = (order: Order) => {
    switch (order.type) {
      case 'video_purchase':
        return order.video?.title || '视频购买';
      case 'coin_recharge':
        return `充值 ${order.coins} 金币`;
      case 'vip_purchase':
        return `VIP会员 (${order.vipDuration}天)`;
      default:
        return '未知订单';
    }
  };

  return (
    <div className="bg-gray-50 min-h-screen">
      <div className="p-3">
        {loading ? (
          <div className="flex items-center justify-center py-20">
            <div className="loading-spinner"></div>
            <span className="ml-3 text-gray-600">加载中...</span>
          </div>
        ) : orders.length > 0 ? (
          <>
            <div className="space-y-3">
              {orders.map((order) => (
                <div
                  key={order.id}
                  className={`bg-white rounded-xl p-4 shadow-sm ${
                    order.type === 'video_purchase' && order.video?.id
                      ? 'cursor-pointer hover:shadow-md transition-shadow active:scale-[0.98]'
                      : ''
                  }`}
                  onClick={() => handleOrderClick(order)}
                >
                  <div className="flex items-center justify-between mb-3">
                    <span className="text-sm text-gray-500">订单号: {order.orderNo}</span>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}>
                      {getOrderStatusText(order.status)}
                    </span>
                  </div>

                  {/* 订单详情 */}
                  <div className="space-y-2">
                    <div className="flex items-start justify-between">
                      <span className="text-gray-600 text-sm">详情</span>
                      <div className="flex-1 ml-4">
                        <div className="text-right">
                          <span className="font-medium text-gray-900">{getOrderDescription(order)}</span>
                          {order.type === 'video_purchase' && order.video?.id && (
                            <div className="flex items-center justify-end mt-1">
                              <span className="text-xs text-blue-600">点击查看视频</span>
                              <svg className="w-3 h-3 ml-1 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                              </svg>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-gray-600 text-sm">类型</span>
                      <span className="font-medium">{getOrderTypeText(order.type)}</span>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-gray-600 text-sm">金额</span>
                      <span className="font-medium text-pink-600">¥{order.amount}</span>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-gray-600 text-sm">创建时间</span>
                      <span className="text-sm">{formatDate(order.createdAt)}</span>
                    </div>

                    {order.paidAt && (
                      <div className="flex items-center justify-between">
                        <span className="text-gray-600 text-sm">支付时间</span>
                        <span className="text-sm">{formatDate(order.paidAt)}</span>
                      </div>
                    )}
                  </div>

                  {order.status === 'pending' && (
                    <div className="mt-3 pt-3 border-t border-gray-100">
                      <button
                        className="w-full mobile-btn-primary py-2 text-sm"
                        onClick={(e) => {
                          e.stopPropagation(); // 防止触发订单点击事件
                          // TODO: 处理继续支付逻辑
                        }}
                      >
                        继续支付
                      </button>
                    </div>
                  )}
                </div>
              ))}
            </div>

            {/* 无限滚动触发器 */}
            {hasMore && (
              <div 
                ref={loadMoreRef}
                className="flex justify-center mt-6 py-4"
              >
                {loadingMore && (
                  <div className="flex items-center">
                    <div className="loading-spinner mr-2"></div>
                    <span className="text-gray-600">加载中...</span>
                  </div>
                )}
              </div>
            )}

            {/* 没有更多内容提示 */}
            {!hasMore && orders.length > 0 && (
              <div className="text-center py-8">
                <p className="text-gray-500 text-sm">没有更多内容了</p>
              </div>
            )}
          </>
        ) : (
          <div className="text-center py-20">
            <div className="text-6xl mb-4">📋</div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">暂无订单记录</h2>
            <p className="text-gray-600 mb-6">还没有任何订单</p>
            <button
              onClick={() => navigate('/recharge')}
              className="mobile-btn-primary px-6 py-3"
            >
              去充值
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default OrdersPage;
