import React, { useState } from 'react';
import MobileLayout from '../components/MobileLayout';
import { unifiedApiService } from '../services/unifiedApiService';
import { Shield, AlertTriangle, RefreshCw, Database, Trash2, TestTube } from 'lucide-react';

const DecryptionErrorTestPage: React.FC = () => {
  const [testResults, setTestResults] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const addTestResult = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setTestResults(prev => [`[${timestamp}] ${message}`, ...prev.slice(0, 19)]);
  };

  // 模拟解密错误
  const simulateDecryptionError = () => {
    addTestResult('🧪 模拟解密错误...');
    
    // 创建模拟的解密错误数据
    const mockDecryptionError = {
      success: false,
      message: "请求数据解密失败",
      code: "DECRYPTION_ERROR",
      details: "无效的会话密钥"
    };

    // 触发解密错误事件
    window.dispatchEvent(new CustomEvent('decryptionError', {
      detail: mockDecryptionError
    }));

    addTestResult('✅ 解密错误事件已触发');
    addTestResult('💡 应该会弹出重启应用对话框');
  };

  // 测试正常API请求
  const testNormalApiRequest = async () => {
    setIsLoading(true);
    addTestResult('🧪 测试正常API请求...');

    try {
      const userInfo = await unifiedApiService.getUserInfo();
      addTestResult('✅ 正常API请求成功');
      addTestResult(`👤 用户信息: ${userInfo.nickname || '未知'}`);
    } catch (error: any) {
      addTestResult(`❌ API请求失败: ${error.message}`);
      if (error.response?.data?.code === 'DECRYPTION_ERROR') {
        addTestResult('🔐 检测到真实的解密错误');
      }
    } finally {
      setIsLoading(false);
    }
  };

  // 测试加密系统状态
  const testEncryptionStatus = async () => {
    setIsLoading(true);
    addTestResult('🧪 检查加密系统状态...');

    try {
      // 检查本地存储的加密相关数据
      const publicKey = localStorage.getItem('publicKey');
      const keyTimestamp = localStorage.getItem('keyTimestamp');
      const encryptedRequestsCount = localStorage.getItem('encryptedRequestsCount');

      addTestResult(`🔑 公钥状态: ${publicKey ? '存在' : '不存在'}`);
      addTestResult(`⏰ 密钥时间戳: ${keyTimestamp || '无'}`);
      addTestResult(`📊 加密请求计数: ${encryptedRequestsCount || '0'}`);

      if (keyTimestamp) {
        const keyAge = Date.now() - parseInt(keyTimestamp);
        const keyAgeMinutes = Math.floor(keyAge / (1000 * 60));
        addTestResult(`🕐 密钥年龄: ${keyAgeMinutes}分钟`);
      }

    } catch (error) {
      addTestResult(`❌ 检查加密状态失败: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  // 清除加密相关数据
  const clearEncryptionData = () => {
    addTestResult('🧹 清除加密相关数据...');

    try {
      localStorage.removeItem('publicKey');
      localStorage.removeItem('keyExchange');
      localStorage.removeItem('keyTimestamp');
      localStorage.removeItem('encryptedRequestsCount');
      localStorage.removeItem('lastEncryptTime');

      addTestResult('✅ 加密数据已清除');
      addTestResult('💡 下次API请求将重新初始化加密');
    } catch (error) {
      addTestResult(`❌ 清除加密数据失败: ${error}`);
    }
  };

  // 手动触发数据清除
  const manualClearAllData = async () => {
    setIsLoading(true);
    addTestResult('🧪 手动清除所有数据...');

    try {
      // 清除认证相关数据
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      localStorage.removeItem('accountSetup');

      // 清除设备相关数据
      localStorage.removeItem('deviceId');
      localStorage.removeItem('device_manager_info');
      localStorage.removeItem('secure_device_info');
      localStorage.removeItem('device_id_registry');

      // 清除加密相关数据
      localStorage.removeItem('publicKey');
      localStorage.removeItem('keyExchange');
      localStorage.removeItem('keyTimestamp');
      localStorage.removeItem('encryptedRequestsCount');
      localStorage.removeItem('lastEncryptTime');

      // 清除缓存数据
      await unifiedApiService.clearAllCache();

      addTestResult('✅ 所有数据已清除');
      addTestResult('🔄 3秒后自动刷新页面...');

      setTimeout(() => {
        window.location.reload();
      }, 3000);

    } catch (error) {
      addTestResult(`❌ 清除数据失败: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  // 运行完整测试套件
  const runFullTestSuite = async () => {
    setTestResults([]);
    addTestResult('🚀 开始运行解密错误测试套件...');

    // 1. 检查加密状态
    await testEncryptionStatus();
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 2. 测试正常请求
    await testNormalApiRequest();
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 3. 模拟解密错误
    simulateDecryptionError();

    addTestResult('🎉 测试套件运行完成');
  };

  return (
    <MobileLayout title="解密错误测试" showBack>
      <div className="p-4 space-y-6">
        {/* 功能说明 */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h2 className="text-lg font-semibold text-blue-800 mb-2 flex items-center">
            <Shield className="w-5 h-5 mr-2" />
            解密错误处理测试
          </h2>
          <p className="text-sm text-blue-700">
            测试当GET接口返回解密失败错误时，系统会弹出重启应用对话框。
          </p>
        </div>

        {/* 测试控制面板 */}
        <div className="bg-white rounded-lg p-4 shadow-sm">
          <h3 className="text-lg font-semibold mb-4 flex items-center">
            <TestTube className="w-5 h-5 mr-2" />
            测试控制面板
          </h3>
          
          <div className="grid grid-cols-2 gap-3 mb-4">
            <button
              onClick={runFullTestSuite}
              disabled={isLoading}
              className="bg-blue-500 text-white px-4 py-2 rounded-lg disabled:opacity-50 text-sm"
            >
              {isLoading ? '测试中...' : '完整测试'}
            </button>
            
            <button
              onClick={simulateDecryptionError}
              disabled={isLoading}
              className="bg-red-500 text-white px-4 py-2 rounded-lg disabled:opacity-50 text-sm"
            >
              模拟解密错误
            </button>
            
            <button
              onClick={testNormalApiRequest}
              disabled={isLoading}
              className="bg-green-500 text-white px-4 py-2 rounded-lg disabled:opacity-50 text-sm"
            >
              测试API请求
            </button>
            
            <button
              onClick={testEncryptionStatus}
              disabled={isLoading}
              className="bg-purple-500 text-white px-4 py-2 rounded-lg disabled:opacity-50 text-sm"
            >
              检查加密状态
            </button>
          </div>

          <div className="grid grid-cols-2 gap-3">
            <button
              onClick={clearEncryptionData}
              disabled={isLoading}
              className="bg-orange-500 text-white px-4 py-2 rounded-lg disabled:opacity-50 text-sm"
            >
              清除加密数据
            </button>
            
            <button
              onClick={manualClearAllData}
              disabled={isLoading}
              className="bg-red-600 text-white px-4 py-2 rounded-lg disabled:opacity-50 text-sm flex items-center justify-center"
            >
              {isLoading ? (
                <RefreshCw className="w-4 h-4 mr-1 animate-spin" />
              ) : (
                <Trash2 className="w-4 h-4 mr-1" />
              )}
              清除所有数据
            </button>
          </div>
        </div>

        {/* 测试结果 */}
        <div className="bg-white rounded-lg p-4 shadow-sm">
          <h3 className="text-lg font-semibold mb-3">测试结果</h3>
          <div className="space-y-1 max-h-64 overflow-y-auto">
            {testResults.length > 0 ? (
              testResults.map((result, index) => (
                <div key={index} className="text-xs font-mono p-2 bg-gray-50 rounded">
                  {result}
                </div>
              ))
            ) : (
              <div className="text-gray-500 text-sm">暂无测试结果</div>
            )}
          </div>
        </div>

        {/* 错误格式说明 */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <h3 className="text-lg font-semibold text-yellow-800 mb-2 flex items-center">
            <AlertTriangle className="w-5 h-5 mr-2" />
            解密错误格式
          </h3>
          <div className="text-sm text-yellow-700 space-y-2">
            <p><strong>触发条件：</strong>GET接口返回以下格式的错误</p>
            <div className="bg-yellow-100 p-3 rounded font-mono text-xs">
              {`{
  "success": false,
  "message": "请求数据解密失败",
  "code": "DECRYPTION_ERROR",
  "details": "无效的会话密钥"
}`}
            </div>
          </div>
        </div>

        {/* 处理流程说明 */}
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <h3 className="text-lg font-semibold text-green-800 mb-2 flex items-center">
            <Database className="w-5 h-5 mr-2" />
            处理流程
          </h3>
          <ol className="text-sm text-green-700 space-y-1 list-decimal list-inside">
            <li>API请求检测到解密错误</li>
            <li>触发全局解密错误事件</li>
            <li>显示重启应用对话框</li>
            <li>用户点击重启按钮</li>
            <li>清除本地数据并刷新应用</li>
          </ol>
        </div>

        {/* 清除的数据列表 */}
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
          <h3 className="text-lg font-semibold text-gray-800 mb-2">将清除的数据</h3>
          <div className="grid grid-cols-2 gap-2 text-sm text-gray-600">
            <div>• 登录状态</div>
            <div>• 用户信息</div>
            <div>• 设备ID</div>
            <div>• 加密密钥</div>
            <div>• API缓存</div>
            <div>• 观看历史</div>
            <div>• WebClip设置</div>
            <div>• 主题设置</div>
          </div>
          <p className="text-xs text-gray-500 mt-2">
            <strong>注意：</strong>服务器上的账号数据（金币、VIP、收藏等）不会受影响
          </p>
        </div>
      </div>
    </MobileLayout>
  );
};

export default DecryptionErrorTestPage;
