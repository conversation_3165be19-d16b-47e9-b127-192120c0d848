import React from 'react';
import MobileLayout from '../components/MobileLayout';
import { Monitor, Smartphone, Tablet, Ruler, Eye } from 'lucide-react';

const LayoutTestPage: React.FC = () => {
  return (
    <MobileLayout title="布局测试" showBack>
      <div className="p-4 space-y-6">
        {/* 功能说明 */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h2 className="text-lg font-semibold text-blue-800 mb-2 flex items-center">
            <Ruler className="w-5 h-5 mr-2" />
            布局高度修复测试
          </h2>
          <p className="text-sm text-blue-700">
            测试页面内容高度是否正确计算，不应该出现不必要的滚动条。
          </p>
        </div>

        {/* 当前页面信息 */}
        <div className="bg-white rounded-lg p-4 shadow-sm">
          <h3 className="text-lg font-semibold mb-3 flex items-center">
            <Eye className="w-5 h-5 mr-2" />
            当前页面状态
          </h3>
          <div className="space-y-2 text-sm">
            <div className="flex items-center justify-between">
              <span className="text-gray-600">视口高度:</span>
              <span className="font-mono text-blue-600">{window.innerHeight}px</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-600">视口宽度:</span>
              <span className="font-mono text-blue-600">{window.innerWidth}px</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-600">设备像素比:</span>
              <span className="font-mono text-blue-600">{window.devicePixelRatio}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-600">用户代理:</span>
              <span className="font-mono text-blue-600 text-xs truncate">
                {navigator.userAgent.includes('iPhone') ? 'iPhone' : 
                 navigator.userAgent.includes('iPad') ? 'iPad' :
                 navigator.userAgent.includes('Android') ? 'Android' : 'Desktop'}
              </span>
            </div>
          </div>
        </div>

        {/* 布局组件信息 */}
        <div className="bg-white rounded-lg p-4 shadow-sm">
          <h3 className="text-lg font-semibold mb-3">布局组件高度</h3>
          <div className="space-y-3">
            <div className="bg-gray-50 rounded-lg p-3">
              <h4 className="font-medium text-gray-800 mb-2">顶部导航栏</h4>
              <div className="text-sm text-gray-600 space-y-1">
                <div>移动端: 56px (h-14)</div>
                <div>平板端: 64px (h-16)</div>
                <div>+ 安全区域: env(safe-area-inset-top)</div>
              </div>
            </div>
            
            <div className="bg-gray-50 rounded-lg p-3">
              <h4 className="font-medium text-gray-800 mb-2">底部Tab栏</h4>
              <div className="text-sm text-gray-600 space-y-1">
                <div>移动端: 64px (h-16)</div>
                <div>平板端: 80px (h-20)</div>
                <div>+ 安全区域: env(safe-area-inset-bottom)</div>
              </div>
            </div>

            <div className="bg-gray-50 rounded-lg p-3">
              <h4 className="font-medium text-gray-800 mb-2">内容区域</h4>
              <div className="text-sm text-gray-600 space-y-1">
                <div>最小高度: 100vh - 导航栏 - Tab栏 - 安全区域</div>
                <div>移动端: calc(100vh - 56px - 64px - env(safe-area-inset-top))</div>
                <div>平板端: calc(100vh - 64px - 80px - env(safe-area-inset-top))</div>
              </div>
            </div>
          </div>
        </div>

        {/* 修复前后对比 */}
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <h3 className="text-lg font-semibold text-green-800 mb-3">修复对比</h3>
          <div className="space-y-3">
            <div className="bg-white rounded-lg p-3">
              <h4 className="font-medium text-red-600 mb-2">❌ 修复前</h4>
              <div className="text-sm text-gray-700 space-y-1">
                <div>• 使用 min-h-screen (100vh)</div>
                <div>• 没有减去导航栏高度</div>
                <div>• 内容总是超出视口</div>
                <div>• 出现不必要的滚动条</div>
              </div>
            </div>
            
            <div className="bg-white rounded-lg p-3">
              <h4 className="font-medium text-green-600 mb-2">✅ 修复后</h4>
              <div className="text-sm text-gray-700 space-y-1">
                <div>• 使用 content-min-height-with-tab</div>
                <div>• 正确减去导航栏和Tab栏高度</div>
                <div>• 内容刚好填满可用空间</div>
                <div>• 只在内容超出时显示滚动条</div>
              </div>
            </div>
          </div>
        </div>

        {/* 响应式测试 */}
        <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
          <h3 className="text-lg font-semibold text-purple-800 mb-3">响应式适配</h3>
          <div className="grid grid-cols-3 gap-3 text-center text-sm">
            <div className="flex flex-col items-center p-3 bg-white rounded-lg">
              <Smartphone className="w-6 h-6 text-blue-500 mb-2" />
              <span className="font-medium">手机</span>
              <span className="text-xs text-gray-500">&lt; 768px</span>
              <div className="text-xs text-gray-600 mt-1">
                <div>导航: 56px</div>
                <div>Tab: 64px</div>
              </div>
            </div>
            <div className="flex flex-col items-center p-3 bg-white rounded-lg">
              <Tablet className="w-6 h-6 text-green-500 mb-2" />
              <span className="font-medium">平板</span>
              <span className="text-xs text-gray-500">768px+</span>
              <div className="text-xs text-gray-600 mt-1">
                <div>导航: 64px</div>
                <div>Tab: 80px</div>
              </div>
            </div>
            <div className="flex flex-col items-center p-3 bg-white rounded-lg">
              <Monitor className="w-6 h-6 text-purple-500 mb-2" />
              <span className="font-medium">桌面</span>
              <span className="text-xs text-gray-500">1024px+</span>
              <div className="text-xs text-gray-600 mt-1">
                <div>导航: 64px</div>
                <div>Tab: 80px</div>
              </div>
            </div>
          </div>
        </div>

        {/* CSS类说明 */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <h3 className="text-lg font-semibold text-yellow-800 mb-3">新增CSS类</h3>
          <div className="space-y-2 text-sm text-yellow-700">
            <div className="bg-white rounded-lg p-3">
              <h4 className="font-medium mb-1">.content-min-height</h4>
              <p className="text-xs">不带Tab栏的内容最小高度</p>
              <code className="text-xs bg-gray-100 px-2 py-1 rounded">
                calc(100vh - 导航栏高度 - 安全区域)
              </code>
            </div>
            
            <div className="bg-white rounded-lg p-3">
              <h4 className="font-medium mb-1">.content-min-height-with-tab</h4>
              <p className="text-xs">带Tab栏的内容最小高度</p>
              <code className="text-xs bg-gray-100 px-2 py-1 rounded">
                calc(100vh - 导航栏高度 - Tab栏高度 - 安全区域)
              </code>
            </div>
          </div>
        </div>

        {/* 测试内容 */}
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
          <h3 className="text-lg font-semibold text-gray-800 mb-3">测试说明</h3>
          <div className="space-y-2 text-sm text-gray-700">
            <p><strong>预期效果：</strong></p>
            <ul className="list-disc list-inside space-y-1 ml-4">
              <li>页面内容刚好填满可用空间</li>
              <li>没有不必要的滚动条</li>
              <li>在不同设备上都有正确的高度计算</li>
              <li>安全区域（刘海屏）得到正确处理</li>
            </ul>
            
            <p className="mt-3"><strong>测试方法：</strong></p>
            <ul className="list-disc list-inside space-y-1 ml-4">
              <li>在不同设备上打开此页面</li>
              <li>检查是否有垂直滚动条</li>
              <li>旋转设备测试横竖屏</li>
              <li>在浏览器中模拟不同设备尺寸</li>
            </ul>
          </div>
        </div>

        {/* 填充内容 - 用于测试滚动 */}
        <div className="bg-indigo-50 border border-indigo-200 rounded-lg p-4">
          <h3 className="text-lg font-semibold text-indigo-800 mb-3">填充内容</h3>
          <p className="text-sm text-indigo-700 mb-3">
            这是一些填充内容，用于测试当内容超出视口时的滚动行为。
          </p>
          {Array.from({ length: 5 }, (_, i) => (
            <div key={i} className="bg-white rounded-lg p-3 mb-2">
              <h4 className="font-medium text-indigo-800">测试内容块 {i + 1}</h4>
              <p className="text-sm text-indigo-600">
                这是第 {i + 1} 个测试内容块。当页面内容超出可用高度时，应该出现滚动条。
                但如果内容没有超出，就不应该有滚动条。
              </p>
            </div>
          ))}
        </div>

        {/* 底部提示 */}
        <div className="bg-gray-100 rounded-lg p-4 text-center">
          <p className="text-sm text-gray-600">
            🎉 如果您看到这条消息且页面滚动正常，说明布局高度修复成功！
          </p>
        </div>
      </div>
    </MobileLayout>
  );
};

export default LayoutTestPage;
