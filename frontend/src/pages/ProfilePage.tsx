import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuthStore } from '../store/authStore';
import { unifiedApiService } from '../services/unifiedApiService';
import { toast } from '../store/toastStore';
import AppHttpsNotice from '../components/AppHttpsNotice';

const ProfilePage: React.FC = () => {
  const navigate = useNavigate();
  const { user, isAuthenticated, updateUser } = useAuthStore();
  const [isLoading, setIsLoading] = useState(false);
  const [showAppHttpsNotice, setShowAppHttpsNotice] = useState(false);
  const [appHttpsError, setAppHttpsError] = useState<string | null>(null);

  // 页面加载时异步请求用户信息API并同步用户数据
  useEffect(() => {
    const syncUserInfo = async () => {
      if (!user || !isAuthenticated) return;

      try {
        setIsLoading(true);
        console.log('🔄 个人中心页面：开始同步用户信息...');

        // 设置超时处理，iOS上网络可能较慢
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('请求超时')), 15000); // 15秒超时
        });

        // 获取最新的用户信息，带超时控制
        const freshUser = await Promise.race([
          unifiedApiService.getUserInfo(),
          timeoutPromise
        ]) as any;

        console.log('✅ 个人中心页面：用户信息同步成功', {
          oldCoins: user.coins,
          newCoins: freshUser.coins,
          oldVip: user.isVip,
          newVip: freshUser.isVip
        });

        updateUser(freshUser);
      } catch (error: any) {
        console.error('❌ 个人中心页面：同步用户信息失败:', error);

        // 检测App端HTTPS相关错误
        const isAppHttpsError = (
          error.code === 'ERR_NETWORK' ||
          error.code === 'ERR_FAILED' ||
          error.message?.includes('Mixed Content') ||
          error.message?.includes('HTTPS') ||
          error.message?.includes('不允许使用非加密传输') ||
          error.message?.includes('cleartext not permitted')
        );

        // 检测App环境
        const userAgent = navigator.userAgent.toLowerCase();
        const isApp = (
          /wkwebview|uiwebview|wv|webview/.test(userAgent) ||
          !!(window as any).ReactNativeWebView ||
          !!(window as any).flutter_inappwebview ||
          /smartvideo-app/.test(userAgent)
        );
        const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

        if (isAppHttpsError && (isApp || isMobile)) {
          const platform = /iphone|ipad|ipod/.test(userAgent) ? 'iOS' :
                          /android/.test(userAgent) ? 'Android' : 'App';
          const errorMessage = `${platform} App要求使用HTTPS安全连接，请联系技术支持配置SSL证书`;

          setAppHttpsError(errorMessage);
          setShowAppHttpsNotice(true);

          console.warn('🔒 检测到App端HTTPS连接问题:', {
            platform,
            isApp,
            error: error.message,
            suggestion: '需要配置HTTPS服务器'
          });
        }

        // 在iOS上，如果同步失败，不影响页面显示，只是不更新数据
        // 用户仍然可以看到缓存的用户信息
        if (error instanceof Error && error.message === '请求超时') {
          console.warn('⚠️ 用户信息同步超时，使用缓存数据');
        }
      } finally {
        setIsLoading(false);
      }
    };

    syncUserInfo();
  }, []); // 只在组件挂载时执行一次

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN');
  };

  // 如果没有用户信息，显示加载状态
  if (!user) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">加载中...</div>
      </div>
    );
  }

  // 根据用户类型构建菜单项
  const menuItems = [];

  // 邮箱相关功能已移至顶部卡片，这里不再显示

  // 通用菜单项
  menuItems.push(
    {
      icon: '💰',
      title: '推广赚钱',
      subtitle: user.email ? '推广好友获得VIP和返佣奖励' : '需要绑定邮箱后才能使用',
      action: () => {
        if (user.email) {
          navigate('/invite');
        } else {
          navigate('/link-email');
        }
      },
      showArrow: true,
      highlight: true,
      disabled: !user.email
    },
    {
      icon: '❤️',
      title: '我的收藏',
      subtitle: '收藏的视频',
      action: () => navigate('/favorites'),
      showArrow: true
    },
    {
      icon: '🛒',
      title: '已购买',
      subtitle: '购买的视频',
      action: () => navigate('/purchased'),
      showArrow: true
    },
    {
      icon: '📺',
      title: '观看历史',
      subtitle: '最近观看的视频',
      action: () => navigate('/history'),
      showArrow: true
    },
    {
      icon: '📋',
      title: '订单记录',
      subtitle: '查看所有订单',
      action: () => navigate('/orders'),
      showArrow: true
    }
  );

  // 移除重新开始功能

  return (
    <div>
      {/* App端HTTPS提示 */}
      {showAppHttpsNotice && (
        <AppHttpsNotice
          onRetry={() => {
            setShowAppHttpsNotice(false);
            setAppHttpsError(null);
            toast.info('正在重试HTTPS连接...');
            window.location.reload();
          }}
          onDismiss={() => {
            setShowAppHttpsNotice(false);
            setAppHttpsError(null);
          }}
        />
      )}

      {/* 用户信息头部 */}
      <div className="bg-gradient-to-r from-blue-500 to-indigo-600 text-white px-4 py-6 relative">
        {/* 设置按钮 - 右上角 */}
        <button
          onClick={() => navigate('/settings')}
          className="absolute top-4 right-4 w-10 h-10 bg-white bg-opacity-20 backdrop-blur-sm rounded-full flex items-center justify-center active:scale-95 transition-all duration-200 hover:bg-opacity-30"
          title="设置"
        >
          <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
        </button>

        <div className="flex items-center space-x-4">
          <div className={`w-16 h-16 rounded-full flex items-center justify-center text-2xl ${
            user.isVip
              ? 'bg-gradient-to-r from-yellow-400 to-yellow-600 shadow-lg'
              : 'bg-white bg-opacity-20'
          }`}>
            {user.isVip ? '👑' : '🎭'}
          </div>
          <div className="flex-1">
            <div className="flex items-center space-x-2">
              <h1 className="text-xl font-bold">{user.nickname}</h1>
              <span className="bg-blue-200 text-blue-900 px-2 py-1 rounded-full text-xs font-bold">
                用户
              </span>
              {user.isVip && (
                <span className="bg-yellow-500 text-yellow-900 px-2 py-1 rounded-full text-xs font-bold">
                  VIP
                </span>
              )}
            </div>
            <p className="opacity-90 text-sm">
              {user.email ? user.email : '默认用户'}
            </p>
            <div className="flex items-center space-x-3 mt-2">
              {!user.email && (
                <span className="bg-orange-500 bg-opacity-20 px-3 py-1 rounded-full text-sm">
                  ⚠️ 未关联邮箱
                </span>
              )}
              {isLoading && (
                <span className="bg-blue-500 bg-opacity-20 px-3 py-1 rounded-full text-sm">
                  🔄 同步中...
                </span>
              )}
            </div>
          </div>
        </div>

        {/* 功能信息卡片 */}
        <div className="mt-4 space-y-3">
          {/* 第一行：VIP会员和金币余额 */}
          <div className="grid grid-cols-2 gap-3">
            {/* VIP会员卡片 */}
            <button
              onClick={() => navigate('/vip')}
              className="bg-white bg-opacity-10 backdrop-blur-sm rounded-xl p-4 text-left active:scale-95 transition-all duration-200 hover:bg-opacity-20"
            >
              <div className="flex items-center space-x-2 mb-2">
                <span className="text-xl">👑</span>
                <span className="font-medium">VIP会员</span>
              </div>
              {user.isVip ? (
                <div>
                  <p className="text-yellow-300 font-bold text-lg">已开通</p>
                  <p className="text-xs opacity-75">
                    {user.vipExpireAt ? `${formatDate(user.vipExpireAt)}到期` : '永久有效'}
                  </p>
                </div>
              ) : (
                <div>
                  <p className="text-gray-300 text-sm">未开通</p>
                  <p className="text-xs opacity-75">点击开通享受特权</p>
                </div>
              )}
            </button>

            {/* 金币余额卡片 */}
            <button
              onClick={() => navigate('/recharge')}
              className="bg-white bg-opacity-10 backdrop-blur-sm rounded-xl p-4 text-left active:scale-95 transition-all duration-200 hover:bg-opacity-20"
            >
              <div className="flex items-center space-x-2 mb-2">
                <span className="text-xl">💰</span>
                <span className="font-medium">金币余额</span>
              </div>
              <div>
                <p className="text-yellow-300 font-bold text-lg">{user.coins}</p>
                <p className="text-xs opacity-75">点击充值金币</p>
              </div>
            </button>
          </div>

          {/* 第二行：邮箱关联提醒（仅在未关联时显示） */}
          {!user.email && (
            <div className="w-full rounded-xl p-4 bg-orange-500 bg-opacity-20 backdrop-blur-sm border border-orange-400 border-opacity-30">
              {/* 未关联邮箱 - 安全提醒 */}
              <div>
                <div className="flex items-start space-x-3 mb-4">
                  <div className="flex-shrink-0">
                    <span className="text-2xl">⚠️</span>
                  </div>
                  <div className="flex-1">
                    <h3 className="font-semibold text-orange-100 mb-1">账号安全提醒</h3>
                    <p className="text-sm text-orange-200 leading-relaxed mb-2">
                      您的账号尚未关联邮箱，一旦设备丢失或数据清除，将无法找回账号信息，包括VIP会员、金币、邀请佣金等重要数据
                    </p>
                    <div className="bg-orange-400 bg-opacity-30 rounded-lg p-2 mt-2">
                      <p className="text-sm text-orange-100 font-medium">
                        🎁 首次关联邮箱奖励：<span className="font-bold">100金币 + 1天VIP</span>
                      </p>
                    </div>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <span className="text-xl">📧</span>
                    <div>
                      <p className="font-medium text-white">邮箱关联</p>
                      <p className="text-orange-300 font-bold text-sm">未关联</p>
                      <p className="text-xs opacity-75 text-orange-200">防止账号丢失，建议立即关联</p>
                    </div>
                  </div>
                  <button
                    onClick={() => navigate('/link-email')}
                    className="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg text-sm font-medium active:scale-95 transition-all duration-200"
                  >
                    立即关联
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>


      </div>

      {/* 菜单列表 */}
      <div className="px-4 py-4 space-y-2">
        {menuItems.map((item, index) => (
          <button
            key={index}
            onClick={item.action}
            className={`w-full bg-white rounded-xl p-4 flex items-center space-x-4 active:scale-98 transition-all duration-150 ${
              item.highlight && !item.disabled ? 'ring-2 ring-yellow-400 ring-opacity-50' : ''
            } ${
              item.disabled ? 'opacity-60' : ''
            } text-gray-900`}
          >
            <div className={`w-10 h-10 rounded-full flex items-center justify-center text-lg ${
              item.highlight && !item.disabled ? 'bg-yellow-100' :
              item.disabled ? 'bg-gray-200' : 'bg-gray-100'
            }`}>
              {item.icon}
            </div>
            <div className="flex-1 text-left">
              <h3 className={`font-medium ${item.disabled ? 'text-gray-500' : 'text-gray-900'}`}>
                {item.title}
                {item.disabled && (
                  <span className="ml-2 text-xs bg-orange-100 text-orange-600 px-2 py-1 rounded-full">
                    需要关联邮箱
                  </span>
                )}
              </h3>
              <p className={`text-sm ${item.disabled ? 'text-gray-400' : 'text-gray-500'}`}>
                {item.subtitle}
              </p>
            </div>
            {item.showArrow && (
              <div className={item.disabled ? 'text-gray-300' : 'text-gray-400'}>
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </div>
            )}
          </button>
        ))}
      </div>
    </div>
  );
};

export default ProfilePage;
