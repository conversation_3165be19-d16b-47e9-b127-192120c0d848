import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  applyBackgroundTheme, 
  getCurrentTheme, 
  getThemeDebugInfo,
  switchToShortsTheme,
  switchToDefaultTheme,
  BACKGROUND_THEMES,
  isWebClipMode,
  isIOSDevice
} from '../utils/dynamicBackgroundManager';
import { toast } from '../store/toastStore';

const TestDynamicBackgroundPage: React.FC = () => {
  const navigate = useNavigate();
  const [currentTheme, setCurrentTheme] = useState(getCurrentTheme());
  const [debugInfo, setDebugInfo] = useState<any>(null);

  useEffect(() => {
    updateDebugInfo();
  }, []);

  const updateDebugInfo = () => {
    const info = getThemeDebugInfo();
    setDebugInfo(info);
    setCurrentTheme(getCurrentTheme());
  };

  const handleThemeChange = (themeName: keyof typeof BACKGROUND_THEMES) => {
    applyBackgroundTheme(themeName);
    updateDebugInfo();
    toast.success(`主题已切换到: ${themeName}`);
  };

  const testShortsTheme = () => {
    switchToShortsTheme();
    updateDebugInfo();
    toast.info('已切换到短视频主题');
  };

  const testDefaultTheme = () => {
    switchToDefaultTheme();
    updateDebugInfo();
    toast.info('已切换到默认主题');
  };

  const goToShorts = () => {
    navigate('/shorts');
    toast.info('跳转到短视频页面（自动切换黑色背景）');
  };

  const goToHome = () => {
    navigate('/');
    toast.info('跳转到首页（自动切换白色背景）');
  };

  return (
    <div className="max-w-md mx-auto bg-white min-h-screen">
      {/* 头部 */}
      <div className="bg-gradient-to-r from-indigo-500 to-purple-600 text-white px-4 py-6 navbar-safe-top">
        <div className="flex items-center space-x-3 safe-area-left safe-area-right">
          <button
            onClick={() => navigate(-1)}
            className="w-10 h-10 bg-white bg-opacity-20 rounded-full flex items-center justify-center touch-button"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <div>
            <h1 className="text-xl font-bold">动态背景测试</h1>
            <p className="text-indigo-100 text-sm">WebClip背景色同步验证</p>
          </div>
        </div>
      </div>

      {/* 内容区域 */}
      <div className="p-4 space-y-4">
        {/* 当前状态 */}
        <div className="bg-gray-50 rounded-lg p-4">
          <h2 className="font-semibold text-gray-900 mb-3">当前状态</h2>
          
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600">当前主题:</span>
              <span className="font-medium text-blue-600">{currentTheme}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">WebClip模式:</span>
              <span className={`font-medium ${isWebClipMode() ? 'text-green-600' : 'text-gray-600'}`}>
                {isWebClipMode() ? '✅ 是' : '❌ 否'}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">iOS设备:</span>
              <span className={`font-medium ${isIOSDevice() ? 'text-green-600' : 'text-gray-600'}`}>
                {isIOSDevice() ? '✅ 是' : '❌ 否'}
              </span>
            </div>
          </div>
        </div>

        {/* 主题切换 */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h2 className="font-semibold text-blue-900 mb-3">主题切换</h2>
          
          <div className="grid grid-cols-2 gap-3">
            {Object.entries(BACKGROUND_THEMES).map(([themeName, theme]) => (
              <button
                key={themeName}
                onClick={() => handleThemeChange(themeName as keyof typeof BACKGROUND_THEMES)}
                className={`p-3 rounded-lg border-2 transition-all ${
                  currentTheme === themeName
                    ? 'border-blue-500 bg-blue-100'
                    : 'border-gray-200 bg-white hover:border-gray-300'
                }`}
                style={{ backgroundColor: theme.backgroundColor === '#000000' ? '#1f2937' : theme.backgroundColor }}
              >
                <div className="text-center">
                  <div className={`text-sm font-medium ${
                    theme.backgroundColor === '#000000' ? 'text-white' : 'text-gray-900'
                  }`}>
                    {themeName}
                  </div>
                  <div className={`text-xs mt-1 ${
                    theme.backgroundColor === '#000000' ? 'text-gray-300' : 'text-gray-600'
                  }`}>
                    {theme.backgroundColor}
                  </div>
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* 快速测试 */}
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <h2 className="font-semibold text-green-900 mb-3">快速测试</h2>
          
          <div className="space-y-3">
            <button
              onClick={testShortsTheme}
              className="w-full bg-black text-white py-3 px-4 rounded-lg font-medium transition-colors hover:bg-gray-800"
            >
              🎬 短视频主题
            </button>
            
            <button
              onClick={testDefaultTheme}
              className="w-full bg-white text-black border border-gray-300 py-3 px-4 rounded-lg font-medium transition-colors hover:bg-gray-50"
            >
              🏠 默认主题
            </button>
          </div>
        </div>

        {/* 页面跳转测试 */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <h2 className="font-semibold text-yellow-900 mb-3">自动切换测试</h2>
          <p className="text-yellow-700 text-sm mb-4">
            跳转到不同页面，观察背景色是否自动切换：
          </p>
          
          <div className="space-y-3">
            <button
              onClick={goToShorts}
              className="w-full bg-black text-white py-3 px-4 rounded-lg font-medium transition-colors hover:bg-gray-800"
            >
              📱 短视频页面 (自动黑色背景)
            </button>
            
            <button
              onClick={goToHome}
              className="w-full bg-white text-black border border-gray-300 py-3 px-4 rounded-lg font-medium transition-colors hover:bg-gray-50"
            >
              🏠 首页 (自动白色背景)
            </button>
          </div>
        </div>

        {/* 调试信息 */}
        {debugInfo && (
          <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
            <div className="flex items-center justify-between mb-3">
              <h2 className="font-semibold text-purple-900">调试信息</h2>
              <button
                onClick={updateDebugInfo}
                className="text-purple-600 hover:text-purple-800 text-sm"
              >
                🔄 刷新
              </button>
            </div>
            
            <div className="space-y-3 text-xs">
              {/* Meta标签信息 */}
              <div>
                <h3 className="font-medium text-purple-800 mb-1">Meta标签</h3>
                <div className="bg-white rounded p-2 space-y-1 font-mono">
                  <div>theme-color: {debugInfo.metaTags.themeColor || '未设置'}</div>
                  <div>status-bar-style: {debugInfo.metaTags.statusBarStyle || '未设置'}</div>
                  <div>ms-nav-color: {debugInfo.metaTags.msNavColor || '未设置'}</div>
                </div>
              </div>

              {/* CSS变量 */}
              <div>
                <h3 className="font-medium text-purple-800 mb-1">CSS变量</h3>
                <div className="bg-white rounded p-2 space-y-1 font-mono">
                  <div>--app-background-color: {debugInfo.cssVariables.appBackgroundColor || '未设置'}</div>
                  <div>--app-theme-color: {debugInfo.cssVariables.appThemeColor || '未设置'}</div>
                  <div>--app-status-bar-style: {debugInfo.cssVariables.appStatusBarStyle || '未设置'}</div>
                </div>
              </div>

              {/* Body样式 */}
              <div>
                <h3 className="font-medium text-purple-800 mb-1">Body样式</h3>
                <div className="bg-white rounded p-2 font-mono">
                  background-color: {debugInfo.bodyBackgroundColor || '未设置'}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 使用说明 */}
        <div className="bg-gray-50 rounded-lg p-4">
          <h2 className="font-semibold text-gray-900 mb-3">使用说明</h2>
          <ul className="text-gray-700 text-sm space-y-2">
            <li>• <strong>WebClip模式</strong>：添加到主屏幕后测试效果最佳</li>
            <li>• <strong>背景同步</strong>：浏览器背景色会与页面主题同步</li>
            <li>• <strong>自动切换</strong>：路由变化时自动切换对应主题</li>
            <li>• <strong>Meta标签</strong>：theme-color等标签会动态更新</li>
            <li>• <strong>iOS优化</strong>：状态栏样式会根据主题调整</li>
          </ul>
        </div>

        {/* 底部间距 */}
        <div className="h-20"></div>
      </div>
    </div>
  );
};

export default TestDynamicBackgroundPage;
