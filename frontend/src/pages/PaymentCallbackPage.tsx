import React, { useEffect, useState } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { toast } from '../store/toastStore';

const PaymentCallbackPage: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [status, setStatus] = useState<'loading' | 'success' | 'failed'>('loading');
  const [orderId, setOrderId] = useState<string>('');

  useEffect(() => {
    const paymentStatus = searchParams.get('status');
    const orderIdParam = searchParams.get('orderId');

    if (orderIdParam) {
      setOrderId(orderIdParam);
    }

    if (paymentStatus === 'success') {
      setStatus('success');
      toast.success('支付成功！金币已到账');
    } else if (paymentStatus === 'failed') {
      setStatus('failed');
      toast.error('支付失败，请重试');
    } else {
      // 如果没有状态参数，默认为失败
      setStatus('failed');
    }
  }, [searchParams]);

  const handleBackToRecharge = () => {
    navigate('/recharge');
  };

  const handleBackToProfile = () => {
    navigate('/profile');
  };

  const handleViewOrders = () => {
    navigate('/profile/orders');
  };

  if (status === 'loading') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-4 border-blue-600 border-t-transparent mx-auto mb-4"></div>
          <p className="text-gray-600">处理支付结果...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      <div className="mobile-container mx-auto px-4 py-8">
        <div className="max-w-md mx-auto">
          {/* 支付结果卡片 */}
          <div className="bg-white rounded-2xl shadow-xl overflow-hidden">
            <div className={`p-8 text-center ${
              status === 'success' 
                ? 'bg-gradient-to-r from-green-500 to-emerald-600' 
                : 'bg-gradient-to-r from-red-500 to-pink-600'
            } text-white`}>
              <div className="w-20 h-20 mx-auto mb-4 rounded-full bg-white bg-opacity-20 flex items-center justify-center">
                {status === 'success' ? (
                  <svg className="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                ) : (
                  <svg className="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                )}
              </div>
              <h1 className="text-2xl font-bold mb-2">
                {status === 'success' ? '支付成功' : '支付失败'}
              </h1>
              <p className="text-white text-opacity-90">
                {status === 'success' 
                  ? '恭喜您，充值已完成！' 
                  : '很抱歉，支付未能完成'
                }
              </p>
            </div>

            <div className="p-6">
              {orderId && (
                <div className="mb-6 p-4 bg-gray-50 rounded-xl">
                  <div className="text-sm text-gray-600 mb-1">订单号</div>
                  <div className="font-mono text-sm text-gray-900">{orderId}</div>
                </div>
              )}

              {status === 'success' ? (
                <div className="space-y-4">
                  <div className="text-center mb-6">
                    <div className="text-green-600 text-sm mb-2">✅ 金币已成功充值到您的账户</div>
                    <div className="text-gray-600 text-sm">您可以立即使用金币购买视频内容</div>
                  </div>

                  <button
                    onClick={handleBackToProfile}
                    className="w-full py-3 rounded-xl font-bold text-white bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 transition-all duration-300 shadow-lg hover:shadow-xl mb-3"
                  >
                    查看我的账户
                  </button>

                  <button
                    onClick={handleViewOrders}
                    className="w-full py-3 rounded-xl font-medium text-green-600 bg-green-50 hover:bg-green-100 transition-all duration-300 mb-3"
                  >
                    查看订单详情
                  </button>

                  <button
                    onClick={handleBackToRecharge}
                    className="w-full py-3 rounded-xl font-medium text-gray-600 bg-gray-100 hover:bg-gray-200 transition-all duration-300"
                  >
                    继续充值
                  </button>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="text-center mb-6">
                    <div className="text-red-600 text-sm mb-2">❌ 支付过程中出现问题</div>
                    <div className="text-gray-600 text-sm">请检查网络连接或稍后重试</div>
                  </div>

                  <button
                    onClick={handleBackToRecharge}
                    className="w-full py-3 rounded-xl font-bold text-white bg-gradient-to-r from-red-500 to-pink-600 hover:from-red-600 hover:to-pink-700 transition-all duration-300 shadow-lg hover:shadow-xl mb-3"
                  >
                    重新充值
                  </button>

                  <button
                    onClick={handleBackToProfile}
                    className="w-full py-3 rounded-xl font-medium text-gray-600 bg-gray-100 hover:bg-gray-200 transition-all duration-300"
                  >
                    返回个人中心
                  </button>
                </div>
              )}
            </div>
          </div>

          {/* 帮助信息 */}
          <div className="mt-6 text-center">
            <div className="text-sm text-gray-500 mb-2">遇到问题？</div>
            <div className="text-xs text-gray-400 leading-relaxed">
              如果支付遇到问题，请联系客服或查看帮助文档
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PaymentCallbackPage;
