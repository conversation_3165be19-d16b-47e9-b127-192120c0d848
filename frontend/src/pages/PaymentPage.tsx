import React, { useEffect, useState } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { toast } from '../store/toastStore';
import { unifiedApiService } from '../services/unifiedApiService';

interface PaymentInfo {
  orderId: string;
  amount: string;
  paymentMethod: string;
  returnUrl: string;
}

const PaymentPage: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [paymentInfo, setPaymentInfo] = useState<PaymentInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [paying, setPaying] = useState(false);
  const [countdown, setCountdown] = useState(300); // 5分钟倒计时

  useEffect(() => {
    // 获取支付参数
    const orderId = searchParams.get('orderId');
    const amount = searchParams.get('amount');
    const paymentMethod = searchParams.get('paymentMethod');
    const returnUrl = searchParams.get('returnUrl');

    if (!orderId || !amount || !paymentMethod || !returnUrl) {
      toast.error('支付参数错误');
      navigate('/recharge');
      return;
    }

    setPaymentInfo({
      orderId,
      amount,
      paymentMethod,
      returnUrl
    });
    setLoading(false);
  }, [searchParams, navigate]);

  // 倒计时
  useEffect(() => {
    if (countdown <= 0) {
      toast.error('支付超时，请重新发起支付');
      navigate('/recharge');
      return;
    }

    const timer = setInterval(() => {
      setCountdown(prev => prev - 1);
    }, 1000);

    return () => clearInterval(timer);
  }, [countdown, navigate]);

  // 模拟支付处理
  const handlePay = async () => {
    if (!paymentInfo) return;

    setPaying(true);
    try {
      // 模拟支付延迟
      await new Promise(resolve => setTimeout(resolve, 2000));

      // 模拟支付成功（实际项目中这里会调用真实的支付接口）
      const paymentResult = {
        success: true,
        paymentId: `${paymentInfo.paymentMethod}_${Date.now()}`,
        orderId: paymentInfo.orderId
      };

      if (paymentResult.success) {
        // 处理支付成功
        await unifiedApiService.processCoinRechargePayment(
          parseInt(paymentInfo.orderId),
          paymentResult.paymentId
        );
        
        toast.success('支付成功！金币已到账');
        navigate('/payment/callback?status=success&orderId=' + paymentInfo.orderId);
      } else {
        toast.error('支付失败，请重试');
      }
    } catch (error: any) {
      console.error('支付失败:', error);
      toast.error('支付失败：' + error.message);
    } finally {
      setPaying(false);
    }
  };

  // 取消支付
  const handleCancel = () => {
    navigate('/recharge');
  };

  // 格式化倒计时
  const formatCountdown = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-4 border-blue-600 border-t-transparent mx-auto mb-4"></div>
          <p className="text-gray-600">加载支付信息...</p>
        </div>
      </div>
    );
  }

  if (!paymentInfo) {
    return null;
  }

  const paymentMethodName = paymentInfo.paymentMethod === 'wechat' ? '微信支付' : '支付宝支付';
  const paymentIcon = paymentInfo.paymentMethod === 'wechat' ? '/wechatPay.svg' : '/alipay.svg';

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      <div className="mobile-container mx-auto px-4 py-8">
        {/* 页面头部 */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full mb-4">
            <span className="text-2xl text-white">💳</span>
          </div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">确认支付</h1>
          <p className="text-gray-600">请在 {formatCountdown(countdown)} 内完成支付</p>
        </div>

        {/* 支付信息卡片 */}
        <div className="bg-white rounded-2xl shadow-xl overflow-hidden mb-6">
          <div className="bg-gradient-to-r from-blue-500 to-purple-600 p-6 text-white">
            <div className="text-center">
              <div className="text-3xl font-bold mb-2">¥{paymentInfo.amount}</div>
              <div className="text-blue-100">充值金额</div>
            </div>
          </div>

          <div className="p-6">
            <div className="space-y-4">
              <div className="flex justify-between items-center py-3 border-b border-gray-100">
                <span className="text-gray-600">订单号</span>
                <span className="font-mono text-sm text-gray-900">{paymentInfo.orderId}</span>
              </div>
              
              <div className="flex justify-between items-center py-3 border-b border-gray-100">
                <span className="text-gray-600">支付方式</span>
                <div className="flex items-center space-x-2">
                  <img src={paymentIcon} alt={paymentMethodName} className="w-6 h-6" />
                  <span className="font-medium text-gray-900">{paymentMethodName}</span>
                </div>
              </div>
              
              <div className="flex justify-between items-center py-3">
                <span className="text-gray-600">支付金额</span>
                <span className="text-2xl font-bold text-blue-600">¥{paymentInfo.amount}</span>
              </div>
            </div>
          </div>
        </div>

        {/* 支付说明 */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-xl p-4 mb-6">
          <div className="flex items-start space-x-3">
            <div className="w-6 h-6 bg-yellow-400 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
              <span className="text-yellow-900 text-xs">⚠️</span>
            </div>
            <div className="text-sm text-yellow-800">
              <div className="font-bold mb-1">支付说明</div>
              <div className="text-xs leading-relaxed">
                • 当前为演示模式，点击"确认支付"即可完成支付<br/>
                • 实际项目中将跳转到真实的支付宝/微信支付页面<br/>
                • 支付成功后金币将立即到账
              </div>
            </div>
          </div>
        </div>

        {/* 支付按钮 */}
        <div className="space-y-4">
          <button
            onClick={handlePay}
            disabled={paying}
            className={`w-full py-4 rounded-xl font-bold text-lg transition-all duration-300 ${
              paying
                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                : 'bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700 shadow-lg hover:shadow-xl'
            }`}
          >
            {paying ? (
              <div className="flex items-center justify-center space-x-2">
                <div className="animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent"></div>
                <span>支付中...</span>
              </div>
            ) : (
              <div className="flex items-center justify-center space-x-2">
                <img src={paymentIcon} alt={paymentMethodName} className="w-6 h-6" />
                <span>确认支付 ¥{paymentInfo.amount}</span>
              </div>
            )}
          </button>

          <button
            onClick={handleCancel}
            disabled={paying}
            className="w-full py-3 rounded-xl font-medium text-gray-600 bg-gray-100 hover:bg-gray-200 transition-all duration-300"
          >
            取消支付
          </button>
        </div>
      </div>
    </div>
  );
};

export default PaymentPage;
