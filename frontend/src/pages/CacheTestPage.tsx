import React, { useState, useEffect } from 'react';
import { unifiedApiService } from '../services/unifiedApiService';
import MobileLayout from '../components/MobileLayout';

const CacheTestPage: React.FC = () => {
  const [videos, setVideos] = useState<any[]>([]);
  const [categories, setCategories] = useState<string[]>([]);
  const [vipOptions, setVipOptions] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [cacheStats, setCacheStats] = useState<any>(null);
  const [logs, setLogs] = useState<string[]>([]);

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [`[${timestamp}] ${message}`, ...prev.slice(0, 9)]);
  };

  // 测试缓存功能
  const testCacheFeature = async () => {
    setLoading(true);
    addLog('🚀 开始测试缓存功能...');

    try {
      // 1. 测试推荐视频缓存
      addLog('📺 获取推荐视频（首次请求）...');
      const recommendedVideos = await unifiedApiService.getRecommendedVideos(
        6,
        (cachedData) => {
          addLog(`✅ 缓存命中：推荐视频 (${cachedData.length} 个)`);
          setVideos(cachedData);
        },
        (freshData) => {
          addLog(`🔄 数据更新：推荐视频 (${freshData.length} 个)`);
          setVideos(freshData);
        }
      );

      // 2. 测试分类缓存
      addLog('📂 获取分类列表...');
      const categoriesData = await unifiedApiService.getCategories(
        (cachedData) => {
          addLog(`✅ 缓存命中：分类列表 (${cachedData.length} 个)`);
          setCategories(cachedData);
        },
        (freshData) => {
          addLog(`🔄 数据更新：分类列表 (${freshData.length} 个)`);
          setCategories(freshData);
        }
      );

      // 3. 测试VIP选项缓存
      addLog('💎 获取VIP选项...');
      const vipData = await unifiedApiService.getVipOptions(
        (cachedData) => {
          addLog(`✅ 缓存命中：VIP选项 (${cachedData.length} 个)`);
          setVipOptions(cachedData);
        },
        (freshData) => {
          addLog(`🔄 数据更新：VIP选项 (${freshData.length} 个)`);
          setVipOptions(freshData);
        }
      );

      addLog('✨ 缓存测试完成！');
    } catch (error) {
      addLog(`❌ 测试失败: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  // 获取缓存统计
  const loadCacheStats = async () => {
    try {
      const stats = await unifiedApiService.getCacheStats();
      setCacheStats(stats);
      addLog(`📊 缓存统计：${stats.totalItems} 项，${(stats.totalSize / 1024).toFixed(2)} KB`);
    } catch (error) {
      addLog(`❌ 获取缓存统计失败: ${error}`);
    }
  };

  // 清除缓存
  const clearCache = async (type: 'user' | 'content' | 'all') => {
    try {
      switch (type) {
        case 'user':
          await unifiedApiService.clearUserCache();
          addLog('🧹 用户缓存已清除');
          break;
        case 'content':
          await unifiedApiService.clearContentCache();
          addLog('🧹 内容缓存已清除');
          break;
        case 'all':
          await unifiedApiService.clearAllCache();
          addLog('🧹 所有缓存已清除');
          break;
      }
      await loadCacheStats();
    } catch (error) {
      addLog(`❌ 清除缓存失败: ${error}`);
    }
  };

  // 预加载关键数据
  const preloadData = async () => {
    try {
      addLog('🚀 开始预加载关键数据...');
      await unifiedApiService.preloadCriticalData();
      addLog('✅ 关键数据预加载完成');
      await loadCacheStats();
    } catch (error) {
      addLog(`❌ 预加载失败: ${error}`);
    }
  };

  useEffect(() => {
    loadCacheStats();
  }, []);

  return (
    <MobileLayout title="缓存测试" showBack>
      <div className="p-4 space-y-6">
        {/* 控制面板 */}
        <div className="bg-white rounded-lg p-4 shadow-sm">
          <h2 className="text-lg font-semibold mb-4">缓存控制面板</h2>
          
          <div className="grid grid-cols-2 gap-3 mb-4">
            <button
              onClick={testCacheFeature}
              disabled={loading}
              className="bg-blue-500 text-white px-4 py-2 rounded-lg disabled:opacity-50"
            >
              {loading ? '测试中...' : '测试缓存'}
            </button>
            
            <button
              onClick={preloadData}
              className="bg-green-500 text-white px-4 py-2 rounded-lg"
            >
              预加载数据
            </button>
            
            <button
              onClick={() => clearCache('content')}
              className="bg-orange-500 text-white px-4 py-2 rounded-lg"
            >
              清除内容缓存
            </button>
            
            <button
              onClick={() => clearCache('all')}
              className="bg-red-500 text-white px-4 py-2 rounded-lg"
            >
              清除所有缓存
            </button>
          </div>

          <button
            onClick={loadCacheStats}
            className="w-full bg-gray-500 text-white px-4 py-2 rounded-lg"
          >
            刷新缓存统计
          </button>
        </div>

        {/* 缓存统计 */}
        {cacheStats && (
          <div className="bg-white rounded-lg p-4 shadow-sm">
            <h3 className="text-lg font-semibold mb-3">缓存统计</h3>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600">缓存项数：</span>
                <span className="font-medium">{cacheStats.totalItems}</span>
              </div>
              <div>
                <span className="text-gray-600">总大小：</span>
                <span className="font-medium">{(cacheStats.totalSize / 1024).toFixed(2)} KB</span>
              </div>
              <div>
                <span className="text-gray-600">最旧项：</span>
                <span className="font-medium text-xs">{cacheStats.oldestItem || '无'}</span>
              </div>
              <div>
                <span className="text-gray-600">最新项：</span>
                <span className="font-medium text-xs">{cacheStats.newestItem || '无'}</span>
              </div>
            </div>
          </div>
        )}

        {/* 数据展示 */}
        <div className="space-y-4">
          {/* 推荐视频 */}
          {videos.length > 0 && (
            <div className="bg-white rounded-lg p-4 shadow-sm">
              <h3 className="text-lg font-semibold mb-3">推荐视频 ({videos.length})</h3>
              <div className="space-y-2">
                {videos.slice(0, 3).map((video, index) => (
                  <div key={index} className="text-sm p-2 bg-gray-50 rounded">
                    {video.title || `视频 ${index + 1}`}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* 分类列表 */}
          {categories.length > 0 && (
            <div className="bg-white rounded-lg p-4 shadow-sm">
              <h3 className="text-lg font-semibold mb-3">分类列表 ({categories.length})</h3>
              <div className="flex flex-wrap gap-2">
                {categories.map((category, index) => (
                  <span key={index} className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm">
                    {category}
                  </span>
                ))}
              </div>
            </div>
          )}

          {/* VIP选项 */}
          {vipOptions.length > 0 && (
            <div className="bg-white rounded-lg p-4 shadow-sm">
              <h3 className="text-lg font-semibold mb-3">VIP选项 ({vipOptions.length})</h3>
              <div className="space-y-2">
                {vipOptions.slice(0, 3).map((option, index) => (
                  <div key={index} className="text-sm p-2 bg-gray-50 rounded">
                    {option.name || `选项 ${index + 1}`} - {option.price || '价格未知'}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* 日志 */}
        <div className="bg-white rounded-lg p-4 shadow-sm">
          <h3 className="text-lg font-semibold mb-3">操作日志</h3>
          <div className="space-y-1 max-h-64 overflow-y-auto">
            {logs.map((log, index) => (
              <div key={index} className="text-xs font-mono p-2 bg-gray-50 rounded">
                {log}
              </div>
            ))}
          </div>
        </div>
      </div>
    </MobileLayout>
  );
};

export default CacheTestPage;
