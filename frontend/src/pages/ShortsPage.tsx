import React, { useState, useEffect, useRef, useCallback } from 'react';
import { unifiedApiService } from '../services/unifiedApiService';
import { useAuthStore } from '../store/authStore';
import { shortVideoHistoryService } from '../services/shortVideoHistoryService';
import { toast } from '../store/toastStore';
import ShortVideoPlayer from '../components/ShortVideoPlayer';
import ShortVideoOverlay from '../components/ShortVideoOverlay';
import PurchaseModal from '../components/PurchaseModal';
import type { Video } from '../types';

const ShortsPage: React.FC = () => {
  const { user, updateUser } = useAuthStore();
  const [videos, setVideos] = useState<Video[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [error, setError] = useState<string | null>(null);
  const [showPreviewEndModal, setShowPreviewEndModal] = useState(false);
  const [isRestoringFromHistory, setIsRestoringFromHistory] = useState(false);

  const containerRef = useRef<HTMLDivElement>(null);
  const startY = useRef<number>(0);
  const isDragging = useRef<boolean>(false);
  const lastTouchTime = useRef<number>(0);

  // 恢复播放记录
  const restorePlaybackPosition = useCallback(async (videoList: Video[]) => {
    try {
      setIsRestoringFromHistory(true);
      const history = await shortVideoHistoryService.getPlaybackPosition();

      if (history && videoList.length > 0) {
        const videoIds = videoList.map(v => v.id);

        // 检查视频列表是否匹配
        if (shortVideoHistoryService.isVideoListMatched(history.videoIds, videoIds)) {
          // 确保索引在有效范围内
          const targetIndex = Math.min(history.currentIndex, videoList.length - 1);

          console.log('📱 恢复短视频播放记录:', {
            targetIndex,
            videoId: videoList[targetIndex]?.id,
            title: videoList[targetIndex]?.title
          });

          setCurrentIndex(targetIndex);
          return;
        } else {
          console.log('📱 视频列表已更新，清除旧的播放记录');
          await shortVideoHistoryService.clearPlaybackPosition();
        }
      }

      // 没有有效记录，从第一个视频开始
      setCurrentIndex(0);
    } catch (error) {
      console.error('恢复播放记录失败:', error);
      setCurrentIndex(0);
    } finally {
      setIsRestoringFromHistory(false);
    }
  }, []);

  // 加载视频数据（带缓存）
  const loadVideos = useCallback(async (page: number = 1, reset: boolean = false) => {
    try {
      if (reset) {
        setLoading(true);
        setError(null);
      } else {
        setLoadingMore(true);
      }

      if (reset && page === 1) {
        // 首次加载使用缓存优先策略
        await unifiedApiService.getShortVideos(
          page,
          10,
        );
      } else {
        // 分页加载直接请求API（不缓存分页数据）
        const response = await unifiedApiService.getShortVideos(page, 10);

        if (reset) {
          setVideos(response.videos);
          // 尝试恢复播放记录，如果没有记录则从第一个视频开始
          await restorePlaybackPosition(response.videos);
        } else {
          setVideos(prev => [...prev, ...response.videos]);
        }

        setHasMore(response.videos.length === 10);
        setCurrentPage(page);
      }
    } catch (error) {
      console.error('Failed to load videos:', error);
      setError('加载失败，请重试');
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  }, [restorePlaybackPosition]);

  // 初始化加载视频数据
  useEffect(() => {
    loadVideos(1, true);
  }, [loadVideos]);

  // 预加载下一页数据
  useEffect(() => {
    if (currentIndex >= videos.length - 3 && hasMore && !loadingMore) {
      loadVideos(currentPage + 1);
    }
  }, [currentIndex, videos.length, hasMore, loadingMore, currentPage, loadVideos]);

  // 切换到下一个视频
  const goToNext = useCallback(() => {
    if (currentIndex < videos.length - 1) {
      const newIndex = currentIndex + 1;
      setCurrentIndex(newIndex);

      // 保存播放记录
      if (videos.length > 0) {
        const videoIds = videos.map(v => v.id);
        shortVideoHistoryService.updatePlaybackPositionDebounced(newIndex, videoIds);
      }
    }
  }, [currentIndex, videos]);

  // 切换到上一个视频
  const goToPrevious = useCallback(() => {
    if (currentIndex > 0) {
      const newIndex = currentIndex - 1;
      setCurrentIndex(newIndex);

      // 保存播放记录
      if (videos.length > 0) {
        const videoIds = videos.map(v => v.id);
        shortVideoHistoryService.updatePlaybackPositionDebounced(newIndex, videoIds);
      }
    }
  }, [currentIndex, videos]);

  // 触摸事件处理 - 优化性能
  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    startY.current = e.touches[0].clientY;
    isDragging.current = true;
    lastTouchTime.current = Date.now();
  }, []);

  const handleTouchMove = useCallback((e: React.TouchEvent) => {
    if (!isDragging.current) return;

    // 防止页面滚动
    e.preventDefault();
  }, []);

  const handleTouchEnd = useCallback((e: React.TouchEvent) => {
    if (!isDragging.current) return;

    const endY = e.changedTouches[0].clientY;
    const deltaY = startY.current - endY;
    const deltaTime = Date.now() - lastTouchTime.current;
    const velocity = Math.abs(deltaY) / deltaTime;

    // 判断是否为有效滑动（距离大于80px或速度足够快）
    const isValidSwipe = Math.abs(deltaY) > 80 || (Math.abs(deltaY) > 30 && velocity > 0.5);

    if (isValidSwipe) {
      if (deltaY > 0) {
        // 向上滑动 - 下一个视频
        goToNext();
      } else {
        // 向下滑动 - 上一个视频
        goToPrevious();
      }
    }

    isDragging.current = false;
  }, [goToNext, goToPrevious]);

  // 智能防止页面滚动 - 只阻止非进度条的滚动
  useEffect(() => {
    const preventDefault = (e: TouchEvent) => {
      const target = e.target as HTMLElement;
      // 如果是进度条或其子元素，允许默认行为
      if ((target as HTMLInputElement).type === 'range' || target.closest('input[type="range"]') || target.closest('[data-progress-bar]')) {
        return;
      }
      e.preventDefault();
    };

    document.addEventListener('touchmove', preventDefault, { passive: false });
    return () => {
      document.removeEventListener('touchmove', preventDefault);
    };
  }, []);

  // 键盘事件处理（用于调试）
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'ArrowUp') {
        goToPrevious();
      } else if (e.key === 'ArrowDown') {
        goToNext();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [goToNext, goToPrevious]);

  // 页面卸载时保存播放记录
  useEffect(() => {
    return () => {
      // 组件卸载时立即保存当前播放位置
      if (videos.length > 0 && currentIndex >= 0) {
        const videoIds = videos.map(v => v.id);
        shortVideoHistoryService.savePlaybackPosition(currentIndex, videoIds);
      }
    };
  }, [videos, currentIndex]);

  // 视频结束时循环播放当前视频
  const handleVideoEnd = useCallback(() => {
    // 不跳转到下一个视频，让视频播放器自己处理循环播放
    console.log('视频播放结束，将循环播放当前视频');
  }, []);

  // 处理视频错误
  const handleVideoError = useCallback((error: string | Error) => {
    console.error('Video error:', error);
    // 不再自动跳过错误视频，让用户手动操作
  }, []);

  // 判断是否可以观看视频
  const canWatch = (video: Video) => {
    // 免费视频（价格为0）
    if (!video.price || video.price === 0) return true;

    // VIP用户可以观看所有视频
    if (user?.isVip) return true;

    // 付费视频需要购买
    return video.hasPurchased;
  };

  // 处理购买
  const handlePurchase = async (video: Video) => {
    // 移除认证检查，所有用户都可以购买
    try {
      // 使用新的直接购买视频接口
      const result = await unifiedApiService.purchaseVideo(video.id);

      // 更新用户金币余额
      const updatedUser = await unifiedApiService.getUserInfo();
      updateUser(updatedUser);

      // 只更新本地状态，避免重新渲染视频
      setVideos(prevVideos =>
        prevVideos.map(v =>
          v.id === video.id
            ? { ...v, hasPurchased: true }
            : v
        )
      );

      toast.success(result.message || '购买成功！');
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : '购买失败';
      toast.error(errorMessage);
    }
  };

  // 预览结束处理
  const handlePreviewEnd = useCallback(() => {
    setShowPreviewEndModal(true);
  }, []);

  // 处理收藏状态更新
  const handleFavoriteUpdate = useCallback((videoId: number, isFavorited: boolean) => {
    setVideos(prevVideos =>
      prevVideos.map(video =>
        video.id === videoId
          ? { ...video, isFavorited }
          : video
      )
    );
  }, []);

  if (loading) {
    return (
      <div className="h-screen bg-black flex items-center justify-center">
        <div className="text-center text-white">
          <div className="loading-spinner mb-4"></div>
          <p>加载中...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="h-screen bg-black flex items-center justify-center">
        <div className="text-center text-white">
          <div className="text-4xl mb-4">⚠️</div>
          <p className="mb-4">{error}</p>
          <button
            onClick={() => loadVideos(1, true)}
            className="px-6 py-2 bg-pink-500 rounded-full text-white"
          >
            重试
          </button>
        </div>
      </div>
    );
  }

  if (videos.length === 0) {
    return (
      <div className="h-screen bg-black flex items-center justify-center">
        <div className="text-center text-white">
          <div className="text-4xl mb-4">📱</div>
          <p>暂无短视频内容</p>
        </div>
      </div>
    );
  }
  return (
    <div
      ref={containerRef}
      className="h-full bg-black overflow-hidden relative"
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
    >
      {/* 渲染当前视频和前后视频（性能优化） */}
      {videos.map((video, index) => {
        // 只渲染当前视频和相邻的视频以优化性能
        const isVisible = Math.abs(index - currentIndex) <= 1;
        const isActive = index === currentIndex;

        if (!isVisible) return null;

        return (
          <div
            key={video.id}
            className={`absolute inset-0 transition-transform duration-300 ease-out ${
              index === currentIndex
                ? 'translate-y-0 z-10'
                : index < currentIndex
                ? '-translate-y-full z-0'
                : 'translate-y-full z-0'
            }`}
            style={{
              // 使用transform3d启用硬件加速
              transform: `translate3d(0, ${
                index === currentIndex
                  ? '0%'
                  : index < currentIndex
                  ? '-100%'
                  : '100%'
              }, 0)`
            }}
          >
            <ShortVideoPlayer
              video={video}
              isActive={isActive}
              onVideoEnd={handleVideoEnd}
              onVideoError={handleVideoError}
              isPaidVideo={video.price ? video.price > 0 : false}
              hasAccess={canWatch(video)}
              previewDuration={60}
              onPreviewEnd={handlePreviewEnd}
              shouldPause={showPreviewEndModal}
            />
            <ShortVideoOverlay
              video={video}
              onPreviewEnd={handlePreviewEnd}
              onFavorite={handleFavoriteUpdate}
            />
          </div>
        );
      })}

      {/* 加载更多指示器 */}
      {loadingMore && (
        <div className="absolute top-4 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-50 text-white px-3 py-1 rounded-full text-sm">
          加载中...
        </div>
      )}

      {/* 视频索引指示器 */}
      <div className="absolute top-4 left-4 bg-black bg-opacity-50 text-white px-2 py-1 rounded-full text-xs z-30">
        {currentIndex + 1} / {videos.length}
      </div>

      {/* 恢复播放记录提示 */}
      {isRestoringFromHistory && currentIndex > 0 && (
        <div className="absolute top-16 left-1/2 transform -translate-x-1/2 bg-green-600 bg-opacity-90 text-white px-4 py-2 rounded-full text-sm z-30 animate-fade-in">
          📱 已恢复到上次观看位置
        </div>
      )}

      {/* 手势提示（仅在第一个视频时显示） */}
      {currentIndex === 0 && videos.length > 1 && (
        <div className="absolute inset-x-0 bottom-40 flex justify-center pointer-events-none">
          <div className="bg-black bg-opacity-50 text-white px-4 py-2 rounded-full text-sm flex items-center space-x-2 animate-bounce">
            <span>👆</span>
            <span>上滑查看更多</span>
          </div>
        </div>
      )}

      {/* 购买弹框 */}
      <PurchaseModal
        isOpen={showPreviewEndModal}
        video={videos[currentIndex] || null}
        onClose={() => setShowPreviewEndModal(false)}
        onPurchase={handlePurchase}
        previewDuration={1}
      />
    </div>
  );
};

export default ShortsPage;
