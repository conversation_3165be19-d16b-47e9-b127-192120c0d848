import React, { useEffect, useState, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import MobileVideoCard from '../components/MobileVideoCard';
import { watchHistoryService, type WatchHistoryItem } from '../services/watchHistoryService';
import type { Video } from '../types';

const HistoryPage: React.FC = () => {
  const navigate = useNavigate();
  const [videos, setVideos] = useState<Video[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const observerRef = useRef<IntersectionObserver | null>(null);
  const loadMoreRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    loadHistory(1, true);
  }, []);

  // 设置无限滚动
  useEffect(() => {
    if (observerRef.current) {
      observerRef.current.disconnect();
    }

    observerRef.current = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && hasMore && !loadingMore && !loading) {
          loadHistory(currentPage + 1);
        }
      },
      { threshold: 0.1 }
    );

    if (loadMoreRef.current) {
      observerRef.current.observe(loadMoreRef.current);
    }

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [hasMore, loadingMore, loading, currentPage]);

  const loadHistory = async (page: number = currentPage, reset: boolean = false) => {
    try {
      if (reset) {
        setLoading(true);
      } else {
        setLoadingMore(true);
      }

      // 使用本地存储获取观看历史
      const response = await watchHistoryService.getWatchHistoriesPaginated(page, 12);

      // 将WatchHistoryItem转换为Video格式
      const convertedVideos: Video[] = response.histories.map((item: WatchHistoryItem) => ({
        id: item.id,
        title: item.title,
        cover: item.cover,
        videoUrl: item.videoUrl,
        duration: item.duration,
        category: item.category,
        price: item.price,
        viewCount: 0, // 本地存储没有播放次数
        isRecommended: false,
        isHot: false,
        createdAt: item.lastWatchAt,
        // 添加观看历史特有的字段
        lastWatchAt: item.lastWatchAt,
        progress: item.progress,
        watchCount: item.watchCount
      } as Video & { lastWatchAt: string; progress?: number; watchCount: number }));

      if (reset) {
        setVideos(convertedVideos);
      } else {
        setVideos(prev => [...prev, ...convertedVideos]);
      }

      setHasMore(response.histories.length === 12);
      setCurrentPage(page);
    } catch (error) {
      console.error('Failed to load watch history:', error);
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  };

  const handleVideoClick = (video: Video) => {
    navigate(`/video/${video.id}`);
  };

  const handleClearHistory = async () => {
    if (window.confirm('确定要清空所有观看历史吗？此操作不可恢复。')) {
      try {
        await watchHistoryService.clearWatchHistory();
        setVideos([]);
        setCurrentPage(1);
        setHasMore(false);
      } catch (error) {
        console.error('清空观看历史失败:', error);
      }
    }
  };



  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 1) {
      return '今天';
    } else if (diffDays === 2) {
      return '昨天';
    } else if (diffDays <= 7) {
      return `${diffDays - 1}天前`;
    } else {
      return date.toLocaleDateString('zh-CN');
    }
  };

  return (
    <div className="bg-gray-50 min-h-screen">
      <div className="p-3">
        {/* 顶部操作栏 */}
        {videos.length > 0 && (
          <div className="flex justify-between items-center mb-4">
            <div className="text-sm text-gray-600">
              共 {videos.length} 个观看记录
            </div>
            <button
              onClick={handleClearHistory}
              className="text-red-600 text-sm px-3 py-1 rounded-lg border border-red-200 hover:bg-red-50 transition-colors"
            >
              清空历史
            </button>
          </div>
        )}

        {loading ? (
          <div className="flex items-center justify-center py-20">
            <div className="loading-spinner"></div>
            <span className="ml-3 text-gray-600">加载中...</span>
          </div>
        ) : videos.length > 0 ? (
          <>
            <div className="grid grid-cols-2 gap-3">
              {videos.map((video) => (
                <div key={video.id} className="relative">
                  <MobileVideoCard
                    video={video}
                    onClick={handleVideoClick}
                    compact={true}
                    showProgress={true}
                  />

                  {/* 观看时间标签 */}
                  {(video as any).lastWatchAt && (
                    <div className="absolute top-2 right-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded">
                      {formatDate((video as any).lastWatchAt)}
                    </div>
                  )}

                  {/* 观看进度标签 */}
                  {(video as any).progress && (video as any).progress > 0 && (
                    <div className="absolute bottom-2 left-2 bg-blue-600 bg-opacity-90 text-white text-xs px-2 py-1 rounded">
                      已看 {Math.floor((video as any).progress / 60)}分钟
                    </div>
                  )}



                  {/* 观看次数标签 */}
                  {(video as any).watchCount > 1 && (
                    <div className="absolute bottom-2 right-2 bg-green-600 bg-opacity-90 text-white text-xs px-2 py-1 rounded">
                      看过 {(video as any).watchCount} 次
                    </div>
                  )}
                </div>
              ))}
            </div>

            {/* 无限滚动触发器 */}
            {hasMore && (
              <div 
                ref={loadMoreRef}
                className="flex justify-center mt-6 py-4"
              >
                {loadingMore && (
                  <div className="flex items-center">
                    <div className="loading-spinner mr-2"></div>
                    <span className="text-gray-600">加载中...</span>
                  </div>
                )}
              </div>
            )}

            {/* 没有更多内容提示 */}
            {!hasMore && videos.length > 0 && (
              <div className="text-center py-8">
                <p className="text-gray-500 text-sm">没有更多内容了</p>
              </div>
            )}
          </>
        ) : (
          <div className="text-center py-20">
            <div className="text-6xl mb-4">📺</div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">暂无观看历史</h2>
            <p className="text-gray-600 mb-6">还没有观看任何视频</p>
            <button
              onClick={() => navigate('/', { replace: true })}
              className="mobile-btn-primary px-6 py-3"
            >
              去发现视频
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default HistoryPage;
