import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import MobileVideoCard from '../components/MobileVideoCard';
import { unifiedApiService } from '../services/unifiedApiService';
import type { Video } from '../types';
import { useAnalytics } from '../hooks/useAnalytics';

const DiscoverPage: React.FC = () => {
  const navigate = useNavigate();
  const [videos, setVideos] = useState<Video[]>([]);
  const [categories, setCategories] = useState<string[]>([]);
  const [selectedCategory, setSelectedCategory] = useState('');
  const [loading, setLoading] = useState(true);
  const [searchKeyword, setSearchKeyword] = useState('');

  // 使用分析 Hook
  const { trackSearchEvent, trackCustomEvent } = useAnalytics();

  useEffect(() => {
    loadInitialData();
  }, []);

  useEffect(() => {
    if (selectedCategory) {
      loadCategoryVideos();
    } else {
      loadAllVideos();
    }
  }, [selectedCategory]);

  const loadInitialData = async () => {
    try {
      setLoading(true);
      const [cats, allVideos] = await Promise.all([
        unifiedApiService.getCategories(),
        unifiedApiService.getVideos({ page: 1, limit: 20 })
      ]);
      setCategories(cats);
      setVideos(allVideos.videos);
    } catch (error) {
      console.error('Failed to load initial data:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadAllVideos = async () => {
    try {
      setLoading(true);
      const response = await unifiedApiService.getVideos({ page: 1, limit: 20 });
      setVideos(response.videos);
    } catch (error) {
      console.error('Failed to load videos:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadCategoryVideos = async () => {
    try {
      setLoading(true);
      const response = await unifiedApiService.getVideos({
        page: 1,
        limit: 20,
        category: selectedCategory
      });
      setVideos(response.videos);
    } catch (error) {
      console.error('Failed to load category videos:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleVideoClick = (video: Video) => {
    navigate(`/video/${video.id}`);
  };

  const handleCategoryClick = (category: string) => {
    setSelectedCategory(category);

    // 跟踪分类点击事件
    trackCustomEvent('category_click', 'navigation', category || '全部');
  };

  const handleSearch = () => {
    if (searchKeyword.trim()) {
      // 跟踪搜索事件
      trackSearchEvent(searchKeyword.trim());

      navigate(`/videos?keyword=${encodeURIComponent(searchKeyword)}&title=搜索结果`);
    }
  };

  const quickActions = [
    {
      title: '推荐视频',
      icon: '🔥',
      color: 'bg-red-100 text-red-600',
      action: () => {
        trackCustomEvent('quick_action_click', 'navigation', '推荐视频');
        navigate('/videos?type=recommended&title=推荐视频');
      }
    },
    {
      title: '热门视频',
      icon: '🌟',
      color: 'bg-orange-100 text-orange-600',
      action: () => {
        trackCustomEvent('quick_action_click', 'navigation', '热门视频');
        navigate('/videos?type=hot&title=热门视频');
      }
    },
    {
      title: '免费观看',
      icon: '🆓',
      color: 'bg-green-100 text-green-600',
      action: () => {
        trackCustomEvent('quick_action_click', 'navigation', '免费观看');
        navigate('/videos?type=free&title=免费观看');
      }
    },
    {
      title: 'VIP专享',
      icon: '👑',
      color: 'bg-purple-100 text-purple-600',
      action: () => {
        trackCustomEvent('quick_action_click', 'navigation', 'VIP专享');
        navigate('/videos?type=vip&title=VIP专享');
      }
    },
    {
      title: '最新上传',
      icon: '🆕',
      color: 'bg-blue-100 text-blue-600',
      action: () => {
        trackCustomEvent('quick_action_click', 'navigation', '最新上传');
        navigate('/videos?type=latest&title=最新上传');
      }
    },
    {
      title: '全部视频',
      icon: '📺',
      color: 'bg-gray-100 text-gray-600',
      action: () => {
        trackCustomEvent('quick_action_click', 'navigation', '全部视频');
        navigate('/videos?title=全部视频');
      }
    }
  ];

  return (
    <div>
      {/* 搜索区域 */}
      <div className="bg-white px-4 py-4">
        <div className="flex space-x-3">
          <div className="flex-1 relative">
            <input
              type="text"
              placeholder="搜索视频..."
              value={searchKeyword}
              onChange={(e) => setSearchKeyword(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              className="w-full px-4 py-3 bg-gray-100 rounded-xl border-0 focus:ring-2 focus:ring-pink-500 focus:bg-white transition-all"
            />
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
              <span className="text-gray-400">🔍</span>
            </div>
          </div>
          <button
            onClick={handleSearch}
            className="mobile-btn-primary px-6"
          >
            搜索
          </button>
        </div>
      </div>

      {/* 快捷入口 */}
      <div className="bg-white mt-2 px-4 py-4">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">快捷入口</h2>
        <div className="grid grid-cols-3 gap-4">
          {quickActions.map((action, index) => (
            <button
              key={index}
              onClick={action.action}
              className="flex flex-col items-center p-4 rounded-xl active:scale-95 transition-transform duration-150"
              style={{ backgroundColor: action.color.includes('bg-') ? '' : action.color }}
            >
              <div className={`w-12 h-12 rounded-full flex items-center justify-center mb-2 ${action.color}`}>
                <span className="text-xl">{action.icon}</span>
              </div>
              <span className="text-sm font-medium text-gray-700">{action.title}</span>
            </button>
          ))}
        </div>
      </div>

      {/* 分类筛选 */}
      {categories.length > 0 && (
        <div className="bg-white mt-2 px-4 py-4">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">分类浏览</h2>
          <div className="grid grid-cols-4 gap-3">
            <button
              onClick={() => handleCategoryClick('')}
              className={`px-3 py-2 rounded-xl text-sm font-medium transition-all duration-200 active:scale-95 text-center ${
                selectedCategory === ''
                  ? 'bg-pink-500 text-white shadow-md'
                  : 'bg-gray-100 text-gray-700 active:bg-gray-200'
              }`}
            >
              全部
            </button>
            {categories.slice(0, 7).map((category) => (
              <button
                key={category}
                onClick={() => handleCategoryClick(category)}
                className={`px-3 py-2 rounded-xl text-sm font-medium transition-all duration-200 active:scale-95 text-center ${
                  selectedCategory === category
                    ? 'bg-pink-500 text-white shadow-md'
                    : 'bg-gray-100 text-gray-700 active:bg-gray-200'
                }`}
              >
                {category}
              </button>
            ))}
          </div>
        </div>
      )}

      {/* 视频列表 */}
      <div className="mt-2 p-3">
        {loading ? (
          <div className="flex items-center justify-center py-20">
            <div className="loading-spinner"></div>
            <span className="ml-3 text-gray-600">加载中...</span>
          </div>
        ) : videos.length > 0 ? (
          <div className="grid grid-cols-2 gap-3">
            {videos.map((video) => (
              <MobileVideoCard
                key={video.id}
                video={video}
                onClick={handleVideoClick}
                compact={true}
              />
            ))}
          </div>
        ) : (
          <div className="text-center py-20">
            <div className="text-6xl mb-4">📹</div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">暂无视频</h2>
            <p className="text-gray-600">没有找到符合条件的视频</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default DiscoverPage;
