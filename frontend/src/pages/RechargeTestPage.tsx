import React, { useState } from 'react';
import MobileLayout from '../components/MobileLayout';
import { unifiedApiService } from '../services/unifiedApiService';
import { toast } from 'react-hot-toast';

const RechargeTestPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [testResults, setTestResults] = useState<string[]>([]);

  const addResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const testCreateOrder = async () => {
    try {
      setLoading(true);
      addResult('🔄 测试创建订单...');
      
      const result = await unifiedApiService.createThirdPartyOrder('test-sku-id');
      addResult(`✅ 创建订单成功: ${JSON.stringify(result)}`);
    } catch (error: any) {
      addResult(`❌ 创建订单失败: ${error.message}`);
      console.error('创建订单失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const testGetSkus = async () => {
    try {
      setLoading(true);
      addResult('🔄 测试获取SKU列表...');
      
      const result = await unifiedApiService.getThirdPartySkus();
      addResult(`✅ 获取SKU成功: ${JSON.stringify(result)}`);
    } catch (error: any) {
      addResult(`❌ 获取SKU失败: ${error.message}`);
      console.error('获取SKU失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const testPayOrder = async () => {
    try {
      setLoading(true);
      addResult('🔄 测试支付订单...');

      const result = await unifiedApiService.payThirdPartyOrder('test-order-id');
      addResult(`✅ 支付订单成功: ${JSON.stringify(result)}`);
    } catch (error: any) {
      addResult(`❌ 支付订单失败: ${error.message}`);
      console.error('支付订单失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const testCreateAndPay = async () => {
    try {
      setLoading(true);
      addResult('🔄 测试一键创建订单并支付...');

      const result = await unifiedApiService.createAndPayRechargeOrder('test-sku-id');
      addResult(`✅ 一键创建并支付成功: ${JSON.stringify(result)}`);
    } catch (error: any) {
      addResult(`❌ 一键创建并支付失败: ${error.message}`);
      console.error('一键创建并支付失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const testGetOrderInfo = async () => {
    try {
      setLoading(true);
      addResult('🔄 测试获取订单信息...');
      
      const result = await unifiedApiService.getThirdPartyOrderInfo('test-order-id');
      addResult(`✅ 获取订单信息成功: ${JSON.stringify(result)}`);
    } catch (error: any) {
      addResult(`❌ 获取订单信息失败: ${error.message}`);
      console.error('获取订单信息失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <MobileLayout title="充值功能测试" showBack>
      <div className="p-4 space-y-6">
        {/* 功能说明 */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h2 className="text-lg font-semibold text-blue-800 mb-2">充值功能测试</h2>
          <p className="text-sm text-blue-700">
            测试第三方支付相关的API接口，检查充值功能是否正常工作。
          </p>
        </div>

        {/* 测试按钮 */}
        <div className="bg-white rounded-lg p-4 shadow-sm">
          <h3 className="text-lg font-semibold mb-4">API测试</h3>
          <div className="grid grid-cols-2 gap-3">
            <button
              onClick={testGetSkus}
              disabled={loading}
              className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors disabled:opacity-50"
            >
              获取SKU列表
            </button>
            <button
              onClick={testCreateOrder}
              disabled={loading}
              className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors disabled:opacity-50"
            >
              创建订单
            </button>
            <button
              onClick={testPayOrder}
              disabled={loading}
              className="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors disabled:opacity-50"
            >
              支付订单
            </button>
            <button
              onClick={testGetOrderInfo}
              disabled={loading}
              className="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors disabled:opacity-50"
            >
              获取订单信息
            </button>
            <button
              onClick={testCreateAndPay}
              disabled={loading}
              className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors disabled:opacity-50 col-span-2"
            >
              🚀 一键创建并支付 (新API)
            </button>
          </div>
          <button
            onClick={clearResults}
            className="w-full mt-3 bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
          >
            清除结果
          </button>
        </div>

        {/* 测试结果 */}
        <div className="bg-white rounded-lg p-4 shadow-sm">
          <h3 className="text-lg font-semibold mb-4">测试结果</h3>
          <div className="bg-gray-50 rounded-lg p-3 max-h-96 overflow-y-auto">
            {testResults.length === 0 ? (
              <p className="text-gray-500 text-sm">暂无测试结果</p>
            ) : (
              <div className="space-y-2">
                {testResults.map((result, index) => (
                  <div key={index} className="text-xs font-mono">
                    {result.includes('✅') && (
                      <div className="text-green-600">{result}</div>
                    )}
                    {result.includes('❌') && (
                      <div className="text-red-600">{result}</div>
                    )}
                    {result.includes('🔄') && (
                      <div className="text-blue-600">{result}</div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* 调试信息 */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <h3 className="text-lg font-semibold text-yellow-800 mb-3">调试信息</h3>
          <div className="space-y-2 text-sm text-yellow-700">
            <div><strong>API服务状态:</strong> {unifiedApiService ? '已加载' : '未加载'}</div>
            <div><strong>加密状态:</strong> {JSON.stringify(unifiedApiService.getEncryptionStatus())}</div>
            <div><strong>当前时间:</strong> {new Date().toLocaleString()}</div>
          </div>
        </div>

        {/* 使用说明 */}
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
          <h3 className="text-lg font-semibold text-gray-800 mb-3">使用说明</h3>
          <div className="space-y-2 text-sm text-gray-700">
            <p><strong>1. 获取SKU列表：</strong>测试是否能正常获取充值套餐</p>
            <p><strong>2. 创建订单：</strong>测试是否能创建第三方支付订单</p>
            <p><strong>3. 支付订单：</strong>测试是否能获取支付链接</p>
            <p><strong>4. 获取订单信息：</strong>测试是否能查询订单状态</p>
            <p><strong>5. 一键创建并支付：</strong>🚀 测试新的简化API，一步完成订单创建和支付</p>
          </div>
        </div>

        {/* 常见问题 */}
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <h3 className="text-lg font-semibold text-red-800 mb-3">常见问题</h3>
          <div className="space-y-2 text-sm text-red-700">
            <p><strong>按钮无反应：</strong>检查是否已登录，是否选择了SKU和支付方式</p>
            <p><strong>API调用失败：</strong>检查网络连接和服务器状态</p>
            <p><strong>加密错误：</strong>检查加密服务是否正常初始化</p>
            <p><strong>权限错误：</strong>检查用户是否有充值权限</p>
            <p><strong>新API优势：</strong>🚀 一键创建并支付API无需用户联系方式，只需登录状态</p>
          </div>
        </div>

        {/* 底部间距 */}
        <div className="h-20"></div>
      </div>
    </MobileLayout>
  );
};

export default RechargeTestPage;
