import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuthStore } from '../store/authStore';
import { unifiedApiService } from '../services/unifiedApiService';
import { toast } from '../store/toastStore';
import InsufficientCoinsModal from '../components/InsufficientCoinsModal';
import SuccessModal from '../components/SuccessModal';

interface VipOption {
  title: string;
  days: number;
  price: number;
  originalPrice?: number;
  duration: string;
  features: string[];
  popular?: boolean;
  discount?: string;
}

const VipPage: React.FC = () => {
  const navigate = useNavigate();
  const { user, isAuthenticated } = useAuthStore();
  const [selectedOption, setSelectedOption] = useState<VipOption | null>(null);
  const [loading, setLoading] = useState(false);
  const [showInsufficientCoinsModal, setShowInsufficientCoinsModal] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [vipOptions, setVipOptions] = useState<VipOption[]>([]);
  const [loadingOptions, setLoadingOptions] = useState(true);

  // 获取VIP选项配置
  useEffect(() => {
    const fetchVipOptions = async () => {
      try {
        setLoadingOptions(true);

        // 使用缓存API获取VIP选项
        const options = await unifiedApiService.getVipOptions(
          (cachedData) => {
            console.log('✅ 缓存命中：VIP选项', cachedData);
            setVipOptions(cachedData);
            setLoadingOptions(false); // 缓存命中时立即隐藏loading
          },
          (freshData) => {
            console.log('🔄 数据更新：VIP选项', freshData);
            setVipOptions(freshData);
          }
        );

        console.log('🔄 获取VIP选项完成:', options);
        setVipOptions(options);
      } catch (error) {
        console.error('获取VIP选项失败:', error);
        // 如果API失败，使用默认配置
        console.log('🔄 使用默认VIP选项');
        setVipOptions(getDefaultVipOptions());
      } finally {
        setLoadingOptions(false);
      }
    };

    if (isAuthenticated) {
      fetchVipOptions();
    }
  }, [isAuthenticated]);

  // 默认VIP选项（作为备用）- 与API配置保持一致
  const getDefaultVipOptions = (): VipOption[] => [
    {
      title: '3天VIP',
      days: 3,
      price: 20,
      duration: '3天',
      features: [
        '体验VIP特权'
      ]
    },
    {
      title: '7天VIP',
      days: 7,
      price: 40,
      originalPrice: 40,
      duration: '7天',
      discount: '10%折扣',
      features: [
        '一周畅享'
      ]
    },
    {
      title: '30天VIP',
      days: 30,
      price: 150,
      originalPrice: 150,
      duration: '30天',
      popular: true,
      discount: '20%折扣',
      features: [
        '月度会员'
      ]
    }
  ];

  const handlePurchase = async () => {
    if (!selectedOption || !isAuthenticated || !user) return;

    console.log('🛒 准备购买VIP:', {
      selectedOption,
      userCoins: user.coins,
      requiredCoins: selectedOption.price
    });

    // 检查金币余额
    if (user.coins < selectedOption.price) {
      console.log('❌ 金币余额不足');
      setShowInsufficientCoinsModal(true);
      return;
    }

    try {
      setLoading(true);

      console.log('💰 直接购买VIP:', {
        days: selectedOption.days
      });

      // 直接购买VIP（支付成功后创建订单）
      const result = await unifiedApiService.purchaseVip(selectedOption.days);

      console.log('✅ VIP购买成功:', result);

      setSuccessMessage(`您已获得${selectedOption.days}天VIP会员权限`);
      setShowSuccessModal(true);
    } catch (error: any) {
      console.error('❌ VIP购买失败:', error);
      toast.error(error.message || 'VIP开通失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('zh-CN');
  };

  const getExpireDate = (option: VipOption) => {
    const now = new Date();
    const expireDate = new Date(now.getTime() + option.days * 24 * 60 * 60 * 1000);
    return expireDate;
  };

  if (!isAuthenticated || !user) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50 pb-4">
      <div className="mobile-container mx-auto px-4 md:px-6 py-4 md:py-6">
        {/* 页面标题 */}
        <div className="text-center mb-6 md:mb-8">
          <h1 className="text-xl md:text-2xl font-bold text-gray-900 mb-1">开通VIP会员</h1>
          <p className="text-sm md:text-base text-gray-600">
            {user.isVip ? (
              <>
                当前状态: <span className="font-semibold text-yellow-600">VIP会员</span>
                {user.vipExpireAt && (
                  <span className="text-gray-500 block text-xs mt-1">
                    到期时间: {formatDate(new Date(user.vipExpireAt))}
                  </span>
                )}
              </>
            ) : (
              <>
                享受更多特权，畅享优质内容
                <span className="block text-xs mt-1">
                  当前金币余额: <span className="font-semibold text-blue-600">{user.coins} 金币</span>
                </span>
              </>
            )}
          </p>
        </div>

        {/* VIP特权介绍 */}
        <div className="bg-gradient-to-r from-yellow-400 to-yellow-600 text-white rounded-lg p-4 mb-6">
          <h2 className="text-lg font-bold mb-3">VIP会员特权</h2>
          <div className="grid grid-cols-2 gap-3">
            <div className="text-center">
              <div className="text-2xl mb-1">🎬</div>
              <div className="font-medium text-sm">免费观看</div>
              <div className="text-xs opacity-90">所有VIP视频</div>
            </div>
            <div className="text-center">
              <div className="text-2xl mb-1">🎯</div>
              <div className="font-medium text-sm">高清画质</div>
              <div className="text-xs opacity-90">1080P超清体验</div>
            </div>
            <div className="text-center">
              <div className="text-2xl mb-1">🚫</div>
              <div className="font-medium text-sm">无广告</div>
              <div className="text-xs opacity-90">纯净观看体验</div>
            </div>
            <div className="text-center">
              <div className="text-2xl mb-1">⭐</div>
              <div className="font-medium text-sm">专属标识</div>
              <div className="text-xs opacity-90">彰显尊贵身份</div>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm overflow-hidden">
          {/* VIP套餐选择 */}
          <div className="p-4">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">选择VIP套餐</h2>

            {loadingOptions ? (
              <div className="flex justify-center items-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-600"></div>
                <span className="ml-2 text-gray-600">加载套餐选项...</span>
              </div>
            ) : (
              <div className="grid grid-cols-2 gap-3 mb-6">
                {vipOptions.map((option, index) => (
                <div
                  key={index}
                  onClick={() => setSelectedOption(option)}
                  className={`relative border-2 rounded-lg p-3 cursor-pointer transition-all duration-200 ${
                    selectedOption === option
                      ? 'border-yellow-500 bg-yellow-50'
                      : 'border-gray-200 hover:border-yellow-300'
                  } ${option.popular ? 'ring-1 ring-yellow-400' : ''}`}
                >
                  {option.popular && (
                    <div className="absolute -top-1 left-1/2 transform -translate-x-1/2">
                      <span className="bg-yellow-400 text-yellow-900 px-2 py-0.5 rounded text-xs font-medium">
                        推荐
                      </span>
                    </div>
                  )}

                  <div className="text-center mb-3">
                    <h3 className="text-sm font-bold text-gray-900 mb-1">
                      {option.title}
                    </h3>
                    <div className="text-lg font-bold text-yellow-600 mb-1">
                      {option.price} 金币
                    </div>
                    {option.originalPrice && (
                      <div className="text-xs text-gray-500 line-through mb-1">
                        原价 {option.originalPrice}
                      </div>
                    )}
                    {option.discount && (
                      <div className="text-xs text-green-600 font-medium bg-green-100 px-2 py-0.5 rounded-full inline-block mb-1">
                        {option.discount}
                      </div>
                    )}
                    <div className="text-xs text-gray-600 bg-gray-100 px-2 py-0.5 rounded-full">
                      每天{(option.price / option.days).toFixed(1)}金币
                    </div>
                  </div>

                  <div className="space-y-1">
                    {option.features.slice(0, 3).map((feature, featureIndex) => (
                      <div key={featureIndex} className="flex items-center text-xs text-gray-700">
                        <svg className="w-3 h-3 text-green-500 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                        {feature}
                      </div>
                    ))}
                    {option.features.length > 3 && (
                      <div className="text-xs text-gray-500 text-center">
                        +{option.features.length - 3}项特权
                      </div>
                    )}
                  </div>

                  {selectedOption === option && (
                    <div className="absolute top-1 right-1">
                      <div className="w-4 h-4 bg-yellow-500 rounded-full flex items-center justify-center">
                        <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      </div>
                    </div>
                  )}
                </div>
                ))}
              </div>
            )}

            {/* 订单详情 */}
            {selectedOption && (
              <div className="bg-yellow-50 rounded-lg p-4 mb-4 border border-yellow-100">
                <h3 className="text-base font-semibold text-gray-900 mb-3">订单详情</h3>
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600 text-sm">套餐类型</span>
                    <span className="font-semibold">{selectedOption.title}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600 text-sm">有效期</span>
                    <span className="font-semibold">{selectedOption.duration}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600 text-sm">到期时间</span>
                    <span className="font-semibold text-yellow-600">
                      {formatDate(getExpireDate(selectedOption))}
                    </span>
                  </div>
                  <div className="flex justify-between items-center pt-2 border-t border-yellow-200">
                    <span className="text-gray-600 text-sm">需要金币</span>
                    <span className="font-semibold text-yellow-600">
                      {selectedOption.price} 金币
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600 text-sm">当前余额</span>
                    <span className={`font-semibold ${user.coins >= selectedOption.price ? 'text-green-600' : 'text-red-600'}`}>
                      {user.coins} 金币
                    </span>
                  </div>
                </div>
              </div>
            )}

            {/* 支付按钮 */}
            <div className="mt-6">
              <button
                onClick={handlePurchase}
                disabled={!selectedOption || loading}
                className="w-full py-3 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium"
              >
                {loading ? '开通中...' : '立即开通VIP'}
              </button>
            </div>
          </div>

          {/* 购买说明 */}
          <div className="bg-gray-50 p-3 border-t border-gray-200">
            <h3 className="text-sm font-semibold text-gray-900 mb-2">购买说明</h3>
            <div className="text-xs text-gray-600 space-y-1">
              <p>• VIP会员开通后立即生效</p>
              <p>• 会员期间可免费观看所有VIP标识的视频</p>
              <p>• 会员到期后将自动恢复为普通用户</p>
              <p>• 如有疑问，请联系客服</p>
            </div>
          </div>
        </div>
      </div>

      {/* 金币不足弹框 */}
      <InsufficientCoinsModal
        isOpen={showInsufficientCoinsModal}
        onClose={() => setShowInsufficientCoinsModal(false)}
        currentCoins={user?.coins || 0}
        requiredCoins={selectedOption?.price || 0}
        itemName={selectedOption?.title || ''}
        itemType="vip"
      />

      {/* 成功弹框 */}
      <SuccessModal
        isOpen={showSuccessModal}
        onClose={() => setShowSuccessModal(false)}
        title="VIP开通成功！"
        message={successMessage}
        buttonText="查看我的VIP"
        redirectPath="/profile"
        showConfetti={true}
      />
    </div>
  );
};

export default VipPage;
