import React, { useEffect, useState, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import MobileVideoCard from '../components/MobileVideoCard';
import { unifiedApiService } from '../services/unifiedApiService';
import type { Video } from '../types';

const FavoritesPage: React.FC = () => {
  const navigate = useNavigate();
  const [videos, setVideos] = useState<Video[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const observerRef = useRef<IntersectionObserver | null>(null);
  const loadMoreRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    loadFavorites(1, true);
  }, []);

  // 设置无限滚动
  useEffect(() => {
    if (observerRef.current) {
      observerRef.current.disconnect();
    }

    observerRef.current = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && hasMore && !loadingMore && !loading) {
          loadFavorites(currentPage + 1);
        }
      },
      { threshold: 0.1 }
    );

    if (loadMoreRef.current) {
      observerRef.current.observe(loadMoreRef.current);
    }

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [hasMore, loadingMore, loading, currentPage]);

  const loadFavorites = async (page: number = currentPage, reset: boolean = false) => {
    try {
      if (reset) {
        setLoading(true);
      } else {
        setLoadingMore(true);
      }

      const response = await unifiedApiService.getFavoriteVideos(page, 12);
      
      if (reset) {
        setVideos(response.videos);
      } else {
        setVideos(prev => [...prev, ...response.videos]);
      }
      
      setHasMore(response.videos.length === 12);
      setCurrentPage(page);
    } catch (error) {
      console.error('Failed to load favorites:', error);
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  };

  const handleVideoClick = (video: Video) => {
    navigate(`/video/${video.id}`);
  };

  return (
    <div className="bg-gray-50 min-h-screen">
      <div className="p-3">
        {loading ? (
          <div className="flex items-center justify-center py-20">
            <div className="loading-spinner"></div>
            <span className="ml-3 text-gray-600">加载中...</span>
          </div>
        ) : videos.length > 0 ? (
          <>
            <div className="grid grid-cols-2 gap-3">
              {videos.map((video) => (
                <MobileVideoCard
                  key={video.id}
                  video={video}
                  onClick={handleVideoClick}
                  compact={true}
                />
              ))}
            </div>

            {/* 无限滚动触发器 */}
            {hasMore && (
              <div 
                ref={loadMoreRef}
                className="flex justify-center mt-6 py-4"
              >
                {loadingMore && (
                  <div className="flex items-center">
                    <div className="loading-spinner mr-2"></div>
                    <span className="text-gray-600">加载中...</span>
                  </div>
                )}
              </div>
            )}

            {/* 没有更多内容提示 */}
            {!hasMore && videos.length > 0 && (
              <div className="text-center py-8">
                <p className="text-gray-500 text-sm">没有更多内容了</p>
              </div>
            )}
          </>
        ) : (
          <div className="text-center py-20">
            <div className="text-6xl mb-4">❤️</div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">暂无收藏</h2>
            <p className="text-gray-600 mb-6">还没有收藏任何视频</p>
            <button
              onClick={() => navigate('/', { replace: true })}
              className="mobile-btn-primary px-6 py-3"
            >
              去发现视频
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default FavoritesPage;
