import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { User, Mail, Edit3, Check, X, AlertCircle } from 'lucide-react';
import { useAuthStore } from '../store/authStore';
import { unifiedApiService } from '../services/unifiedApiService';
import { toast } from '../store/toastStore';
import MobileLayout from '../components/MobileLayout';
import { validateNickname, filterInput, getNicknameSuggestions, isDefaultNickname } from '../utils/nicknameValidator';

const UserProfilePage: React.FC = () => {
  const navigate = useNavigate();
  const { user, updateUser } = useAuthStore();
  const [isEditingNickname, setIsEditingNickname] = useState(false);
  const [newNickname, setNewNickname] = useState(user?.nickname || '');
  const [loading, setLoading] = useState(false);
  const [validationError, setValidationError] = useState<string>('');
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [suggestions, setSuggestions] = useState<string[]>([]);

  // 实时验证昵称
  const validateNicknameInput = (nickname: string) => {
    const validation = validateNickname(nickname);
    setValidationError(validation.isValid ? '' : validation.message);

    if (!validation.isValid && nickname.trim()) {
      const suggestions = getNicknameSuggestions(nickname);
      setSuggestions(suggestions);
      setShowSuggestions(true);
    } else {
      setShowSuggestions(false);
    }

    return validation;
  };

  // 处理输入变化
  const handleNicknameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const filtered = filterInput(e.target.value);
    setNewNickname(filtered);
    validateNicknameInput(filtered);
  };

  // 保存昵称
  const handleSaveNickname = async () => {
    const validation = validateNickname(newNickname);

    if (!validation.isValid) {
      setValidationError(validation.message);
      return;
    }

    if (validation.sanitized === user?.nickname) {
      setIsEditingNickname(false);
      setValidationError('');
      return;
    }

    try {
      setLoading(true);
      const updatedUser = await unifiedApiService.updateProfile({
        nickname: validation.sanitized!
      });

      updateUser(updatedUser);
      setIsEditingNickname(false);
      setValidationError('');
      setShowSuggestions(false);
      toast.success('昵称修改成功');
    } catch (error: any) {
      console.error('修改昵称失败:', error);
      const errorMessage = error.response?.data?.message || '修改昵称失败';
      setValidationError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // 取消编辑
  const handleCancelEdit = () => {
    setNewNickname(user?.nickname || '');
    setIsEditingNickname(false);
    setValidationError('');
    setShowSuggestions(false);
  };

  // 使用建议的昵称
  const useSuggestion = (suggestion: string) => {
    setNewNickname(suggestion);
    setValidationError('');
    setShowSuggestions(false);
  };

  // 开始编辑时的处理
  const handleStartEdit = () => {
    setIsEditingNickname(true);
    setNewNickname(user?.nickname || '');
    setValidationError('');
    setShowSuggestions(false);
  };

  // 检查是否为默认昵称，给出提示
  useEffect(() => {
    if (user?.nickname && isDefaultNickname(user.nickname)) {
      // 可以在这里添加提示用户修改默认昵称的逻辑
    }
  }, [user?.nickname]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (!user) {
    return (
      <MobileLayout title="个人信息" showBack={true} showTabBar={false}>
        <div className="flex items-center justify-center h-64">
          <div className="text-gray-500">加载中...</div>
        </div>
      </MobileLayout>
    );
  }

  return (
    <MobileLayout title="个人信息" showBack={true} showTabBar={false}>
      <div className="bg-gray-50 min-h-screen">
        {/* 用户头像区域 */}
        <div className="bg-gradient-to-r from-blue-500 to-indigo-600 text-white px-4 py-8">
          <div className="flex flex-col items-center">
            <div className={`w-20 h-20 rounded-full flex items-center justify-center text-3xl mb-4 ${
              user.isVip
                ? 'bg-gradient-to-r from-yellow-400 to-yellow-600 shadow-lg'
                : 'bg-white bg-opacity-20'
            }`}>
              {user.isVip ? '👑' : '🎭'}
            </div>
            <div className="flex items-center space-x-2">
              <span className="bg-blue-200 text-blue-900 px-2 py-1 rounded-full text-xs font-bold">
                用户
              </span>
              {user.isVip && (
                <span className="bg-yellow-500 text-yellow-900 px-2 py-1 rounded-full text-xs font-bold">
                  VIP
                </span>
              )}
            </div>
          </div>
        </div>

        {/* 基本信息卡片 */}
        <div className="px-4 py-4 space-y-4">
          {/* 昵称 */}
          <div className="bg-white rounded-xl p-4 shadow-sm">
            <div className="flex items-start justify-between">
              <div className="flex items-start space-x-3 flex-1">
                <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center flex-shrink-0">
                  <User className="w-5 h-5 text-blue-600" />
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2 mb-1">
                    <h3 className="font-medium text-gray-900">昵称</h3>
                    {user.nickname && isDefaultNickname(user.nickname) && (
                      <span className="bg-orange-100 text-orange-600 px-2 py-0.5 rounded-full text-xs">
                        建议修改
                      </span>
                    )}
                  </div>

                  {isEditingNickname ? (
                    <div className="space-y-3">
                      {/* 输入框 */}
                      <div className="relative">
                        <input
                          type="text"
                          value={newNickname}
                          onChange={handleNicknameChange}
                          className={`w-full px-4 py-3 border rounded-lg text-base focus:outline-none focus:ring-2 transition-colors ${
                            validationError
                              ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                              : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
                          }`}
                          placeholder="请输入昵称（2-20个字符）"
                          maxLength={20}
                          autoFocus
                          autoComplete="off"
                          spellCheck={false}
                        />
                        <div className="absolute right-3 top-3 text-xs text-gray-400">
                          {newNickname.length}/20
                        </div>
                      </div>

                      {/* 验证错误提示 */}
                      {validationError && (
                        <div className="flex items-start space-x-2 text-red-600 text-sm">
                          <AlertCircle className="w-4 h-4 mt-0.5 flex-shrink-0" />
                          <span>{validationError}</span>
                        </div>
                      )}

                      {/* 昵称建议 */}
                      {showSuggestions && suggestions.length > 0 && (
                        <div className="space-y-2">
                          <p className="text-sm text-gray-600">建议使用：</p>
                          <div className="flex flex-wrap gap-2">
                            {suggestions.map((suggestion, index) => (
                              <button
                                key={index}
                                onClick={() => useSuggestion(suggestion)}
                                className="px-3 py-1.5 bg-blue-50 text-blue-600 rounded-full text-sm hover:bg-blue-100 transition-colors"
                              >
                                {suggestion}
                              </button>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* 操作按钮 */}
                      <div className="flex space-x-3">
                        <button
                          onClick={handleSaveNickname}
                          disabled={loading || !!validationError}
                          className="flex-1 bg-blue-500 text-white py-3 rounded-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed hover:bg-blue-600 transition-colors"
                        >
                          {loading ? '保存中...' : '保存'}
                        </button>
                        <button
                          onClick={handleCancelEdit}
                          disabled={loading}
                          className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg font-medium hover:bg-gray-50 transition-colors"
                        >
                          取消
                        </button>
                      </div>
                    </div>
                  ) : (
                    <div className="space-y-1">
                      <p className="text-gray-600 text-sm break-all">{user.nickname}</p>
                      <p className="text-xs text-gray-400">点击右侧编辑图标可修改昵称</p>
                    </div>
                  )}
                </div>
              </div>

              {!isEditingNickname && (
                <button
                  onClick={handleStartEdit}
                  className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors flex-shrink-0"
                >
                  <Edit3 className="w-5 h-5" />
                </button>
              )}
            </div>
          </div>

          {/* 邮箱 */}
          <div className="bg-white rounded-xl p-4 shadow-sm">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center">
                <Mail className="w-5 h-5 text-green-600" />
              </div>
              <div className="flex-1">
                <h3 className="font-medium text-gray-900">邮箱</h3>
                {user.email ? (
                  <div>
                    <p className="text-gray-600 text-sm">{user.email}</p>
                    {user.emailVerified ? (
                      <div className="flex items-center mt-1">
                        <span className="bg-green-100 text-green-800 px-2 py-0.5 rounded-full text-xs">
                          ✓ 已验证
                        </span>
                      </div>
                    ) : (
                      <div className="flex items-center mt-1">
                        <span className="bg-yellow-100 text-yellow-800 px-2 py-0.5 rounded-full text-xs">
                          ⚠ 待验证
                        </span>
                      </div>
                    )}
                  </div>
                ) : (
                  <div>
                    <p className="text-gray-400 text-sm">未关联</p>
                    <button
                      onClick={() => navigate('/link-email')}
                      className="mt-1 bg-orange-500 text-white px-3 py-1 rounded-full text-xs hover:bg-orange-600 transition-colors"
                    >
                      立即关联
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* 账号信息 */}
          <div className="bg-white rounded-xl p-4 shadow-sm">
            <h3 className="font-medium text-gray-900 mb-3">账号信息</h3>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-gray-600 text-sm">用户ID</span>
                <span className="text-gray-900 text-sm font-mono">{user.id}</span>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="text-gray-600 text-sm">账号类型</span>
                <span className="text-gray-900 text-sm">
                  {user.isGuest ? '游客账号' : '正式账号'}
                </span>
              </div>

              <div className="flex justify-between items-center">
                <span className="text-gray-600 text-sm">VIP状态</span>
                <div className="flex items-center space-x-2">
                  <span className={`text-sm ${user.isVip ? 'text-yellow-600' : 'text-gray-900'}`}>
                    {user.isVip ? 'VIP会员' : '普通用户'}
                  </span>
                  {user.isVip && user.vipExpireAt && (
                    <span className="text-xs text-gray-500">
                      {formatDate(user.vipExpireAt)}到期
                    </span>
                  )}
                </div>
              </div>

              <div className="flex justify-between items-center">
                <span className="text-gray-600 text-sm">金币余额</span>
                <span className="text-yellow-600 text-sm font-semibold">{user.coins}</span>
              </div>

              <div className="flex justify-between items-center">
                <span className="text-gray-600 text-sm">注册时间</span>
                <span className="text-gray-900 text-sm">{formatDate(user.createdAt)}</span>
              </div>
            </div>
          </div>

          {/* 邀请信息（如果有邀请码） */}
          {user.inviteCode && (
            <div className="bg-white rounded-xl p-4 shadow-sm">
              <h3 className="font-medium text-gray-900 mb-3">邀请信息</h3>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-gray-600 text-sm">邀请码</span>
                  <span className="text-blue-600 text-sm font-mono">{user.inviteCode}</span>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-gray-600 text-sm">邀请人数</span>
                  <span className="text-gray-900 text-sm">{user.totalInvites || 0}人</span>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-gray-600 text-sm">累计佣金</span>
                  <span className="text-green-600 text-sm font-semibold">¥{user.totalCommission || '0.00'}</span>
                </div>
              </div>
            </div>
          )}


        </div>
      </div>
    </MobileLayout>
  );
};

export default UserProfilePage;
