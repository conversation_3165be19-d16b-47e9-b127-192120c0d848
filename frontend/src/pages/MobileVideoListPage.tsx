import React, { useEffect, useState, useCallback, useRef } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import MobileVideoCard from '../components/MobileVideoCard';
import { ScrollToTopButton } from '../components/ScrollRestoration';
import { unifiedApiService } from '../services/unifiedApiService';
import type { Video, VideoQuery } from '../types';

const MobileVideoListPage: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [videos, setVideos] = useState<Video[]>([]);
  const [categories, setCategories] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedCategory, setSelectedCategory] = useState('');
  const observerRef = useRef<IntersectionObserver | null>(null);
  const loadMoreRef = useRef<HTMLDivElement | null>(null);

  // 从URL参数获取查询条件
  const keyword = searchParams.get('keyword') || '';
  const type = searchParams.get('type') as 'recommended' | 'hot' | 'free' | 'vip' | 'latest' | undefined;
  const category = searchParams.get('category') || '';
  const title = searchParams.get('title') || '';

  useEffect(() => {
    loadCategories();
  }, []);

  useEffect(() => {
    // 如果URL中有category参数，设置为选中状态
    if (category) {
      setSelectedCategory(category);
    }
  }, [category]);

  useEffect(() => {
    resetAndLoadVideos();
  }, [selectedCategory, keyword, type, category]);

  // 设置无限滚动
  useEffect(() => {
    if (observerRef.current) {
      observerRef.current.disconnect();
    }

    observerRef.current = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && hasMore && !loadingMore && !loading) {
          loadVideos(currentPage + 1);
        }
      },
      { threshold: 0.1 }
    );

    if (loadMoreRef.current) {
      observerRef.current.observe(loadMoreRef.current);
    }

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [hasMore, loadingMore, loading, currentPage]);

  // 清理
  useEffect(() => {
    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, []);

  const loadCategories = async () => {
    try {
      const cats = await unifiedApiService.getCategories();
      setCategories(cats);
    } catch (error) {
      console.error('Failed to load categories:', error);
    }
  };

  const resetAndLoadVideos = useCallback(async () => {
    setVideos([]);
    setCurrentPage(1);
    setHasMore(true);
    await loadVideos(1, true);
  }, [selectedCategory, keyword, type, category]);

  const loadVideos = async (page: number = currentPage, reset: boolean = false) => {
    try {
      if (reset) {
        setLoading(true);
      } else {
        setLoadingMore(true);
      }

      const query: VideoQuery = {
        page,
        limit: 12,
      };

      // 优先使用URL中的category参数
      if (category) {
        query.category = category;
      } else if (selectedCategory) {
        query.category = selectedCategory;
      }

      if (keyword) query.keyword = keyword;

      // 处理特殊类型
      if (type === 'recommended') {
        const response = await unifiedApiService.getRecommendedVideos(query.limit || 12);
        if (reset) {
          setVideos(response);
        } else {
          setVideos(prev => [...prev, ...response]);
        }
        setHasMore(false); // 推荐视频不分页
        return;
      } else if (type === 'hot') {
        const response = await unifiedApiService.getHotVideos(query.limit || 12);
        if (reset) {
          setVideos(response);
        } else {
          setVideos(prev => [...prev, ...response]);
        }
        setHasMore(false); // 热门视频不分页
        return;
      } else if (type === 'free') {
        query.type = 'free';
      } else if (type === 'vip') {
        query.type = 'vip';
      } else if (type === 'latest') {
        // 最新上传，按时间排序
        query.sort = 'createdAt';
        query.order = 'desc';
      }

      const response = await unifiedApiService.getVideos(query);

      if (reset) {
        setVideos(response.videos);
      } else {
        setVideos(prev => [...prev, ...response.videos]);
      }

      setHasMore(response.videos.length === query.limit);
      setCurrentPage(page);
    } catch (error) {
      console.error('Failed to load videos:', error);
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  };

  const handleVideoClick = (video: Video) => {
    navigate(`/video/${video.id}`);
  };

  const handleCategoryClick = (category: string) => {
    setSelectedCategory(category);
  };

  const loadMore = () => {
    if (!loadingMore && hasMore) {
      loadVideos(currentPage + 1);
    }
  };

  const getPageTitle = () => {
    // 优先使用URL中的title参数
    if (title) return title;
    if (keyword) return `搜索: ${keyword}`;
    if (category) return category;
    if (selectedCategory) return selectedCategory;
    if (type === 'recommended') return '推荐视频';
    if (type === 'hot') return '热门视频';
    if (type === 'free') return '免费观看';
    if (type === 'vip') return 'VIP专享';
    if (type === 'latest') return '最新上传';
    return '全部视频';
  };

  return (
    <div>
      {/* 分类标签 - 只在非搜索且非特定类型状态显示 */}
      {!keyword && !type && !category && categories.length > 0 && (
        <section className="bg-white px-4 py-4 border-b border-gray-100">
          <div className="grid grid-cols-4 gap-3">
            <button
              onClick={() => handleCategoryClick('')}
              className={`px-3 py-2 rounded-xl text-sm font-medium transition-all duration-200 active:scale-95 text-center ${
                selectedCategory === ''
                  ? 'bg-pink-500 text-white shadow-md'
                  : 'bg-gray-100 text-gray-700 active:bg-gray-200'
              }`}
            >
              全部
            </button>
            {categories.slice(0, 7).map((cat) => (
              <button
                key={cat}
                onClick={() => handleCategoryClick(cat)}
                className={`px-3 py-2 rounded-xl text-sm font-medium transition-all duration-200 active:scale-95 text-center ${
                  selectedCategory === cat
                    ? 'bg-pink-500 text-white shadow-md'
                    : 'bg-gray-100 text-gray-700 active:bg-gray-200'
                }`}
              >
                {cat}
              </button>
            ))}
          </div>
        </section>
      )}

      {/* 视频列表 */}
      <div className={`p-3 ${!keyword && !type && !category && categories.length > 0 ? '' : 'pt-4'}`}>
        {loading ? (
          <div className="flex items-center justify-center py-20">
            <div className="loading-spinner"></div>
            <span className="ml-3 text-gray-600">加载中...</span>
          </div>
        ) : videos.length > 0 ? (
          <>
            <div className="grid grid-cols-2 md:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-6 gap-3 md:gap-4">
              {videos.map((video) => (
                <MobileVideoCard
                  key={video.id}
                  video={video}
                  onClick={handleVideoClick}
                  compact={true}
                />
              ))}
            </div>

            {/* 无限滚动触发器 */}
            {hasMore && (
              <div
                ref={loadMoreRef}
                className="flex justify-center mt-6 py-4"
              >
                {loadingMore && (
                  <div className="flex items-center">
                    <div className="loading-spinner mr-2"></div>
                    <span className="text-gray-600">加载中...</span>
                  </div>
                )}
              </div>
            )}

            {/* 没有更多内容提示 */}
            {!hasMore && videos.length > 0 && (
              <div className="text-center py-8">
                <p className="text-gray-500 text-sm">没有更多内容了</p>
              </div>
            )}
          </>
        ) : (
          <div className="text-center py-20">
            <div className="text-6xl mb-4">📹</div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">暂无视频</h2>
            <p className="text-gray-600">没有找到符合条件的视频</p>
          </div>
        )}
      </div>

      {/* 回到顶部按钮 */}
      <ScrollToTopButton threshold={300} />
    </div>
  );
};

export default MobileVideoListPage;
