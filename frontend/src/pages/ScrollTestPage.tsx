import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import MobileLayout from '../components/MobileLayout';
import { ScrollToTopButton, ScrollProgressBar, ScrollContainer } from '../components/ScrollRestoration';
import { useScrollReset, useScrollPosition, useSmartScroll } from '../hooks/useScrollReset';

const ScrollTestPage: React.FC = () => {
  const navigate = useNavigate();
  const [scrollInfo, setScrollInfo] = useState({ position: 0, progress: 0 });
  
  // 演示不同的滚动Hook
  const { resetScroll } = useScrollReset();
  const { saveScrollPosition, restoreScrollPosition, getCurrentScrollPosition } = useScrollPosition('scroll-test');
  const smartScroll = useSmartScroll({
    resetOnRouteChange: true,
    rememberPositions: true,
    positionKey: 'scroll-test-smart'
  });

  // 监听滚动位置
  React.useEffect(() => {
    const handleScroll = () => {
      const position = getCurrentScrollPosition();
      const maxScroll = document.documentElement.scrollHeight - window.innerHeight;
      const progress = maxScroll > 0 ? (position / maxScroll) * 100 : 0;
      
      setScrollInfo({ position: Math.round(position), progress: Math.round(progress) });
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    handleScroll(); // 初始化

    return () => window.removeEventListener('scroll', handleScroll);
  }, [getCurrentScrollPosition]);

  // 生成测试内容
  const generateContent = () => {
    const items = [];
    for (let i = 1; i <= 50; i++) {
      items.push(
        <div key={i} className="bg-white rounded-lg p-4 mb-4 shadow-sm">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">测试内容 #{i}</h3>
          <p className="text-gray-600 mb-3">
            这是第 {i} 个测试内容块。滚动测试页面用于验证滚动重置功能是否正常工作。
            当您从这个页面跳转到其他页面时，滚动位置应该自动重置到顶部。
          </p>
          <div className="flex flex-wrap gap-2">
            <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm">标签{i}</span>
            <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-sm">测试</span>
            <span className="bg-purple-100 text-purple-800 px-2 py-1 rounded text-sm">滚动</span>
          </div>
        </div>
      );
    }
    return items;
  };

  const scrollToPosition = (position: number) => {
    window.scrollTo({
      top: position,
      behavior: 'smooth'
    });
  };

  return (
    <MobileLayout title="滚动测试" showBack>
      {/* 滚动进度条 */}
      <ScrollProgressBar color="#ec4899" height={3} />
      
      <div className="p-4">
        {/* 控制面板 */}
        <div className="bg-white rounded-lg p-4 mb-6 shadow-sm sticky top-0 z-10">
          <h2 className="text-lg font-semibold mb-4">滚动控制面板</h2>
          
          {/* 滚动信息 */}
          <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
            <div>
              <span className="text-gray-600">滚动位置：</span>
              <span className="font-medium">{scrollInfo.position}px</span>
            </div>
            <div>
              <span className="text-gray-600">滚动进度：</span>
              <span className="font-medium">{scrollInfo.progress}%</span>
            </div>
          </div>

          {/* 控制按钮 */}
          <div className="grid grid-cols-2 gap-3 mb-4">
            <button
              onClick={() => resetScroll()}
              className="bg-blue-500 text-white px-3 py-2 rounded-lg text-sm"
            >
              重置滚动
            </button>
            
            <button
              onClick={() => saveScrollPosition()}
              className="bg-green-500 text-white px-3 py-2 rounded-lg text-sm"
            >
              保存位置
            </button>
            
            <button
              onClick={() => restoreScrollPosition()}
              className="bg-purple-500 text-white px-3 py-2 rounded-lg text-sm"
            >
              恢复位置
            </button>
            
            <button
              onClick={() => scrollToPosition(1000)}
              className="bg-orange-500 text-white px-3 py-2 rounded-lg text-sm"
            >
              跳到1000px
            </button>
          </div>

          {/* 页面跳转测试 */}
          <div className="border-t pt-4">
            <h3 className="text-sm font-semibold mb-3">页面跳转测试</h3>
            <div className="grid grid-cols-2 gap-3">
              <button
                onClick={() => navigate('/')}
                className="bg-pink-500 text-white px-3 py-2 rounded-lg text-sm"
              >
                跳转到首页
              </button>
              
              <button
                onClick={() => navigate('/videos')}
                className="bg-indigo-500 text-white px-3 py-2 rounded-lg text-sm"
              >
                跳转到视频列表
              </button>
              
              <button
                onClick={() => navigate('/shorts')}
                className="bg-red-500 text-white px-3 py-2 rounded-lg text-sm"
              >
                跳转到短视频
              </button>
              
              <button
                onClick={() => navigate('/profile')}
                className="bg-gray-500 text-white px-3 py-2 rounded-lg text-sm"
              >
                跳转到个人中心
              </button>
            </div>
          </div>
        </div>

        {/* 测试说明 */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
          <h3 className="text-lg font-semibold text-yellow-800 mb-2">📋 测试说明</h3>
          <ul className="text-sm text-yellow-700 space-y-1">
            <li>• 向下滚动页面，然后点击上方的页面跳转按钮</li>
            <li>• 跳转后的页面应该自动滚动到顶部</li>
            <li>• 短视频页面会保持滚动位置（在preserveScrollPaths中配置）</li>
            <li>• 使用"保存位置"和"恢复位置"测试滚动位置记忆功能</li>
            <li>• 右下角的"回到顶部"按钮在滚动超过300px后显示</li>
            <li>• 顶部的进度条显示当前滚动进度</li>
          </ul>
        </div>

        {/* 滚动容器测试 */}
        <div className="bg-white rounded-lg p-4 mb-6 shadow-sm">
          <h3 className="text-lg font-semibold mb-3">滚动容器测试</h3>
          <ScrollContainer
            className="h-40 border border-gray-200 rounded-lg p-3"
            resetOnRouteChange={true}
            rememberPosition={true}
            positionKey="scroll-container-test"
          >
            <div className="space-y-3">
              {Array.from({ length: 20 }, (_, i) => (
                <div key={i} className="bg-gray-50 p-3 rounded">
                  容器内容项 #{i + 1} - 这是一个可滚动容器的测试内容
                </div>
              ))}
            </div>
          </ScrollContainer>
          <p className="text-sm text-gray-600 mt-2">
            ↑ 这个容器有独立的滚动位置管理
          </p>
        </div>

        {/* 长内容区域 */}
        <div className="space-y-4">
          <h2 className="text-xl font-bold text-gray-900">长内容区域</h2>
          <p className="text-gray-600">
            向下滚动查看更多内容，然后测试页面跳转时的滚动重置功能。
          </p>
          
          {generateContent()}
          
          <div className="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
            <h3 className="text-lg font-semibold text-green-800 mb-2">🎉 到达底部</h3>
            <p className="text-green-700 mb-4">
              恭喜！您已经滚动到页面底部。现在可以测试页面跳转功能了。
            </p>
            <button
              onClick={() => navigate('/')}
              className="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors"
            >
              跳转到首页测试滚动重置
            </button>
          </div>
        </div>
      </div>

      {/* 回到顶部按钮 */}
      <ScrollToTopButton threshold={300}>
        <div className="flex items-center justify-center">
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 10l7-7m0 0l7 7m-7-7v18" />
          </svg>
        </div>
      </ScrollToTopButton>
    </MobileLayout>
  );
};

export default ScrollTestPage;
