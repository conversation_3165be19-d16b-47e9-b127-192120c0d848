import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { detectSafeAreaInfo, getSafeContentDimensions, type SafeAreaInfo } from '../utils/iosSafeAreaHelper';
import { toast } from '../store/toastStore';

const TestSafeAreaPage: React.FC = () => {
  const navigate = useNavigate();
  const [safeAreaInfo, setSafeAreaInfo] = useState<SafeAreaInfo | null>(null);
  const [contentDimensions, setContentDimensions] = useState<any>(null);

  useEffect(() => {
    const updateInfo = () => {
      const info = detectSafeAreaInfo();
      const dimensions = getSafeContentDimensions();
      setSafeAreaInfo(info);
      setContentDimensions(dimensions);
    };

    updateInfo();

    // 监听窗口变化
    const handleResize = () => {
      setTimeout(updateInfo, 100);
    };

    const handleOrientationChange = () => {
      setTimeout(updateInfo, 200);
    };

    window.addEventListener('resize', handleResize);
    window.addEventListener('orientationchange', handleOrientationChange);

    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('orientationchange', handleOrientationChange);
    };
  }, []);

  const refreshInfo = () => {
    const info = detectSafeAreaInfo();
    const dimensions = getSafeContentDimensions();
    setSafeAreaInfo(info);
    setContentDimensions(dimensions);
    toast.success('安全区域信息已刷新');
  };

  if (!safeAreaInfo || !contentDimensions) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-gray-500">加载中...</div>
      </div>
    );
  }

  return (
    <div className="max-w-md mx-auto bg-white min-h-screen">
      {/* 头部 - 测试安全区域适配 */}
      <div className="bg-gradient-to-r from-green-500 to-blue-600 text-white px-4 py-6 navbar-safe-top">
        <div className="flex items-center space-x-3 safe-area-left safe-area-right">
          <button
            onClick={() => navigate(-1)}
            className="w-10 h-10 bg-white bg-opacity-20 rounded-full flex items-center justify-center"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <div>
            <h1 className="text-xl font-bold">iOS安全区域测试</h1>
            <p className="text-green-100 text-sm">刘海屏适配验证</p>
          </div>
        </div>
      </div>

      {/* 内容区域 */}
      <div className="p-4 space-y-4">
        {/* 刷新按钮 */}
        <button
          onClick={refreshInfo}
          className="w-full bg-blue-500 hover:bg-blue-600 text-white py-3 px-4 rounded-lg font-medium transition-colors"
        >
          🔄 刷新安全区域信息
        </button>

        {/* 安全区域信息 */}
        <div className="bg-gray-50 rounded-lg p-4">
          <h2 className="font-semibold text-gray-900 mb-3">安全区域信息</h2>
          
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600">顶部安全区域:</span>
              <span className="font-medium text-blue-600">{safeAreaInfo.top}px</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">底部安全区域:</span>
              <span className="font-medium text-blue-600">{safeAreaInfo.bottom}px</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">左侧安全区域:</span>
              <span className="font-medium text-blue-600">{safeAreaInfo.left}px</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">右侧安全区域:</span>
              <span className="font-medium text-blue-600">{safeAreaInfo.right}px</span>
            </div>
          </div>
        </div>

        {/* 设备信息 */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h2 className="font-semibold text-blue-900 mb-3">设备信息</h2>
          
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-blue-700">设备型号:</span>
              <span className="font-medium text-blue-900">{safeAreaInfo.deviceModel}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-blue-700">是否iOS:</span>
              <span className={`font-medium ${safeAreaInfo.isIOS ? 'text-green-600' : 'text-red-600'}`}>
                {safeAreaInfo.isIOS ? '✅ 是' : '❌ 否'}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-blue-700">有刘海屏:</span>
              <span className={`font-medium ${safeAreaInfo.hasNotch ? 'text-green-600' : 'text-gray-600'}`}>
                {safeAreaInfo.hasNotch ? '✅ 是' : '❌ 否'}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-blue-700">有Dynamic Island:</span>
              <span className={`font-medium ${safeAreaInfo.hasDynamicIsland ? 'text-green-600' : 'text-gray-600'}`}>
                {safeAreaInfo.hasDynamicIsland ? '✅ 是' : '❌ 否'}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-blue-700">WebClip模式:</span>
              <span className={`font-medium ${safeAreaInfo.isWebClip ? 'text-green-600' : 'text-gray-600'}`}>
                {safeAreaInfo.isWebClip ? '✅ 是' : '❌ 否'}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-blue-700">屏幕方向:</span>
              <span className="font-medium text-blue-900">
                {safeAreaInfo.orientation === 'portrait' ? '📱 竖屏' : '📱 横屏'}
              </span>
            </div>
          </div>
        </div>

        {/* 屏幕尺寸信息 */}
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <h2 className="font-semibold text-green-900 mb-3">屏幕尺寸</h2>
          
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-green-700">视口宽度:</span>
              <span className="font-medium text-green-900">{window.innerWidth}px</span>
            </div>
            <div className="flex justify-between">
              <span className="text-green-700">视口高度:</span>
              <span className="font-medium text-green-900">{window.innerHeight}px</span>
            </div>
            <div className="flex justify-between">
              <span className="text-green-700">屏幕宽度:</span>
              <span className="font-medium text-green-900">{window.screen.width}px</span>
            </div>
            <div className="flex justify-between">
              <span className="text-green-700">屏幕高度:</span>
              <span className="font-medium text-green-900">{window.screen.height}px</span>
            </div>
            <div className="flex justify-between">
              <span className="text-green-700">设备像素比:</span>
              <span className="font-medium text-green-900">{window.devicePixelRatio}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-green-700">安全内容宽度:</span>
              <span className="font-medium text-green-900">{contentDimensions.width}px</span>
            </div>
            <div className="flex justify-between">
              <span className="text-green-700">安全内容高度:</span>
              <span className="font-medium text-green-900">{contentDimensions.height}px</span>
            </div>
          </div>
        </div>

        {/* 视觉测试区域 */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <h2 className="font-semibold text-yellow-900 mb-3">视觉测试</h2>
          <p className="text-yellow-700 text-sm mb-3">
            以下区域用于测试安全区域适配是否正确：
          </p>
          
          {/* 顶部测试条 */}
          <div className="bg-red-200 border-2 border-red-400 rounded p-2 mb-2">
            <p className="text-red-800 text-xs text-center">
              顶部测试条 - 应该不被刘海遮挡
            </p>
          </div>
          
          {/* 左右测试条 */}
          <div className="flex space-x-2 mb-2">
            <div className="flex-1 bg-blue-200 border-2 border-blue-400 rounded p-2">
              <p className="text-blue-800 text-xs text-center">左侧</p>
            </div>
            <div className="flex-1 bg-blue-200 border-2 border-blue-400 rounded p-2">
              <p className="text-blue-800 text-xs text-center">右侧</p>
            </div>
          </div>
          
          {/* 底部测试条 */}
          <div className="bg-green-200 border-2 border-green-400 rounded p-2">
            <p className="text-green-800 text-xs text-center">
              底部测试条 - 应该不被Home指示器遮挡
            </p>
          </div>
        </div>

        {/* CSS变量信息 */}
        <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
          <h2 className="font-semibold text-purple-900 mb-3">CSS变量</h2>
          <div className="text-xs text-purple-700 space-y-1 font-mono">
            <div>--safe-area-inset-top: {safeAreaInfo.top}px</div>
            <div>--safe-area-inset-bottom: {safeAreaInfo.bottom}px (未使用)</div>
            <div>--safe-area-inset-left: {safeAreaInfo.left}px</div>
            <div>--safe-area-inset-right: {safeAreaInfo.right}px</div>
            <div>--top-bar-height: {Math.max(56, 56 + safeAreaInfo.top)}px (含安全区域)</div>
            <div>--bottom-bar-height: 48px (固定高度，已优化)</div>
          </div>
          <div className="mt-2 text-xs text-purple-600">
            <strong>注意：</strong> 只有顶部导航使用安全区域适配，底部导航使用固定高度
          </div>
        </div>

        {/* 底部间距 */}
        <div className="h-20"></div>
      </div>
    </div>
  );
};

export default TestSafeAreaPage;
