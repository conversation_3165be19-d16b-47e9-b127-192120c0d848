import React, { useState, useEffect } from 'react';
import { useAuthStore } from '../store/authStore';
import { toast } from '../store/toastStore';
import { unifiedApiService } from '../services/unifiedApiService';

interface ThirdPartySku {
  id: string;
  skuid: string;
  price: number;
  coins: number;
  bonus: number;
  description: string;
  popular?: boolean;
}

interface PaymentMethod {
  id: 'wxpay' | 'alipay';
  name: string;
  icon: string;
  description: string;
}

const RechargePage: React.FC = () => {
  const { user, isAuthenticated } = useAuthStore();
  const [selectedSku, setSelectedSku] = useState<ThirdPartySku | null>(null);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<PaymentMethod | null>(null);
  const [loading, setLoading] = useState(false);
  const [thirdPartySkus, setThirdPartySkus] = useState<ThirdPartySku[]>([]);
  const [loadingOptions, setLoadingOptions] = useState(true);

  // 写死支付方式为微信支付和支付宝支付
  const paymentMethods: PaymentMethod[] = [
    {
      id: 'wxpay',
      name: '微信支付',
      icon: '/wechatPay.svg',
      description: '使用微信扫码支付'
    },
    {
      id: 'alipay',
      name: '支付宝支付',
      icon: '/alipay.svg',
      description: '使用支付宝扫码支付'
    }
  ];

  // 获取第三方SKU列表
  useEffect(() => {
    const fetchSkus = async () => {
      try {
        setLoadingOptions(true);

        // 使用缓存API，缓存命中时立即显示数据
        const skus = await unifiedApiService.getThirdPartySkus(
          (cachedData) => {
            console.log('✅ 缓存命中：第三方SKU', cachedData);
            setThirdPartySkus(cachedData);
            setLoadingOptions(false); // 缓存命中时立即隐藏loading
          },
          (freshData) => {
            console.log('🔄 数据更新：第三方SKU', freshData);
            setThirdPartySkus(freshData);
          }
        );

        console.log('🔄 获取第三方SKU完成:', skus);
        setThirdPartySkus(skus);
      } catch (error) {
        console.error('获取第三方SKU失败:', error);
        toast.error('获取充值套餐失败，请刷新重试');
      } finally {
        setLoadingOptions(false);
      }
    };

    if (isAuthenticated) {
      fetchSkus();
    }
  }, [isAuthenticated]);

  // 处理充值（只支持第三方支付）
  const handleRecharge = async () => {
    console.log('🔄 开始处理充值...');
    console.log('selectedPaymentMethod:', selectedPaymentMethod);
    console.log('isAuthenticated:', isAuthenticated);
    console.log('selectedSku:', selectedSku);
    console.log('user:', user);

    // 检查用户认证状态
    if (!isAuthenticated || !user) {
      console.log('❌ 用户未认证');
      toast.error('请先登录后再进行充值');
      return;
    }

    // 检查是否选择了充值套餐
    if (!selectedSku) {
      console.log('❌ 充值套餐未选择');
      toast.error('请选择充值套餐');
      return;
    }

    // 检查是否选择了支付方式
    if (!selectedPaymentMethod) {
      console.log('❌ 支付方式未选择');
      toast.error('请选择支付方式');
      return;
    }

    try {
      console.log('✅ 开始充值流程...');
      setLoading(true);
      await handleThirdPartyRecharge();
    } catch (error: any) {
      console.error('❌ 支付失败:', error);
      toast.error(error.message || '充值失败');
    } finally {
      setLoading(false);
    }
  };

  // 第三方充值处理
  const handleThirdPartyRecharge = async () => {
    console.log('🔄 开始第三方充值处理...');
    console.log('selectedSku:', selectedSku);
    console.log('selectedPaymentMethod:', selectedPaymentMethod);
    console.log('user:', user);

    if (!selectedSku || !selectedPaymentMethod) {
      console.log('❌ 缺少必要参数');
      toast.error('缺少必要的支付参数');
      return;
    }

    try {
      console.log('🔄 创建充值订单并发起支付...');
      // 使用新的简化API：创建订单并立即发起支付
      const paymentResult = await unifiedApiService.createAndPayRechargeOrder(selectedSku.skuid);
      console.log('✅ 支付结果:', paymentResult);

      // 在app外部打开支付链接
      window.open(paymentResult.payUrl, '_blank');

      // 显示支付提示
      toast.success('支付页面已打开，请完成支付');

      // 简单的支付状态检查（轮询）
      let checkCount = 0;
      const maxChecks = 60; // 最多检查60次
      const checkInterval = 3000; // 每3秒检查一次

      const checkPaymentStatus = async (): Promise<boolean> => {
        try {
          const orderInfo = await unifiedApiService.getThirdPartyOrderInfo(paymentResult.orderId);
          return orderInfo.status === 'paid';
        } catch (error) {
          console.error('检查支付状态失败:', error);
          return false;
        }
      };

      const pollStatus = async (): Promise<boolean> => {
        while (checkCount < maxChecks) {
          checkCount++;
          const isPaid = await checkPaymentStatus();

          if (isPaid) {
            return true;
          }

          await new Promise(resolve => setTimeout(resolve, checkInterval));
        }
        return false;
      };

      const paymentSuccess = await pollStatus();

      if (paymentSuccess) {
        toast.success('支付成功！金币已到账');
        // 刷新用户信息
        window.location.reload();
      } else {
        toast.error('支付超时或失败，请检查支付状态');
      }
    } catch (error: any) {
      console.error('第三方充值失败:', error);
      throw error;
    }
  };

  if (!isAuthenticated || !user) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 pb-24">
      <div className="mobile-container mx-auto px-4 md:px-6 py-6 md:py-8">
        {/* 页面头部 */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full mb-4">
            <span className="text-2xl">💰</span>
          </div>
          <h1 className="text-2xl md:text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-2">
            金币充值
          </h1>
          <div className="bg-white rounded-full px-6 py-3 shadow-lg inline-block">
            <p className="text-sm text-gray-600">
              当前余额: <span className="font-bold text-xl bg-gradient-to-r from-yellow-500 to-orange-500 bg-clip-text text-transparent">{user.coins}</span> 金币
            </p>
          </div>
        </div>

        {/* 充值选项卡片 */}
        <div className="bg-white rounded-2xl shadow-xl overflow-hidden mb-6">
          <div className="bg-gradient-to-r from-blue-500 to-purple-600 p-6 text-white">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-xl font-bold mb-2">选择充值套餐</h2>
                <p className="text-blue-100 text-sm">选择最适合您的充值方案，享受更多优惠</p>
              </div>
            </div>
          </div>

          <div className="p-6">
            {loadingOptions ? (
              <div className="flex items-center justify-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-4 border-blue-600 border-t-transparent mr-3"></div>
                <span className="text-gray-600">加载充值套餐...</span>
              </div>
            ) : (
              <div className="grid grid-cols-2 gap-4 mb-6">
                {/* 第三方SKU选项 */}
                {thirdPartySkus.map((sku) => (
                <div
                  key={sku.id}
                  onClick={() => setSelectedSku(sku)}
                  className={`relative group cursor-pointer transition-all duration-300 transform hover:scale-105 ${
                    selectedSku === sku ? 'scale-105' : ''
                  }`}
                >
                  {sku.popular && (
                    <div className="absolute -top-2 left-1/2 transform -translate-x-1/2 z-10">
                      <div className="bg-gradient-to-r from-yellow-400 to-orange-500 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg">
                        🔥 热门
                      </div>
                    </div>
                  )}

                  <div className={`relative overflow-hidden rounded-xl border-2 p-4 transition-all duration-300 ${
                    selectedSku === sku
                      ? 'border-blue-500 bg-gradient-to-br from-blue-50 to-purple-50 shadow-lg'
                      : 'border-gray-200 bg-white hover:border-blue-300 hover:shadow-md'
                  } ${sku.popular ? 'ring-2 ring-yellow-400 ring-opacity-50' : ''}`}>
                    
                    <div className="text-center">
                      <div className="text-2xl font-bold text-gray-900 mb-1">¥{sku.price}</div>
                      <div className="text-blue-600 font-semibold mb-2">{sku.coins} 金币</div>
                      {sku.bonus > 0 && (
                        <div className="text-green-600 text-sm font-medium mb-2">
                          +{sku.bonus} 赠送金币
                        </div>
                      )}
                      <div className="text-xs text-gray-500">
                        {sku.description}
                      </div>
                    </div>

                    {selectedSku === sku && (
                      <div className="absolute top-2 right-2">
                        <div className="w-6 h-6 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center shadow-lg">
                          <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
                ))}
              </div>
            )}

            {/* 订单详情 */}
            {selectedSku && (
              <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-5 mb-6 border border-blue-200 shadow-sm">
                <div className="flex items-center mb-4">
                  <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center mr-3">
                    <span className="text-white text-sm">📋</span>
                  </div>
                  <h3 className="text-lg font-bold text-gray-900">订单详情</h3>
                </div>
                
                <div className="space-y-3">
                  <div className="flex justify-between items-center py-2">
                    <span className="text-gray-600 font-medium">充值金额</span>
                    <span className="font-bold text-lg text-gray-900">
                      ¥{selectedSku.price}
                    </span>
                  </div>
                  <div className="flex justify-between items-center py-2">
                    <span className="text-gray-600 font-medium">获得金币</span>
                    <div className="text-right">
                      <span className="font-bold text-lg text-blue-600">
                        {selectedSku.coins} 金币
                      </span>
                      {selectedSku.bonus > 0 && (
                        <div className="text-green-600 text-sm font-medium">
                          +{selectedSku.bonus} 赠送金币
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="flex justify-between items-center py-2 border-t border-gray-200 pt-3">
                    <span className="text-gray-700 font-semibold">充值后余额</span>
                    <span className="font-bold text-xl bg-gradient-to-r from-yellow-500 to-orange-500 bg-clip-text text-transparent">
                      {user.coins + selectedSku.coins + selectedSku.bonus} 金币
                    </span>
                  </div>
                </div>
              </div>
            )}

            {/* 支付方式选择 */}
            {selectedSku && (
              <div className="mb-6">
                <div className="flex items-center mb-4">
                  <div className="w-8 h-8 bg-gradient-to-r from-green-500 to-blue-500 rounded-full flex items-center justify-center mr-3">
                    <span className="text-white text-sm">💳</span>
                  </div>
                  <h3 className="text-lg font-bold text-gray-900">选择支付方式</h3>
                </div>
                
                <div className="grid grid-cols-1 gap-3">
                  {paymentMethods.map((method) => (
                    <div
                      key={method.id}
                      onClick={() => setSelectedPaymentMethod(method)}
                      className={`relative cursor-pointer rounded-xl border-2 p-4 transition-all duration-300 hover:shadow-md ${
                        selectedPaymentMethod === method
                          ? 'border-blue-500 bg-gradient-to-r from-blue-50 to-purple-50 shadow-lg'
                          : 'border-gray-200 bg-white hover:border-blue-300'
                      }`}
                    >
                      <div className="flex items-center">
                        <img src={method.icon} alt={method.name} className="w-8 h-8 mr-4" />
                        <div className="flex-1">
                          <div className="font-bold text-gray-900">{method.name}</div>
                          <div className="text-sm text-gray-600">{method.description}</div>
                        </div>
                        {selectedPaymentMethod === method && (
                          <div className="w-6 h-6 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                            <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                            </svg>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* 支付说明 */}
            {selectedSku && (
              <div className="mt-4 p-4 bg-gradient-to-r from-blue-50 to-green-50 border border-blue-200 rounded-xl">
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-blue-400 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-blue-900 text-xs">💳</span>
                  </div>
                  <div className="text-sm text-blue-800">
                    <div className="font-bold mb-1">支付流程</div>
                    <div className="text-xs leading-relaxed">
                      • 点击"立即支付"将跳转到安全的支付页面<br/>
                      • 完成支付后系统将自动处理订单<br/>
                      • 支付成功后金币将立即到账，可立即使用
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 固定在底部的支付按钮 */}
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 shadow-lg z-50">
        <div className="max-w-full md:max-w-[768px] lg:max-w-[1024px] xl:max-w-[1200px] mx-auto px-4 py-4">
          {/* 调试信息 */}
          <div className="text-xs text-gray-500 mb-2">
            调试: SKU={selectedSku?.skuid || 'null'}, 支付方式={selectedPaymentMethod?.id || 'null'}
          </div>
          {selectedSku && selectedPaymentMethod ? (
            <div className="space-y-3">
              {/* 订单摘要 */}
              <div className="flex items-center justify-between text-sm">
                <div className="flex items-center space-x-2">
                  <span className="text-gray-600">充值金额:</span>
                  <span className="font-bold text-gray-900">¥{selectedSku.price}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-gray-600">获得:</span>
                  <span className="font-bold text-blue-600">
                    {selectedSku.coins + selectedSku.bonus} 金币
                  </span>
                </div>
              </div>

              {/* 支付按钮 */}
              <button
                onClick={(e) => {
                  console.log('🔄 支付按钮被点击!', e);
                  handleRecharge();
                }}
                disabled={loading}
                className={`w-full py-4 rounded-xl font-bold text-lg transition-all duration-300 transform ${
                  loading
                    ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                    : 'bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700 hover:scale-105 shadow-lg hover:shadow-xl'
                }`}
                style={{ pointerEvents: 'auto', zIndex: 1000 }}
              >
                {loading ? (
                  <div className="flex items-center justify-center space-x-2">
                    <div className="animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent"></div>
                    <span>支付中...</span>
                  </div>
                ) : (
                  <div className="flex items-center justify-center space-x-2">
                    <span>立即支付</span>
                  </div>
                )}
              </button>
            </div>
          ) : (
            <div className="text-center py-2">
              <div className="text-gray-500 text-sm mb-2">
                {!selectedSku ? '请选择充值套餐' : '请选择支付方式'}
              </div>
              <button
                disabled
                className="w-full py-3 rounded-xl font-medium text-gray-400 bg-gray-100 cursor-not-allowed"
              >
                请完成选择
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default RechargePage;
