import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import IOSInstallPrompt from '../components/IOSInstallPrompt';
import { toast } from '../store/toastStore';

const TestIOSPromptPage: React.FC = () => {
  const navigate = useNavigate();
  const [showPrompt, setShowPrompt] = useState(false);
  const [promptSettings, setPromptSettings] = useState({
    autoShow: false,
    onDismiss: () => {
      setShowPrompt(false);
      toast.info('安装提示已关闭');
    }
  });

  const handleShowPrompt = () => {
    setShowPrompt(true);
    toast.success('显示iOS安装提示');
  };

  const handleHidePrompt = () => {
    setShowPrompt(false);
    toast.info('隐藏iOS安装提示');
  };

  const simulateIOSEnvironment = () => {
    // 模拟iOS Safari环境
    Object.defineProperty(navigator, 'userAgent', {
      value: 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1',
      configurable: true
    });
    
    toast.success('已模拟iOS Safari环境');
  };

  const resetEnvironment = () => {
    // 重新加载页面以重置环境
    window.location.reload();
  };

  return (
    <div className="max-w-md mx-auto bg-white min-h-screen">
      {/* 头部 */}
      <div className="bg-gradient-to-r from-blue-500 to-indigo-600 text-white px-4 py-6">
        <div className="flex items-center space-x-3">
          <button
            onClick={() => navigate(-1)}
            className="w-10 h-10 bg-white bg-opacity-20 rounded-full flex items-center justify-center"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <div>
            <h1 className="text-xl font-bold">iOS提示测试</h1>
            <p className="text-blue-100 text-sm">测试iOS安装提示组件</p>
          </div>
        </div>
      </div>

      {/* 测试控制面板 */}
      <div className="p-4 space-y-4">
        <div className="bg-gray-50 rounded-lg p-4">
          <h2 className="font-semibold text-gray-900 mb-3">测试控制</h2>
          
          <div className="space-y-3">
            <button
              onClick={handleShowPrompt}
              className="w-full bg-blue-500 hover:bg-blue-600 text-white py-3 px-4 rounded-lg font-medium transition-colors"
            >
              📱 显示iOS安装提示
            </button>
            
            <button
              onClick={handleHidePrompt}
              className="w-full bg-gray-500 hover:bg-gray-600 text-white py-3 px-4 rounded-lg font-medium transition-colors"
            >
              ❌ 隐藏安装提示
            </button>
            
            <button
              onClick={simulateIOSEnvironment}
              className="w-full bg-green-500 hover:bg-green-600 text-white py-3 px-4 rounded-lg font-medium transition-colors"
            >
              🍎 模拟iOS环境
            </button>
            
            <button
              onClick={resetEnvironment}
              className="w-full bg-orange-500 hover:bg-orange-600 text-white py-3 px-4 rounded-lg font-medium transition-colors"
            >
              🔄 重置环境
            </button>
          </div>
        </div>

        {/* 当前环境信息 */}
        <div className="bg-gray-50 rounded-lg p-4">
          <h2 className="font-semibold text-gray-900 mb-3">环境信息</h2>
          
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600">User Agent:</span>
              <span className="font-mono text-xs text-gray-800 max-w-48 truncate">
                {navigator.userAgent}
              </span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-gray-600">屏幕尺寸:</span>
              <span className="font-medium">
                {window.innerWidth} × {window.innerHeight}
              </span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-gray-600">设备像素比:</span>
              <span className="font-medium">
                {window.devicePixelRatio}
              </span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-gray-600">Standalone模式:</span>
              <span className="font-medium">
                {(window.navigator as any).standalone ? '是' : '否'}
              </span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-gray-600">显示模式:</span>
              <span className="font-medium">
                {window.matchMedia('(display-mode: standalone)').matches ? 'standalone' : 'browser'}
              </span>
            </div>
          </div>
        </div>

        {/* 测试说明 */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="font-semibold text-blue-800 mb-2">测试说明</h3>
          <ul className="text-blue-700 text-sm space-y-1">
            <li>• 点击"显示iOS安装提示"可以手动显示提示框</li>
            <li>• 提示框会自动避开底部导航栏</li>
            <li>• 在不同屏幕尺寸下测试响应式效果</li>
            <li>• 模拟iOS环境可以测试检测逻辑</li>
            <li>• 提示框应该显示在底部导航栏上方</li>
          </ul>
        </div>

        {/* 布局测试区域 */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <h3 className="font-semibold text-yellow-800 mb-2">布局测试</h3>
          <p className="text-yellow-700 text-sm mb-3">
            这个区域用于测试提示框是否正确显示在底部导航栏上方。
          </p>
          
          <div className="space-y-2">
            {Array.from({ length: 10 }, (_, i) => (
              <div key={i} className="bg-white p-3 rounded border">
                <p className="text-gray-600">测试内容行 {i + 1}</p>
              </div>
            ))}
          </div>
        </div>

        {/* 底部间距 */}
        <div className="h-20"></div>
      </div>

      {/* iOS安装提示组件 */}
      {showPrompt && (
        <IOSInstallPrompt
          autoShow={false}
          onDismiss={promptSettings.onDismiss}
        />
      )}
    </div>
  );
};

export default TestIOSPromptPage;
