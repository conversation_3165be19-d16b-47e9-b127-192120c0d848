import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { globalApiService } from '../services/globalApiService';
import { useAuthStore } from '../store/authStore';
import { toast } from '../store/toastStore';
import { useDebouncedAction } from '../hooks/useDebounce';
import type { Video } from '../types';
import MobileLayout from '../components/MobileLayout';
import PurchaseModal from '../components/PurchaseModal';
import { useAnalytics } from '../hooks/useAnalytics';
import {
  VideoPlayer,
  VideoInfo,
  LoadingState,
  ErrorState
} from '../components/VideoDetail';
import {debounce} from 'lodash-es';

  const updateFavoriteState = debounce(async (videoID: number, isFavorited: boolean) => {
    try {
      if (isFavorited) {
        await globalApiService.addFavorite(videoID);
      } else {
        await globalApiService.removeFavorite(videoID);
      }
      console.log('收藏状态已同步到服务器');
    } catch (error) {
      console.error('收藏操作失败:', error);
    }
  }, 500);

const VideoDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const { user, updateUser } = useAuthStore();
  const [video, setVideo] = useState<Video | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showPreviewEndModal, setShowPreviewEndModal] = useState(false);

  // 使用防抖 Hook
  const favoriteAction = useDebouncedAction(300);

  // 使用分析 Hook
  const { trackVideo, trackUser, trackPurchaseEvent, trackErrorEvent, trackCustomEvent } = useAnalytics();

  useEffect(() => {
    if (id) {
      loadVideoDetail(parseInt(id));
      console.log('loadVideoDetail',id);
    }
  }, [id]);

  // 清理防抖定时器
  useEffect(() => {
    return () => {
      favoriteAction.cleanup();
    };
  }, [favoriteAction]);

  const loadVideoDetail = async (videoId: number) => {
    try {
      setLoading(true);
      const videoData = await globalApiService.getVideoDetail(videoId);
      setVideo(videoData);

      // 跟踪视频详情页面访问
      trackCustomEvent('video_detail_view', 'video', videoData.title, undefined, {
        video_id: videoId,
        video_title: videoData.title,
        video_price: videoData.price || 0,
        is_vip_video: (videoData.price || 0) > 0,
      });
    } catch (error: any) {
      setError(error.message || '加载视频详情失败');

      // 跟踪错误
      trackErrorEvent(error.message || '加载视频详情失败', 'VideoDetailPage.loadVideoDetail');
    } finally {
      setLoading(false);
    }
  };

  const handlePurchase = async () => {
    if (!video) return;

    // 移除认证检查，所有用户都可以购买
    try {
      // 直接购买视频（支付成功后创建订单）
      const result = await globalApiService.purchaseVideo(video.id);

      // 跟踪购买事件
      trackPurchaseEvent(
        result.order.id.toString(),
        video.price || 0,
        'CNY',
        [{
          item_id: video.id.toString(),
          item_name: video.title,
          category: 'video',
          quantity: 1,
          price: video.price || 0,
        }]
      );

      // 跟踪用户购买行为
      trackUser('purchase', {
        video_id: video.id,
        video_title: video.title,
        video_price: video.price || 0,
        order_id: result.order.id,
      });

      // 更新用户金币余额
      const updatedUser = await globalApiService.getUserInfo();
      updateUser(updatedUser);

      // 只更新本地状态，避免重新渲染视频
      setVideo(prevVideo => {
        if (!prevVideo) return prevVideo;
        return {
          ...prevVideo,
          hasPurchased: true
        };
      });

      // 关闭购买弹框
      setShowPreviewEndModal(false);
      toast.success(`购买成功！现在可以观看《${video.title}》完整视频了`);
    } catch (error: any) {
      // 跟踪购买失败
      trackErrorEvent(error.message || '购买失败', 'VideoDetailPage.handlePurchase');

      toast.error(error.message || '购买失败，请重试');
    }
  };



  const handleToggleFavorite = async () => {
    if (!video) return;

    const newFavoriteState = !video.isFavorited;
    console.log('收藏按钮被点击，新状态:', newFavoriteState);

    // 跟踪收藏行为
    trackUser('favorite', {
      video_id: video.id,
      video_title: video.title,
      action: newFavoriteState ? 'add' : 'remove',
    });

    setVideo(prevVideo => {
      if (!prevVideo) return prevVideo;
      return {
        ...prevVideo,
        isFavorited: newFavoriteState
      };
    });
    updateFavoriteState(video.id, newFavoriteState);
  };

  const canWatch = (video: Video): boolean => {
    // 免费视频（价格为0）
    if (!video.price || video.price === 0) return true;

    // VIP用户可以观看所有视频
    if (user?.isVip) return true;

    // 付费视频需要购买
    return Boolean(video.hasPurchased);
  };

  if (loading) {
    return <LoadingState />;
  }

  if (error || !video) {
    return <ErrorState error={error} />;
  }

  return (
    <MobileLayout
      title={video.title}
      showBack
      showTabBar={false}
    >
      <div className="px-4 pb-4 pt-4">
        {/* 视频播放器区域 */}
        <VideoPlayer
          video={video}
          isPaidVideo={video.price ? video.price > 0 : false}
          hasAccess={canWatch(video)}
          shouldPause={showPreviewEndModal}
          onPreviewEnd={() => {
            setShowPreviewEndModal(true)
          }}
          onVideoError={(error: string) => console.error('视频播放错误:', error)}
        />

        {/* 视频信息 */}
        <VideoInfo
          video={video}
          onToggleFavorite={handleToggleFavorite}
          onPriceClick={() => {
            // 如果已购买或用户是VIP，不显示购买弹框
            if (!video.hasPurchased && !user?.isVip) {
              setShowPreviewEndModal(true);
            }
          }}
        />
      </div>

      {/* 购买弹框 */}
      <PurchaseModal
        isOpen={showPreviewEndModal}
        video={video}
        onClose={() => setShowPreviewEndModal(false)}
        onPurchase={handlePurchase}
        previewDuration={1}
      />
    </MobileLayout>
  );
};

export default VideoDetailPage;
