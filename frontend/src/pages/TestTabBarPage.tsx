import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import TabBar from '../components/TabBar';
import { toast } from '../store/toastStore';

const TestTabBarPage: React.FC = () => {
  const navigate = useNavigate();
  const [darkMode, setDarkMode] = useState(false);

  const toggleDarkMode = () => {
    setDarkMode(!darkMode);
    toast.info(`TabBar ${!darkMode ? '黑色' : '白色'}模式`);
  };

  const goToShorts = () => {
    navigate('/shorts');
    toast.info('跳转到短视频页面（黑色TabBar）');
  };

  const goToHome = () => {
    navigate('/');
    toast.info('跳转到首页（白色TabBar）');
  };

  return (
    <div className={`min-h-screen ${darkMode ? 'bg-black' : 'bg-gray-50'}`}>
      {/* 头部 */}
      <div className={`px-4 py-6 navbar-safe-top ${
        darkMode 
          ? 'bg-gray-900 text-white' 
          : 'bg-gradient-to-r from-blue-500 to-purple-600 text-white'
      }`}>
        <div className="flex items-center space-x-3 safe-area-left safe-area-right">
          <button
            onClick={() => navigate(-1)}
            className="w-10 h-10 bg-white bg-opacity-20 rounded-full flex items-center justify-center touch-button"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <div>
            <h1 className="text-xl font-bold">TabBar测试</h1>
            <p className={`text-sm ${darkMode ? 'text-gray-300' : 'text-blue-100'}`}>
              底部导航栏样式验证
            </p>
          </div>
        </div>
      </div>

      {/* 内容区域 */}
      <div className="p-4 space-y-4 pb-16">
        {/* 控制面板 */}
        <div className={`rounded-lg p-4 ${
          darkMode ? 'bg-gray-800 border border-gray-700' : 'bg-white border border-gray-200'
        }`}>
          <h2 className={`font-semibold mb-3 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
            TabBar控制
          </h2>
          
          <div className="space-y-3">
            <button
              onClick={toggleDarkMode}
              className={`w-full py-3 px-4 rounded-lg font-medium transition-colors ${
                darkMode
                  ? 'bg-white text-black hover:bg-gray-100'
                  : 'bg-black text-white hover:bg-gray-800'
              }`}
            >
              {darkMode ? '🌞 切换到白色模式' : '🌙 切换到黑色模式'}
            </button>
          </div>
        </div>

        {/* 页面跳转测试 */}
        <div className={`rounded-lg p-4 ${
          darkMode ? 'bg-gray-800 border border-gray-700' : 'bg-blue-50 border border-blue-200'
        }`}>
          <h2 className={`font-semibold mb-3 ${
            darkMode ? 'text-white' : 'text-blue-900'
          }`}>
            页面跳转测试
          </h2>
          
          <div className="space-y-3">
            <button
              onClick={goToShorts}
              className="w-full bg-black text-white py-3 px-4 rounded-lg font-medium transition-colors hover:bg-gray-800"
            >
              📱 短视频页面 (黑色TabBar)
            </button>
            
            <button
              onClick={goToHome}
              className="w-full bg-white text-black border border-gray-300 py-3 px-4 rounded-lg font-medium transition-colors hover:bg-gray-50"
            >
              🏠 首页 (白色TabBar)
            </button>
          </div>
        </div>

        {/* TabBar高度信息 */}
        <div className={`rounded-lg p-4 ${
          darkMode ? 'bg-gray-800 border border-gray-700' : 'bg-green-50 border border-green-200'
        }`}>
          <h2 className={`font-semibold mb-3 ${
            darkMode ? 'text-white' : 'text-green-900'
          }`}>
            TabBar高度信息
          </h2>
          
          <div className={`space-y-2 text-sm ${
            darkMode ? 'text-gray-300' : 'text-green-700'
          }`}>
            <div className="flex justify-between">
              <span>移动端高度:</span>
              <span className="font-medium">48px (优化后)</span>
            </div>
            <div className="flex justify-between">
              <span>桌面端高度:</span>
              <span className="font-medium">64px</span>
            </div>
            <div className="flex justify-between">
              <span>底部内边距:</span>
              <span className="font-medium">48px (移动端)</span>
            </div>
            <div className="flex justify-between">
              <span>安全区域适配:</span>
              <span className="font-medium">❌ 已移除</span>
            </div>
          </div>
        </div>

        {/* 视觉测试区域 */}
        <div className={`rounded-lg p-4 ${
          darkMode ? 'bg-gray-800 border border-gray-700' : 'bg-yellow-50 border border-yellow-200'
        }`}>
          <h2 className={`font-semibold mb-3 ${
            darkMode ? 'text-white' : 'text-yellow-900'
          }`}>
            视觉测试
          </h2>
          <p className={`text-sm mb-4 ${
            darkMode ? 'text-gray-300' : 'text-yellow-700'
          }`}>
            观察底部TabBar的样式和高度：
          </p>
          
          <div className="space-y-2">
            {Array.from({ length: 8 }, (_, i) => (
              <div 
                key={i} 
                className={`p-3 rounded border ${
                  darkMode 
                    ? 'bg-gray-700 border-gray-600 text-white' 
                    : 'bg-white border-gray-200 text-gray-800'
                }`}
              >
                <p>测试内容行 {i + 1} - 检查底部是否被TabBar遮挡</p>
              </div>
            ))}
          </div>
        </div>

        {/* 短视频模拟区域 */}
        <div className="bg-black rounded-lg p-4 text-white">
          <h2 className="font-semibold mb-3">短视频模拟区域</h2>
          <p className="text-gray-300 text-sm mb-4">
            这个区域模拟短视频页面的黑色背景，观察TabBar是否与背景融合：
          </p>
          
          <div className="aspect-video bg-gray-900 rounded-lg flex items-center justify-center mb-4">
            <div className="text-center">
              <div className="text-4xl mb-2">🎬</div>
              <p className="text-gray-400">短视频播放区域</p>
            </div>
          </div>
          
          <div className="flex items-center justify-between text-sm text-gray-400">
            <span>👤 用户名</span>
            <div className="flex space-x-4">
              <span>❤️ 1.2k</span>
              <span>💬 88</span>
              <span>📤 分享</span>
            </div>
          </div>
        </div>

        {/* 测试说明 */}
        <div className={`rounded-lg p-4 ${
          darkMode ? 'bg-gray-800 border border-gray-700' : 'bg-purple-50 border border-purple-200'
        }`}>
          <h2 className={`font-semibold mb-3 ${
            darkMode ? 'text-white' : 'text-purple-900'
          }`}>
            测试说明
          </h2>
          <ul className={`text-sm space-y-2 ${
            darkMode ? 'text-gray-300' : 'text-purple-700'
          }`}>
            <li>• <strong>高度优化</strong>：TabBar高度从64px减少到48px</li>
            <li>• <strong>黑色模式</strong>：短视频页面使用黑色TabBar</li>
            <li>• <strong>白色模式</strong>：其他页面使用白色TabBar</li>
            <li>• <strong>底部空白</strong>：减少了底部的空白区域</li>
            <li>• <strong>触摸优化</strong>：保持所有触摸优化功能</li>
          </ul>
        </div>

        {/* 底部间距测试 */}
        <div className={`h-20 rounded-lg flex items-center justify-center ${
          darkMode ? 'bg-gray-700 text-white' : 'bg-gray-200 text-gray-700'
        }`}>
          <p className="text-sm">底部间距测试区域</p>
        </div>
      </div>

      {/* TabBar组件 */}
      <TabBar dark={darkMode} />
    </div>
  );
};

export default TestTabBarPage;
