import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { unifiedApiService } from '../services/unifiedApiService';
import { useAuthStore } from '../store/authStore';
import MobileLayout from '../components/MobileLayout';

const InviteListPage: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuthStore();
  const [loading, setLoading] = useState(true);
  const [invitations, setInvitations] = useState<any[]>([]);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  useEffect(() => {
    // 检查邮箱绑定状态
    if (!user?.email) {
      navigate('/link-email');
      return;
    }
    loadInvitations();
  }, [page, user?.email, navigate]);

  const loadInvitations = async () => {
    try {
      setLoading(true);
      const data = await unifiedApiService.getInviteList(page, 10);
      setInvitations(data.invitations);
      setTotalPages(data.totalPages);
    } catch (error) {
      console.error('加载邀请记录失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    const colors = {
      pending: 'text-yellow-600 bg-yellow-100',
      registered: 'text-blue-600 bg-blue-100',
      email_verified: 'text-green-600 bg-green-100',
      completed: 'text-purple-600 bg-purple-100'
    };
    return colors[status as keyof typeof colors] || 'text-gray-600 bg-gray-100';
  };

  const getStatusText = (status: string) => {
    const texts = {
      pending: '等待注册',
      registered: '已注册',
      email_verified: '已验证邮箱',
      completed: '已完成'
    };
    return texts[status as keyof typeof texts] || '未知状态';
  };

  const getLevelBadge = (level: number) => {
    const colors = [
      'bg-red-100 text-red-600',
      'bg-orange-100 text-orange-600', 
      'bg-yellow-100 text-yellow-600',
      'bg-green-100 text-green-600',
      'bg-blue-100 text-blue-600'
    ];
    return colors[level - 1] || 'bg-gray-100 text-gray-600';
  };

  if (loading && page === 1) {
    return (
      <MobileLayout title="推广记录" showBack showTabBar={false}>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">加载中...</p>
          </div>
        </div>
      </MobileLayout>
    );
  }

  return (
    <MobileLayout title="推广记录" showBack showTabBar={false}>
      <div className="max-w-md mx-auto p-4">
        {invitations.length === 0 ? (
          <div className="bg-white rounded-lg p-8 text-center">
            <div className="text-6xl mb-4">👥</div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">暂无推广记录</h3>
            <p className="text-gray-600 mb-4">
              开始推广赚钱，即可在这里查看推广记录
            </p>
            <button
              onClick={() => navigate('/invite')}
              className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              去推广赚钱
            </button>
          </div>
        ) : (
          <div className="space-y-3">
            {invitations.map((invitation) => (
              <div key={invitation.id} className="bg-white rounded-lg p-4 shadow-sm">
                <div className="flex items-start justify-between mb-3">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-1">
                      <h3 className="font-semibold text-gray-900">
                        {invitation.invitee?.nickname || '未知用户'}
                      </h3>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getLevelBadge(invitation.level)}`}>
                        {invitation.level}级
                      </span>
                    </div>
                    {invitation.invitee?.email && (
                      <p className="text-sm text-gray-600">{invitation.invitee.email}</p>
                    )}
                  </div>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(invitation.status)}`}>
                    {getStatusText(invitation.status)}
                  </span>
                </div>

                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-500">注册时间:</span>
                    <div className="font-medium">
                      {invitation.registeredAt 
                        ? new Date(invitation.registeredAt).toLocaleDateString()
                        : '未注册'
                      }
                    </div>
                  </div>
                  <div>
                    <span className="text-gray-500">验证时间:</span>
                    <div className="font-medium">
                      {invitation.emailVerifiedAt 
                        ? new Date(invitation.emailVerifiedAt).toLocaleDateString()
                        : '未验证'
                      }
                    </div>
                  </div>
                </div>

                {invitation.totalCommissionEarned > 0 && (
                  <div className="mt-3 pt-3 border-t border-gray-100">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">累计返佣:</span>
                      <span className="font-semibold text-green-600">
                        {invitation.totalCommissionEarned} 金币
                      </span>
                    </div>
                  </div>
                )}

                {invitation.vipRewardGiven && (
                  <div className="mt-2">
                    <div className="flex items-center space-x-2 text-sm">
                      <span className="text-yellow-600">👑</span>
                      <span className="text-yellow-600 font-medium">已获得VIP奖励</span>
                    </div>
                  </div>
                )}
              </div>
            ))}

            {/* 分页 */}
            {totalPages > 1 && (
              <div className="flex justify-center space-x-2 mt-6">
                <button
                  onClick={() => setPage(Math.max(1, page - 1))}
                  disabled={page === 1}
                  className="px-4 py-2 bg-white border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                >
                  上一页
                </button>
                <span className="px-4 py-2 bg-blue-600 text-white rounded-lg">
                  {page} / {totalPages}
                </span>
                <button
                  onClick={() => setPage(Math.min(totalPages, page + 1))}
                  disabled={page === totalPages}
                  className="px-4 py-2 bg-white border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                >
                  下一页
                </button>
              </div>
            )}
          </div>
        )}
      </div>
    </MobileLayout>
  );
};

export default InviteListPage;
