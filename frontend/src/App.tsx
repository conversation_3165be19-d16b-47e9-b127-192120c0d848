import React, { useEffect, useState } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate, useSearchParams } from 'react-router-dom';
import { useAuthStore } from './store/authStore';
import useToastStore from './store/toastStore';
import './utils/hlsKeyMonitor'; // 自动启用HLS密钥监控
import { initGA } from './utils/analytics'; // 谷歌分析
import { secureDeviceManager } from './utils/secureDeviceManager'; // 安全设备管理器
import useDecryptionErrorHandler from './hooks/useDecryptionErrorHandler';
import DecryptionErrorDialog from './components/DecryptionErrorDialog';
import { unifiedApiService } from './services/unifiedApiService'; // 统一加密API服务
import { encryptionManager } from './services/encryptionManager'; // 加密管理器
import { optimizeWebClipExperience, applyWebClipCSSVariables, watchDisplayModeChange } from './utils/webClipHelper'; // WebClip优化
import ScrollRestoration from './components/ScrollRestoration'; // 滚动恢复组件
import { initSafeAreaAdaptation } from './utils/iosSafeAreaHelper'; // iOS安全区域适配
import { initIOSTouchOptimization } from './utils/iosTouchOptimizer'; // iOS触摸优化
import { initDynamicBackgroundManager } from './utils/dynamicBackgroundManager'; // 动态背景管理
import MobileLayout from './components/MobileLayout';
import RouteGuard from './components/RouteGuard';
import ToastContainer from './components/ToastContainer';
import EncryptionFloatingButton from './components/EncryptionFloatingButton';
import IOSInstallPrompt from './components/IOSInstallPrompt';


import MobileHomePage from './pages/MobileHomePage';
import VideoDetailPage from './pages/VideoDetailPage';
import MobileVideoListPage from './pages/MobileVideoListPage';
import DiscoverPage from './pages/DiscoverPage';
import ShortsPage from './pages/ShortsPage';
import ProfilePage from './pages/ProfilePage';
import LinkEmailPage from './pages/LinkEmailPage';
import MergeAccountPage from './pages/MergeAccountPage';
import InvitePage from './pages/InvitePage';
import InviteListPage from './pages/InviteListPage';
import CommissionListPage from './pages/CommissionListPage';
import FavoritesPage from './pages/FavoritesPage';
import PurchasedPage from './pages/PurchasedPage';
import HistoryPage from './pages/HistoryPage';
import OrdersPage from './pages/OrdersPage';
import SettingsPage from './pages/SettingsPage';
import UserProfilePage from './pages/UserProfilePage';
import WebClipSettingsPage from './pages/WebClipSettingsPage';
import TestIOSPromptPage from './pages/TestIOSPromptPage';
import TestSafeAreaPage from './pages/TestSafeAreaPage';
import TestTouchOptimizationPage from './pages/TestTouchOptimizationPage';
import TestTabBarPage from './pages/TestTabBarPage';
import TestDynamicBackgroundPage from './pages/TestDynamicBackgroundPage';
import RechargePage from './pages/RechargePage';
import VipPage from './pages/VipPage';
import PaymentPage from './pages/PaymentPage';
import PaymentCallbackPage from './pages/PaymentCallbackPage';
import ScrollTestPage from './pages/ScrollTestPage';
import CacheTestPage from './pages/CacheTestPage';
import DeviceIdTestPage from './pages/DeviceIdTestPage';
import CoinRewardTestPage from './pages/CoinRewardTestPage';
import DecryptionErrorTestPage from './pages/DecryptionErrorTestPage';
import PurchaseModalTestPage from './pages/PurchaseModalTestPage';
import LayoutTestPage from './pages/LayoutTestPage';
import RechargeTestPage from './pages/RechargeTestPage';


// 视频列表页面包装器，用于获取URL参数并设置标题
const VideoListPageWrapper: React.FC = () => {
  const [searchParams] = useSearchParams();

  const getPageTitle = () => {
    const title = searchParams.get('title');
    const keyword = searchParams.get('keyword');
    const category = searchParams.get('category');
    const type = searchParams.get('type');

    if (title) return title;
    if (keyword) return `搜索: ${keyword}`;
    if (category) return category;
    if (type === 'recommended') return '推荐视频';
    if (type === 'hot') return '热门视频';
    if (type === 'free') return '免费观看';
    if (type === 'vip') return 'VIP专享';
    if (type === 'latest') return '最新上传';
    return '全部视频';
  };

  return (
    <MobileLayout title={getPageTitle()} showBack={true} showTabBar={false}>
      <MobileVideoListPage />
    </MobileLayout>
  );
};

// 受保护的路由组件（现在只是一个包装器，不再强制要求认证）
const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // 移除认证检查，所有用户（包括游客）都可以访问
  return <>{children}</>;
};



function App() {
  const { toasts, removeToast } = useToastStore();
  const [isInitialized, setIsInitialized] = useState(false);
  const [isInitializing, setIsInitializing] = useState(false);
  const [webClipInfo, setWebClipInfo] = useState<any>(null);

  // 解密错误处理
  const {
    errorInfo,
    showClearDataDialog,
    handleDecryptionError,
    clearDataAndRefresh
  } = useDecryptionErrorHandler();

  // 初始化谷歌分析、设备管理器和WebClip优化
  useEffect(() => {
    initGA();

    // 监听设备不一致事件
    const handleDeviceInconsistency = () => {
      // 可以在这里添加用户提示或重新验证逻辑
    };

    // 监听解密错误事件
    const handleDecryptionErrorEvent = (event: CustomEvent) => {
      console.log('🔐 收到解密错误事件:', event.detail);
      handleDecryptionError({ response: { data: event.detail } });
    };

    window.addEventListener('deviceInconsistency', handleDeviceInconsistency as EventListener);
    window.addEventListener('decryptionError', handleDecryptionErrorEvent as EventListener);

    // 确保安全设备管理器已初始化
    secureDeviceManager.getDeviceId();

    // 初始化iOS安全区域适配
    const safeAreaCleanup = initSafeAreaAdaptation();

    // 初始化iOS触摸优化
    const touchOptimizationCleanup = initIOSTouchOptimization({
      disableHighlight: true,
      disableCallout: true,
      disableSelection: true,
      enableFastClick: true,
      customTouchFeedback: true
    });

    // 初始化动态背景管理器
    const backgroundManagerCleanup = initDynamicBackgroundManager();

    // 初始化WebClip优化
    const webClipEnvironment = optimizeWebClipExperience();
    setWebClipInfo(webClipEnvironment);

    // 应用WebClip CSS变量
    applyWebClipCSSVariables();

    // 监听显示模式变化
    watchDisplayModeChange((info) => {
      setWebClipInfo(info);
      applyWebClipCSSVariables();

      if (info.isWebClip) {
        console.log('🎯 切换到WebClip模式');
        document.body.classList.add('webclip-mode');
      } else {
        console.log('📱 切换到浏览器模式');
        document.body.classList.remove('webclip-mode');
      }
    });

    // 清理函数
    return () => {
      window.removeEventListener('deviceInconsistency', handleDeviceInconsistency as EventListener);
      window.removeEventListener('decryptionError', handleDecryptionErrorEvent as EventListener);
      safeAreaCleanup(); // 清理安全区域监听器
      touchOptimizationCleanup(); // 清理触摸优化
      backgroundManagerCleanup(); // 清理动态背景管理器
    };
  }, [handleDecryptionError]);

  useEffect(() => {
    // 初始化检查 - 只在组件挂载时执行一次
    const checkAccountStatus = async () => {
      if (isInitializing) {
        return;
      }

      setIsInitializing(true);

      try {
        // 1. 首先等待加密系统就绪
        await encryptionManager.waitForReady();
      } catch (encryptionError) {
        // 加密系统初始化失败，但继续应用启动
      }

      try {
        // 检查URL中是否有邀请码
        const urlParams = new URLSearchParams(window.location.search);
        const inviteCode = urlParams.get('invite');

        const accountSetup = localStorage.getItem('accountSetup');
        const storedToken = localStorage.getItem('token');
        const storedUser = localStorage.getItem('user');

        // 安全解析用户数据
        if (storedUser) {
          try {
            JSON.parse(storedUser);
          } catch {
            localStorage.removeItem('user');
          }
        }



        // 如果有邀请码，强制重新创建账号
        if (inviteCode) {
          try {
            // 清除现有状态
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            localStorage.removeItem('guestId');

            // 使用邀请码创建新账号
            const { initializeGuest } = useAuthStore.getState();
            await initializeGuest(inviteCode);

            // 清除URL参数
            window.history.replaceState({}, document.title, window.location.pathname);

            return;
          } catch (error) {
            console.error('处理邀请码失败:', error);
          }
        }
        // 如果有完整的账号信息，但store中没有，则恢复状态并同步最新用户信息
        const { isAuthenticated } = useAuthStore.getState();
        if (accountSetup && storedToken && storedUser && !isAuthenticated) {
          try {
            let parsedUser;
            try {
              parsedUser = JSON.parse(storedUser);
            } catch {
              localStorage.removeItem('user');
              localStorage.removeItem('token');
              localStorage.removeItem('accountSetup');
              throw new Error('用户数据格式错误');
            }

            // 先恢复基本状态
            useAuthStore.setState({
              user: parsedUser,
              token: storedToken,
              isAuthenticated: true,
              isGuest: parsedUser.isGuest,
            });

            // 然后从服务器获取最新用户信息
            try {
              const updatedUser = await unifiedApiService.getUserInfo();

              // 更新用户信息
              const { updateUser } = useAuthStore.getState();
              updateUser(updatedUser);

            } catch (syncError: any) {
              // 如果同步失败，可能是认证问题，清除状态防止循环
              if (syncError.message?.includes('401') ||
                  syncError.message?.includes('登录') ||
                  syncError.response?.status === 401) {
                localStorage.removeItem('token');
                localStorage.removeItem('user');
                localStorage.removeItem('accountSetup');
                useAuthStore.setState({
                  user: null,
                  token: null,
                  isAuthenticated: false,
                  isGuest: false,
                });

                // 重新初始化游客账号
                try {
                  const { initializeGuest } = useAuthStore.getState();
                  await initializeGuest();
                  localStorage.setItem('accountSetup', 'true');
                } catch (initError) {
                  // 重新初始化失败
                }
              }
            }
          } catch (error) {
            console.error('恢复用户状态失败:', error);
            // 清除损坏的数据
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            localStorage.removeItem('accountSetup');
          }
        }
        // 需要创建新账号或通过设备ID恢复账号
        else if (!accountSetup || !storedToken || !storedUser) {
          try {
            const { initializeGuest } = useAuthStore.getState();
            await initializeGuest();

            // 标记账号已设置
            localStorage.setItem('accountSetup', 'true');
          } catch (error) {
            // 账号初始化失败
          }
        }
        // 已有账号且状态正常，同步最新用户信息
        else {
          // 如果用户已认证，同步最新用户信息
          const currentState = useAuthStore.getState();
          if (currentState.isAuthenticated && currentState.user) {
            try {
              const updatedUser = await unifiedApiService.getUserInfo();

              // 更新用户信息
              const { updateUser } = useAuthStore.getState();
              updateUser(updatedUser);
            } catch (syncError: any) {
              // 如果是认证错误，清除状态并重新初始化
              if (syncError.message?.includes('401') ||
                  syncError.message?.includes('登录') ||
                  syncError.response?.status === 401) {
                localStorage.removeItem('token');
                localStorage.removeItem('user');
                localStorage.removeItem('accountSetup');

                // 重置认证状态
                useAuthStore.setState({
                  user: null,
                  token: null,
                  isAuthenticated: false,
                  isGuest: true,
                });

                // 重新初始化游客账号
                try {
                  const { initializeGuest } = useAuthStore.getState();
                  await initializeGuest();
                  localStorage.setItem('accountSetup', 'true');
                } catch {
                  // 重新初始化失败
                }
              }
            }
          }
        }
      } catch {
        // 账号状态检查失败
      } finally {
        // 无论如何都要设置为已初始化，防止卡在初始化页面
        setIsInitializing(false);
        setIsInitialized(true);
      }
    };

    checkAccountStatus();
  }, []); // 移除依赖，只在组件挂载时执行一次

  // 如果还在初始化中，显示加载状态
  if (!isInitialized) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-pink-50 to-purple-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-6xl mb-4">🎬</div>
          <div className="text-lg text-gray-600">正在初始化...</div>
        </div>
      </div>
    );
  }

  return (
    <Router>
      {/* Toast容器 */}
      <ToastContainer toasts={toasts} onRemoveToast={removeToast} />

      {/* iOS WebClip安装提示 */}
      <IOSInstallPrompt />

      {/* 加密控制浮动按钮 */}
      <EncryptionFloatingButton />

      {/* 解密错误对话框 */}
      <DecryptionErrorDialog
        isOpen={showClearDataDialog}
        errorInfo={errorInfo}
        onClearDataAndRefresh={clearDataAndRefresh}
      />

      {/* 滚动恢复组件 - 自动处理路由跳转时的滚动位置重置 */}
      <ScrollRestoration
        preserveScrollPaths={['/shorts']} // 短视频页面保持滚动位置
        delay={50} // 稍微延迟重置，确保页面内容已渲染
      />

      <Routes>
        {/* 重定向已移除的登录注册页面到首页 - 最高优先级 */}
        <Route path="/login" element={<Navigate to="/" replace />} />
        <Route path="/register" element={<Navigate to="/" replace />} />
        <Route path="/recover" element={<Navigate to="/" replace />} />

        {/* 公开路由 */}
        <Route path="/link-email" element={<LinkEmailPage />} />
        <Route path="/merge-account" element={<MergeAccountPage />} />

        {/* 邀请系统路由 */}
        <Route path="/invite" element={
          <RouteGuard requireHomeVisit={true}>
            <ProtectedRoute>
              <InvitePage />
            </ProtectedRoute>
          </RouteGuard>
        } />
        <Route path="/invite/list" element={
          <RouteGuard requireHomeVisit={true}>
            <ProtectedRoute>
              <InviteListPage />
            </ProtectedRoute>
          </RouteGuard>
        } />
        <Route path="/invite/commissions" element={
          <RouteGuard requireHomeVisit={true}>
            <ProtectedRoute>
              <CommissionListPage />
            </ProtectedRoute>
          </RouteGuard>
        } />

        {/* 主要路由 */}
        <>
            {/* 需要布局的路由 */}
            <Route path="/" element={
              <RouteGuard requireHomeVisit={false}>
                <MobileLayout showSearch={true}>
                  <MobileHomePage />
                </MobileLayout>
              </RouteGuard>
            } />

            <Route path="/discover" element={
              <RouteGuard requireHomeVisit={true}>
                <MobileLayout showTabBar={false} title="发现" showBack>
                  <DiscoverPage />
                </MobileLayout>
              </RouteGuard>
            } />

            <Route path="/shorts" element={
              <RouteGuard requireHomeVisit={true}>
                <MobileLayout showTabBar={true} fullscreen={true} hideTopBar={true} darkTabBar={true}>
                  <ShortsPage />
                </MobileLayout>
              </RouteGuard>
            } />

            <Route path="/videos" element={
              <RouteGuard requireHomeVisit={true}>
                <VideoListPageWrapper />
              </RouteGuard>
            } />

            <Route path="/video/:id" element={
              <RouteGuard requireHomeVisit={true}>
                <VideoDetailPage />
              </RouteGuard>
            } />

            <Route path="/profile" element={
              <RouteGuard requireHomeVisit={true}>
                <ProtectedRoute>
                  <MobileLayout title="我的">
                    <ProfilePage />
                  </MobileLayout>
                </ProtectedRoute>
              </RouteGuard>
            } />

            <Route path="/recharge" element={
              <RouteGuard requireHomeVisit={true}>
                <ProtectedRoute>
                  <MobileLayout title="充值" showBack={true} showTabBar={false}>
                    <RechargePage />
                  </MobileLayout>
                </ProtectedRoute>
              </RouteGuard>
            } />

            <Route path="/vip" element={
              <RouteGuard requireHomeVisit={true}>
                <ProtectedRoute>
                  <MobileLayout title="VIP会员" showBack={true} showTabBar={false}>
                    <VipPage />
                  </MobileLayout>
                </ProtectedRoute>
              </RouteGuard>
            } />

            <Route path="/favorites" element={
              <RouteGuard requireHomeVisit={true}>
                <ProtectedRoute>
                  <MobileLayout title="我的收藏" showBack={true} showTabBar={false}>
                    <FavoritesPage />
                  </MobileLayout>
                </ProtectedRoute>
              </RouteGuard>
            } />

            <Route path="/purchased" element={
              <RouteGuard requireHomeVisit={true}>
                <ProtectedRoute>
                  <MobileLayout title="已购买" showBack={true} showTabBar={false}>
                    <PurchasedPage />
                  </MobileLayout>
                </ProtectedRoute>
              </RouteGuard>
            } />

            <Route path="/history" element={
              <RouteGuard requireHomeVisit={true}>
                <ProtectedRoute>
                  <MobileLayout title="观看历史" showBack={true} showTabBar={false}>
                    <HistoryPage />
                  </MobileLayout>
                </ProtectedRoute>
              </RouteGuard>
            } />

            <Route path="/orders" element={
              <RouteGuard requireHomeVisit={true}>
                <ProtectedRoute>
                  <MobileLayout title="订单记录" showBack={true} showTabBar={false}>
                    <OrdersPage />
                  </MobileLayout>
                </ProtectedRoute>
              </RouteGuard>
            } />

            <Route path="/settings" element={
              <RouteGuard requireHomeVisit={true}>
                <ProtectedRoute>
                  <MobileLayout title="设置" showBack={true} showTabBar={false}>
                    <SettingsPage />
                  </MobileLayout>
                </ProtectedRoute>
              </RouteGuard>
            } />

            <Route path="/settings/profile" element={
              <RouteGuard requireHomeVisit={true}>
                <ProtectedRoute>
                  <UserProfilePage />
                </ProtectedRoute>
              </RouteGuard>
            } />

            <Route path="/settings/webclip" element={
              <RouteGuard requireHomeVisit={true}>
                <ProtectedRoute>
                  <WebClipSettingsPage />
                </ProtectedRoute>
              </RouteGuard>
            } />

            <Route path="/test/ios-prompt" element={
              <RouteGuard requireHomeVisit={true}>
                <ProtectedRoute>
                  <TestIOSPromptPage />
                </ProtectedRoute>
              </RouteGuard>
            } />

            <Route path="/test/safe-area" element={
              <RouteGuard requireHomeVisit={true}>
                <ProtectedRoute>
                  <TestSafeAreaPage />
                </ProtectedRoute>
              </RouteGuard>
            } />

            <Route path="/test/touch-optimization" element={
              <RouteGuard requireHomeVisit={true}>
                <ProtectedRoute>
                  <TestTouchOptimizationPage />
                </ProtectedRoute>
              </RouteGuard>
            } />

            <Route path="/test/tabbar" element={
              <RouteGuard requireHomeVisit={true}>
                <ProtectedRoute>
                  <TestTabBarPage />
                </ProtectedRoute>
              </RouteGuard>
            } />

            <Route path="/test/dynamic-background" element={
              <RouteGuard requireHomeVisit={true}>
                <ProtectedRoute>
                  <TestDynamicBackgroundPage />
                </ProtectedRoute>
              </RouteGuard>
            } />

            <Route path="/test/scroll" element={
              <RouteGuard requireHomeVisit={true}>
                <ProtectedRoute>
                  <ScrollTestPage />
                </ProtectedRoute>
              </RouteGuard>
            } />

            <Route path="/test/cache" element={
              <RouteGuard requireHomeVisit={true}>
                <ProtectedRoute>
                  <CacheTestPage />
                </ProtectedRoute>
              </RouteGuard>
            } />

            <Route path="/test/device-id" element={
              <RouteGuard requireHomeVisit={true}>
                <ProtectedRoute>
                  <DeviceIdTestPage />
                </ProtectedRoute>
              </RouteGuard>
            } />

            <Route path="/test/coin-reward" element={
              <RouteGuard requireHomeVisit={true}>
                <ProtectedRoute>
                  <CoinRewardTestPage />
                </ProtectedRoute>
              </RouteGuard>
            } />

            <Route path="/test/decryption-error" element={
              <RouteGuard requireHomeVisit={true}>
                <ProtectedRoute>
                  <DecryptionErrorTestPage />
                </ProtectedRoute>
              </RouteGuard>
            } />

            <Route path="/test/purchase-modal" element={
              <RouteGuard requireHomeVisit={true}>
                <ProtectedRoute>
                  <PurchaseModalTestPage />
                </ProtectedRoute>
              </RouteGuard>
            } />

            <Route path="/test/layout" element={
              <RouteGuard requireHomeVisit={true}>
                <ProtectedRoute>
                  <LayoutTestPage />
                </ProtectedRoute>
              </RouteGuard>
            } />

            <Route path="/test/recharge" element={
              <RouteGuard requireHomeVisit={true}>
                <ProtectedRoute>
                  <RechargeTestPage />
                </ProtectedRoute>
              </RouteGuard>
            } />

            {/* 支付相关路由 */}
            <Route path="/payment/pay" element={
              <RouteGuard requireHomeVisit={true}>
                <ProtectedRoute>
                  <PaymentPage />
                </ProtectedRoute>
              </RouteGuard>
            } />

            <Route path="/payment/callback" element={
              <RouteGuard requireHomeVisit={true}>
                <ProtectedRoute>
                  <PaymentCallbackPage />
                </ProtectedRoute>
              </RouteGuard>
            } />



            {/* 404 页面 */}
            <Route path="*" element={
              <MobileLayout title="页面不存在" showBack={true}>
                <div className="flex flex-col items-center justify-center h-full px-4 py-20">
                  <div className="text-6xl mb-4">😵</div>
                  <h1 className="text-2xl font-bold text-gray-900 mb-2">页面不存在</h1>
                  <p className="text-gray-600 mb-8 text-center">抱歉，您访问的页面不存在</p>
                  <button
                    onClick={() => window.location.href = '/'}
                    className="bg-pink-500 text-white px-6 py-3 rounded-lg hover:bg-pink-600 transition-colors"
                  >
                    返回首页
                  </button>
                </div>
              </MobileLayout>
            } />
        </>
      </Routes>
    </Router>
  );
}

export default App;
