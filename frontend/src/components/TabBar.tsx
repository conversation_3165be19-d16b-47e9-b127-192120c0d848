import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuthStore } from '../store/authStore';

interface TabItem {
  key: string;
  label: string;
  icon: string;
  activeIcon: string;
  path: string;
  requireAuth?: boolean;
}

interface TabBarProps {
  dark?: boolean; // 深色模式
}

const TabBar: React.FC<TabBarProps> = ({ dark = false }) => {
  const navigate = useNavigate();
  const location = useLocation();

  const tabs: TabItem[] = [
    {
      key: 'home',
      label: '首页',
      icon: '🏠',
      activeIcon: '🏠',
      path: '/'
    },
    {
      key: 'shorts',
      label: '短视频',
      icon: '📱',
      activeIcon: '📱',
      path: '/shorts'
    },
    {
      key: 'profile',
      label: '我的',
      icon: '👤',
      activeIcon: '👤',
      path: '/profile'
    }
  ];

  const handleTabClick = (tab: TabItem) => {
    // 移除认证检查，所有用户（包括游客）都可以访问个人页面
    navigate(tab.path);
  };

  const isActive = (tab: TabItem) => {
    if (tab.path === '/') {
      return location.pathname === '/';
    }
    return location.pathname.startsWith(tab.path);
  };

  return (
    <div className={`fixed bottom-0 left-0 right-0 z-50 ${
      dark
        ? 'bg-black bg-opacity-80 backdrop-blur-sm border-t border-gray-800'
        : 'bg-white border-t border-gray-100'
    }`}>
      <div className="mx-auto max-w-full md:max-w-[768px] xl:max-w-[1024px] 2xl:max-w-[1200px] md:shadow-lg 2xl:shadow-xl">
        <div className="flex items-center justify-around h-16 md:h-20 px-4 md:px-8 lg:px-12">
        {tabs.map((tab) => (
          <button
            key={tab.key}
            onClick={() => handleTabClick(tab)}
            className={`flex flex-col items-center justify-center flex-1 h-full transition-all duration-200 active:scale-95 touch-button ${
              isActive(tab)
                ? 'text-pink-500'
                : dark
                ? 'text-gray-300 active:text-white'
                : 'text-gray-500 active:text-gray-700'
            }`}
          >
            <div className="text-lg md:text-xl mb-1">
              {isActive(tab) ? tab.activeIcon : tab.icon}
            </div>
            <span className="text-xs md:text-sm font-medium leading-tight">
              {tab.label}
            </span>
          </button>
        ))}
        </div>
      </div>
    </div>
  );
};

export default TabBar;
