import React, { useState, useEffect } from 'react';
import { X, Shield, ShieldOff, Key, RefreshCw, Eye, EyeOff, Settings, Trash2 } from 'lucide-react';
import { useEncryptionStore } from '../store/encryptionStore';

interface EncryptionControlPanelProps {
  onClose: () => void;
  isEncryptionEnabled: boolean;
  onEncryptionToggle: (enabled: boolean) => void;
}

const EncryptionControlPanel: React.FC<EncryptionControlPanelProps> = ({
  onClose,
  isEncryptionEnabled,
  onEncryptionToggle
}) => {
  const [keyInfo, setKeyInfo] = useState<any>(null);
  const [showKeys, setShowKeys] = useState(false);
  const [stats, setStats] = useState<any>(null);
  const { debugMode, setDebugMode } = useEncryptionStore();

  useEffect(() => {
    loadKeyInfo();
    loadStats();

    // 监听密钥刷新事件
    const handleKeysRefreshed = () => {
      console.log('🔑 收到密钥刷新事件，更新界面...');
      loadKeyInfo();
      loadStats();
    };

    window.addEventListener('encryptionKeysRefreshed', handleKeysRefreshed);

    return () => {
      window.removeEventListener('encryptionKeysRefreshed', handleKeysRefreshed);
    };
  }, []);

  const loadKeyInfo = () => {
    try {
      const publicKey = localStorage.getItem('publicKey');
      const keyExchange = localStorage.getItem('keyExchange');
      const keyTimestamp = localStorage.getItem('keyTimestamp');
      
      setKeyInfo({
        publicKey: publicKey ? publicKey.substring(0, 50) + '...' : '未设置',
        keyExchange: keyExchange ? keyExchange.substring(0, 50) + '...' : '未设置',
        timestamp: keyTimestamp ? new Date(parseInt(keyTimestamp)).toLocaleString() : '未知',
        hasKeys: !!(publicKey && keyExchange)
      });
    } catch (error) {
      console.error('加载密钥信息失败:', error);
    }
  };

  const loadStats = async () => {
    try {
      // 从 globalApiService 获取实时统计信息
      const { globalApiService } = await import('../services/globalApiService');
      const encryptionStats = globalApiService.getEncryptionStats();

      // 从 encryptionManager 获取状态信息
      const { encryptionManager } = await import('../services/encryptionManager');
      const managerStatus = encryptionManager.getStatus();

      setStats({
        ...encryptionStats,
        lastEncryptTime: new Date().toLocaleString(),
        hasValidatedOnEntry: managerStatus.hasValidatedOnEntry,
        isManagerReady: managerStatus.isReady
      });
    } catch (error) {
      console.error('加载统计信息失败:', error);
      // 回退到本地存储
      const encryptedRequests = localStorage.getItem('encryptedRequestsCount') || '0';
      const lastEncryptTime = localStorage.getItem('lastEncryptTime');

      setStats({
        totalRequests: 0,
        encryptedRequests: parseInt(encryptedRequests),
        encryptionRate: 0,
        successRate: 0,
        lastEncryptTime: lastEncryptTime ? new Date(parseInt(lastEncryptTime)).toLocaleString() : '从未',
        hasValidatedOnEntry: false,
        isManagerReady: false
      });
    }
  };

  const toggleEncryption = () => {
    const newState = !isEncryptionEnabled;
    onEncryptionToggle(newState);

    // 显示状态变化提示
    console.log(`🔐 加密状态已${newState ? '启用' : '禁用'}`);

    // 刷新统计信息
    setTimeout(() => {
      loadStats();
    }, 100);
  };

  const refreshKeys = async () => {
    try {
      // 使用加密管理器刷新密钥
      const { encryptionManager } = await import('../services/encryptionManager');
      const success = await encryptionManager.refreshKeys();

      if (success) {
        loadKeyInfo();
        loadStats();
        alert('密钥刷新成功！');
      } else {
        alert('密钥刷新失败，请检查网络连接');
      }
    } catch (error) {
      console.error('刷新密钥失败:', error);
      alert('刷新密钥失败，请检查网络连接');
    }
  };

  const clearCache = () => {
    if (confirm('确定要清除所有加密缓存吗？这将需要重新获取密钥。')) {
      localStorage.removeItem('publicKey');
      localStorage.removeItem('keyExchange');
      localStorage.removeItem('keyTimestamp');
      localStorage.removeItem('encryptedRequestsCount');
      localStorage.removeItem('lastEncryptTime');
      
      loadKeyInfo();
      loadStats();
      alert('缓存已清除！');
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full max-h-[80vh] overflow-y-auto">
        {/* 头部 */}
        <div className="flex items-center justify-between p-4 border-b">
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <Settings className="w-5 h-5" />
            加密调试面板
          </h3>
          <button
            onClick={onClose}
            className="p-1 hover:bg-gray-100 rounded"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* 内容 */}
        <div className="p-4 space-y-4">
          {/* 加密状态 */}
          <div className="border rounded-lg p-3">
            <div className="flex items-center justify-between mb-2">
              <span className="font-medium">加密状态</span>
              <div className={`flex items-center gap-1 px-2 py-1 rounded text-sm ${
                isEncryptionEnabled ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'
              }`}>
                {isEncryptionEnabled ? <Shield className="w-4 h-4" /> : <ShieldOff className="w-4 h-4" />}
                {isEncryptionEnabled ? '已启用' : '已禁用'}
              </div>
            </div>
            <button
              onClick={toggleEncryption}
              className={`w-full py-2 px-4 rounded font-medium transition-colors ${
                isEncryptionEnabled 
                  ? 'bg-red-500 hover:bg-red-600 text-white' 
                  : 'bg-green-500 hover:bg-green-600 text-white'
              }`}
            >
              {isEncryptionEnabled ? '禁用加密' : '启用加密'}
            </button>
          </div>

          {/* 密钥信息 */}
          <div className="border rounded-lg p-3">
            <div className="flex items-center justify-between mb-2">
              <span className="font-medium">密钥信息</span>
              <div className="flex items-center gap-2">
                <div className={`w-2 h-2 rounded-full ${keyInfo?.hasKeys ? 'bg-green-400' : 'bg-red-400'}`}
                     title={keyInfo?.hasKeys ? '密钥正常' : '密钥缺失'} />
                <button
                  onClick={() => setShowKeys(!showKeys)}
                  className="p-1 hover:bg-gray-100 rounded"
                >
                  {showKeys ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                </button>
              </div>
            </div>
            
            {keyInfo && (
              <div className="space-y-2 text-sm">
                <div>
                  <span className="text-gray-600">状态: </span>
                  <span className={keyInfo.hasKeys ? 'text-green-600' : 'text-red-600'}>
                    {keyInfo.hasKeys ? '已配置' : '未配置'}
                  </span>
                </div>
                <div>
                  <span className="text-gray-600">更新时间: </span>
                  <span>{keyInfo.timestamp}</span>
                </div>
                {showKeys && (
                  <>
                    <div>
                      <span className="text-gray-600">公钥: </span>
                      <code className="text-xs bg-gray-100 p-1 rounded">{keyInfo.publicKey}</code>
                    </div>
                    <div>
                      <span className="text-gray-600">交换密钥: </span>
                      <code className="text-xs bg-gray-100 p-1 rounded">{keyInfo.keyExchange}</code>
                    </div>
                  </>
                )}
              </div>
            )}
            
            <button
              onClick={refreshKeys}
              className="w-full mt-2 py-2 px-4 bg-blue-500 hover:bg-blue-600 text-white rounded font-medium flex items-center justify-center gap-2"
            >
              <RefreshCw className="w-4 h-4" />
              刷新密钥
            </button>
          </div>

          {/* 统计信息 */}
          {stats && (
            <div className="border rounded-lg p-3">
              <span className="font-medium">统计信息</span>
              <div className="space-y-1 text-sm mt-2">
                <div>
                  <span className="text-gray-600">总请求数: </span>
                  <span>{stats.totalRequests || 0}</span>
                </div>
                <div>
                  <span className="text-gray-600">加密请求数: </span>
                  <span>{stats.encryptedRequests || 0}</span>
                </div>
                <div>
                  <span className="text-gray-600">加密率: </span>
                  <span>{stats.encryptionRate || 0}%</span>
                </div>
                <div>
                  <span className="text-gray-600">成功率: </span>
                  <span>{stats.successRate || 0}%</span>
                </div>
                <div>
                  <span className="text-gray-600">入口验证: </span>
                  <span className={stats.hasValidatedOnEntry ? 'text-green-600' : 'text-orange-600'}>
                    {stats.hasValidatedOnEntry ? '已完成' : '未完成'}
                  </span>
                </div>
                <div>
                  <span className="text-gray-600">管理器状态: </span>
                  <span className={stats.isManagerReady ? 'text-green-600' : 'text-red-600'}>
                    {stats.isManagerReady ? '就绪' : '未就绪'}
                  </span>
                </div>
                <div>
                  <span className="text-gray-600">最后更新: </span>
                  <span>{stats.lastEncryptTime}</span>
                </div>
              </div>
            </div>
          )}

          {/* 调试模式 */}
          <div className="border rounded-lg p-3">
            <div className="flex items-center justify-between mb-2">
              <span className="font-medium">调试模式</span>
              <div className={`flex items-center gap-1 px-2 py-1 rounded text-sm ${
                debugMode ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-700'
              }`}>
                {debugMode ? '已启用' : '已禁用'}
              </div>
            </div>
            <button
              onClick={() => setDebugMode(!debugMode)}
              className={`w-full py-2 px-4 rounded font-medium transition-colors ${
                debugMode
                  ? 'bg-red-500 hover:bg-red-600 text-white'
                  : 'bg-blue-500 hover:bg-blue-600 text-white'
              }`}
            >
              {debugMode ? '禁用调试' : '启用调试'}
            </button>
          </div>

          {/* 操作按钮 */}
          <div className="border rounded-lg p-3">
            <span className="font-medium">操作</span>
            <div className="mt-2">
              <button
                onClick={clearCache}
                className="w-full py-2 px-4 bg-red-500 hover:bg-red-600 text-white rounded font-medium flex items-center justify-center gap-2"
              >
                <Trash2 className="w-4 h-4" />
                清除缓存
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EncryptionControlPanel;
