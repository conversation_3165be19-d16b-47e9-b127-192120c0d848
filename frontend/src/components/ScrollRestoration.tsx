import React, { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { useScrollReset } from '../hooks/useScrollReset';

interface ScrollRestorationProps {
  /**
   * 子组件
   */
  children?: React.ReactNode;
  
  /**
   * 是否启用自动滚动重置
   * @default true
   */
  enabled?: boolean;
  
  /**
   * 需要保持滚动位置的路径
   * 这些路径在跳转时不会重置滚动位置
   */
  preserveScrollPaths?: string[];
  
  /**
   * 滚动重置延迟（毫秒）
   * @default 0
   */
  delay?: number;
  
  /**
   * 滚动行为
   * @default 'auto'
   */
  behavior?: ScrollBehavior;
  
  /**
   * 是否只在路径名变化时重置滚动
   * @default false
   */
  onlyOnPathnameChange?: boolean;
  
  /**
   * 自定义滚动重置逻辑
   */
  onScrollReset?: (pathname: string) => void;
}

/**
 * 滚动恢复组件
 * 自动处理路由跳转时的滚动位置重置
 */
const ScrollRestoration: React.FC<ScrollRestorationProps> = ({
  children,
  enabled = true,
  preserveScrollPaths = [],
  delay = 0,
  behavior = 'auto',
  onlyOnPathnameChange = false,
  onScrollReset
}) => {
  const location = useLocation();

  // 使用滚动重置Hook
  const { resetScroll } = useScrollReset({
    enabled,
    preserveScrollPaths,
    delay,
    behavior,
    onlyOnPathnameChange
  });

  // 监听路由变化，执行自定义滚动重置逻辑
  useEffect(() => {
    if (enabled && onScrollReset) {
      onScrollReset(location.pathname);
    }
  }, [location.pathname, enabled, onScrollReset]);

  // 如果有子组件，渲染子组件
  if (children) {
    return <>{children}</>;
  }

  // 否则返回null（仅作为功能组件使用）
  return null;
};

/**
 * 高阶组件：为组件添加滚动重置功能
 */
export const withScrollReset = <P extends object>(
  Component: React.ComponentType<P>,
  options: Omit<ScrollRestorationProps, 'children'> = {}
) => {
  const WrappedComponent: React.FC<P> = (props) => {
    return (
      <ScrollRestoration {...options}>
        <Component {...props} />
      </ScrollRestoration>
    );
  };

  WrappedComponent.displayName = `withScrollReset(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
};

/**
 * 滚动到顶部按钮组件
 */
export const ScrollToTopButton: React.FC<{
  threshold?: number;
  className?: string;
  style?: React.CSSProperties;
  children?: React.ReactNode;
}> = ({
  threshold = 300,
  className = '',
  style = {},
  children
}) => {
  const [isVisible, setIsVisible] = React.useState(false);

  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      setIsVisible(scrollTop > threshold);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, [threshold]);

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  if (!isVisible) return null;

  return (
    <button
      onClick={scrollToTop}
      className={`fixed bottom-20 right-4 z-50 bg-pink-500 text-white p-3 rounded-full shadow-lg hover:bg-pink-600 transition-all duration-300 active:scale-95 ${className}`}
      style={style}
      aria-label="回到顶部"
    >
      {children || (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 10l7-7m0 0l7 7m-7-7v18" />
        </svg>
      )}
    </button>
  );
};

/**
 * 滚动进度条组件
 */
export const ScrollProgressBar: React.FC<{
  className?: string;
  style?: React.CSSProperties;
  color?: string;
  height?: number;
}> = ({
  className = '',
  style = {},
  color = '#ec4899',
  height = 3
}) => {
  const [progress, setProgress] = React.useState(0);

  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      const scrollHeight = document.documentElement.scrollHeight - window.innerHeight;
      const scrollProgress = scrollHeight > 0 ? (scrollTop / scrollHeight) * 100 : 0;
      setProgress(Math.min(100, Math.max(0, scrollProgress)));
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    handleScroll(); // 初始化

    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <div
      className={`fixed top-0 left-0 z-50 transition-all duration-150 ${className}`}
      style={{
        width: `${progress}%`,
        height: `${height}px`,
        backgroundColor: color,
        ...style
      }}
    />
  );
};

/**
 * 智能滚动容器组件
 * 为容器内的滚动提供更好的控制
 */
export const ScrollContainer: React.FC<{
  children: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
  resetOnRouteChange?: boolean;
  rememberPosition?: boolean;
  positionKey?: string;
}> = ({
  children,
  className = '',
  style = {},
  resetOnRouteChange = true,
  rememberPosition = false,
  positionKey
}) => {
  const location = useLocation();
  const containerRef = React.useRef<HTMLDivElement>(null);
  const savedPositions = React.useRef<Map<string, number>>(new Map());

  const key = positionKey || location.pathname;

  // 保存滚动位置
  const savePosition = React.useCallback(() => {
    if (containerRef.current && rememberPosition) {
      const scrollTop = containerRef.current.scrollTop;
      savedPositions.current.set(key, scrollTop);
    }
  }, [key, rememberPosition]);

  // 恢复滚动位置
  const restorePosition = React.useCallback(() => {
    if (containerRef.current && rememberPosition) {
      const savedPosition = savedPositions.current.get(key);
      if (savedPosition !== undefined) {
        containerRef.current.scrollTop = savedPosition;
      }
    }
  }, [key, rememberPosition]);

  // 重置滚动位置
  const resetPosition = React.useCallback(() => {
    if (containerRef.current) {
      containerRef.current.scrollTop = 0;
    }
  }, []);

  // 路由变化时的处理
  useEffect(() => {
    if (resetOnRouteChange && !rememberPosition) {
      resetPosition();
    } else if (rememberPosition) {
      restorePosition();
    }
  }, [location.pathname, resetOnRouteChange, rememberPosition, resetPosition, restorePosition]);

  // 组件卸载时保存位置
  useEffect(() => {
    return () => {
      if (rememberPosition) {
        savePosition();
      }
    };
  }, [savePosition, rememberPosition]);

  return (
    <div
      ref={containerRef}
      className={`overflow-auto ${className}`}
      style={style}
      data-scroll-reset={resetOnRouteChange ? 'true' : undefined}
      onScroll={rememberPosition ? savePosition : undefined}
    >
      {children}
    </div>
  );
};

export default ScrollRestoration;
