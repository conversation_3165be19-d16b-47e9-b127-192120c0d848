import React, { useState } from 'react';
import { RefreshCw, AlertCircle } from 'lucide-react';

interface DecryptionErrorInfo {
  success: false;
  message: string;
  code: string;
  details: string;
}

interface DecryptionErrorDialogProps {
  isOpen: boolean;
  errorInfo: DecryptionErrorInfo | null;
  onClearDataAndRefresh: () => Promise<void>;
}

const DecryptionErrorDialog: React.FC<DecryptionErrorDialogProps> = ({
  isOpen,
  errorInfo,
  onClearDataAndRefresh
}) => {
  const [isClearing, setIsClearing] = useState(false);

  if (!isOpen || !errorInfo) return null;

  const handleClearData = async () => {
    setIsClearing(true);
    try {
      await onClearDataAndRefresh();
    } catch (error) {
      console.error('重启应用失败:', error);
      setIsClearing(false);
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-sm w-full">
        {/* 头部 */}
        <div className="p-6 text-center">
          <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <AlertCircle className="w-8 h-8 text-orange-600" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">应用需要重启</h3>
          <p className="text-sm text-gray-600 mb-6">
            检测到数据异常，需要重启应用以恢复正常使用
          </p>
        </div>

        {/* 安全提示 */}
        <div className="px-6 pb-4">
          <div className="bg-green-50 border border-green-200 rounded-lg p-3">
            <p className="text-sm text-green-700 text-center">
              您的账号数据（金币、VIP、收藏等）都安全保存在服务器上，不会丢失
            </p>
          </div>
        </div>

        {/* 底部按钮 */}
        <div className="p-6 border-t border-gray-200">
          <button
            onClick={handleClearData}
            disabled={isClearing}
            className="w-full px-4 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
          >
            {isClearing ? (
              <>
                <RefreshCw className="w-5 h-5 mr-2 animate-spin" />
                正在重启...
              </>
            ) : (
              <>
                <RefreshCw className="w-5 h-5 mr-2" />
                重启应用
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default DecryptionErrorDialog;
