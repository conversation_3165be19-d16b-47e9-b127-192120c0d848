import React, { useRef, useEffect, useState, useCallback } from 'react';
import Hls from 'hls.js';
import { watchHistoryService } from '../services/watchHistoryService';
import { trackVideoEvent } from '../utils/analytics';
import { processVideoUrl, processCoverUrl } from '../utils/videoUrl';
import { getRecommendedFullscreenMethod } from '../utils/deviceDetection';
import './VideoPlayer.css';

interface HLSPlayerProps {
  video: {
    id: number;
    title: string;
    videoUrl: string;
    price?: number;
    cover?: string;
    duration?: number;
    category?: string;
  };
  isActive?: boolean;
  hasAccess?: boolean;
  isPaidVideo?: boolean;
  previewDuration?: number;
  onPreviewEnd?: () => void;
  onVideoError?: (error: string) => void;
  className?: string;
  autoplay?: boolean;
  loop?: boolean;
  shouldPause?: boolean; // 新增：外部控制暂停
}

const HLSPlayer: React.FC<HLSPlayerProps> = ({
  video,
  isActive = true,
  hasAccess = true,
  isPaidVideo = false,
  previewDuration = 60,
  onPreviewEnd,
  onVideoError,
  className = '',
  autoplay = false,
  loop = false,
  shouldPause = false
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const hlsRef = useRef<Hls | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);

  const [showControls, setShowControls] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  // 移除音量状态，默认最大音量，让用户通过设备调节
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isSeeking, setIsSeeking] = useState(false);
  const controlsTimeoutRef = useRef<number | null>(null);
  const clickTimeoutRef = useRef<number | null>(null);
  const mouseMoveTimeoutRef = useRef<number | null>(null);
  const lastTouchTimeRef = useRef<number>(0);
  const touchCountRef = useRef<number>(0);
  const hasRecordedHistory = useRef<boolean>(false); // 是否已记录观看历史
  const lastProgressUpdate = useRef<number>(0); // 上次进度更新时间

  // 初始化HLS播放器
  const initializeHLS = useCallback(() => {
    if (!videoRef.current || !video.videoUrl) return;

    const videoElement = videoRef.current;

    // 处理视频URL（支持完整URL和绝对路径）
    const processedVideoUrl = processVideoUrl(video.videoUrl);

    // 清理现有的HLS实例
    if (hlsRef.current) {
      hlsRef.current.destroy();
      hlsRef.current = null;
    }

    // 检查是否为HLS流
    if (processedVideoUrl.includes('.m3u8')) {
      if (Hls.isSupported()) {
        const hls = new Hls({
          // 🔑 核心配置：解决密钥重复请求问题
          enableWorker: true,
          lowLatencyMode: false,
          backBufferLength: 90,

          // 🔐 密钥加载配置 - 使用默认加载器
          // 密钥请求监控通过事件处理

          // 🎯 优化配置
          maxBufferLength: 30,
          maxMaxBufferLength: 600,
          maxBufferSize: 60 * 1000 * 1000,
          maxBufferHole: 0.5,

          // 🔄 重试配置
          manifestLoadingTimeOut: 10000,
          manifestLoadingMaxRetry: 4,
          manifestLoadingRetryDelay: 500,

          // 🔐 片段加载配置
          fragLoadingTimeOut: 20000,
          fragLoadingMaxRetry: 6,
          fragLoadingRetryDelay: 500,



          // 🎨 其他配置
          startLevel: -1,
          capLevelToPlayerSize: true,
          debug: false
        });

        hlsRef.current = hls;

        // 🔍 事件监听
        hls.on(Hls.Events.MANIFEST_PARSED, () => {
          setIsLoading(false);
          setError(null);

          if (autoplay && isActive) {
            videoElement.play().catch(() => {
              // 自动播放失败，忽略错误
            });
          }
        });

        hls.on(Hls.Events.ERROR, (_, data) => {

          if (data.fatal) {
            switch (data.type) {
              case Hls.ErrorTypes.NETWORK_ERROR:
                setError('网络错误，请检查网络连接');
                hls.startLoad();
                break;
              case Hls.ErrorTypes.MEDIA_ERROR:
                setError('媒体错误，尝试恢复中...');
                hls.recoverMediaError();
                break;
              default:
                setError('播放失败，请重试');
                hls.destroy();
                break;
            }

            if (onVideoError) {
              onVideoError(data.reason || '播放失败');
            }
          }
        });



        // 加载视频
        hls.loadSource(processedVideoUrl);
        hls.attachMedia(videoElement);

      } else if (videoElement.canPlayType('application/vnd.apple.mpegurl')) {
        // Safari原生支持
        console.log('🍎 使用Safari原生HLS支持');
        videoElement.src = processedVideoUrl;
        setIsLoading(false);
      } else {
        setError('浏览器不支持HLS播放');
      }
    } else {
      // 普通MP4视频
      console.log('🎬 加载MP4视频');
      videoElement.src = processedVideoUrl;
      setIsLoading(false);
    }
  }, [video.videoUrl, autoplay, isActive]);

  // 初始化播放器
  useEffect(() => {
    initializeHLS();

    return () => {
      if (hlsRef.current) {
        hlsRef.current.destroy();
        hlsRef.current = null;
      }

      // 清理定时器
      if (controlsTimeoutRef.current) {
        clearTimeout(controlsTimeoutRef.current);
      }
      if (clickTimeoutRef.current) {
        clearTimeout(clickTimeoutRef.current);
      }
      if (mouseMoveTimeoutRef.current) {
        clearTimeout(mouseMoveTimeoutRef.current);
      }
    };
  }, [initializeHLS]);

  // 视频事件处理
  useEffect(() => {
    const videoElement = videoRef.current;
    if (!videoElement) return;

    const handleLoadedMetadata = () => {
      setDuration(videoElement.duration);
    };

    const handleTimeUpdate = () => {
      const currentTime = videoElement.currentTime;
      setCurrentTime(currentTime);

      // 记录观看历史 - 只在视频开始播放时记录一次
      if (!hasRecordedHistory.current && currentTime > 0) {
        hasRecordedHistory.current = true;
        watchHistoryService.addWatchHistory({
          id: video.id,
          title: video.title,
          cover: video.cover || '',
          videoUrl: video.videoUrl,
          duration: video.duration || videoElement.duration || 0,
          category: video.category || '未分类',
          price: video.price || 0
        }, currentTime).catch(console.error);
      }

      // 定期更新观看进度（每5秒更新一次）
      const now = Date.now();
      if (now - lastProgressUpdate.current > 5000) { // 5秒间隔
        lastProgressUpdate.current = now;
        watchHistoryService.updateWatchProgress(video.id, currentTime).catch(console.error);
      }

      // 预览时间限制
      if (isPaidVideo && !hasAccess && previewDuration > 0) {
        if (currentTime >= previewDuration) {
          videoElement.pause();
          if (onPreviewEnd) {
            onPreviewEnd();
          }
        }
      }
    };

    const handlePlay = () => {
      setIsPlaying(true);
      // 跟踪视频播放事件
      trackVideoEvent('play', video.id, video.title, videoElement.currentTime, videoElement.duration);
    };

    const handlePause = () => {
      setIsPlaying(false);
      // 跟踪视频暂停事件
      trackVideoEvent('pause', video.id, video.title, videoElement.currentTime, videoElement.duration);
    };

    const handleEnded = () => {
      setIsPlaying(false);
      // 跟踪视频完成事件
      trackVideoEvent('complete', video.id, video.title, videoElement.currentTime, videoElement.duration);

      if (loop) {
        videoElement.currentTime = 0;
        videoElement.play().catch(console.error);
      }
    };

    // 移除音量变化处理，默认最大音量

    const handleSeeking = () => {
      setIsSeeking(true);
    };

    const handleSeeked = () => {
      setIsSeeking(false);
    };

    videoElement.addEventListener('loadedmetadata', handleLoadedMetadata);
    videoElement.addEventListener('timeupdate', handleTimeUpdate);
    videoElement.addEventListener('play', handlePlay);
    videoElement.addEventListener('pause', handlePause);
    videoElement.addEventListener('ended', handleEnded);
    videoElement.addEventListener('seeking', handleSeeking);
    videoElement.addEventListener('seeked', handleSeeked);

    return () => {
      videoElement.removeEventListener('loadedmetadata', handleLoadedMetadata);
      videoElement.removeEventListener('timeupdate', handleTimeUpdate);
      videoElement.removeEventListener('play', handlePlay);
      videoElement.removeEventListener('pause', handlePause);
      videoElement.removeEventListener('ended', handleEnded);
      videoElement.removeEventListener('seeking', handleSeeking);
      videoElement.removeEventListener('seeked', handleSeeked);
    };
  }, [isPaidVideo, hasAccess, previewDuration, onPreviewEnd, loop, video]);

  // 控制播放状态
  useEffect(() => {
    const videoElement = videoRef.current;
    if (!videoElement || isLoading) return;

    if (isActive && !isPlaying && !videoElement.paused) {
      // 应该播放但当前暂停
      videoElement.play().catch(console.error);
    } else if (!isActive && isPlaying) {
      // 应该暂停但当前播放
      videoElement.pause();
    }
  }, [isActive, isPlaying, isLoading]);

  // 外部控制暂停（购买弹框显示时）
  useEffect(() => {
    const videoElement = videoRef.current;
    if (!videoElement || isLoading) return;

    if (shouldPause && !videoElement.paused) {
      // 需要暂停且当前正在播放
      videoElement.pause();
      console.log('🛑 视频因购买弹框显示而暂停');
    }
  }, [shouldPause, isLoading]);

  // 显示/隐藏控制条
  const showControlsTemporarily = (delay: number = 2000) => {
    setShowControls(true);

    // 每次调用都重新计算倒计时
    if (controlsTimeoutRef.current) {
      clearTimeout(controlsTimeoutRef.current);
    }

    // 如果延迟为0，则不自动隐藏
    if (delay > 0) {
      controlsTimeoutRef.current = setTimeout(() => {
        setShowControls(false);
      }, delay);
    }
  };

  // 优化的鼠标移动处理 - 防止频繁重置倒计时
  const handleMouseMove = (e: React.MouseEvent) => {
    // 检查是否在底部进度条区域（底部20px区域）
    const rect = e.currentTarget.getBoundingClientRect();
    const mouseY = e.clientY - rect.top;
    const isInBottomArea = mouseY > rect.height - 20;

    // 如果在底部区域且控件已隐藏，不立即显示控件
    if (isInBottomArea && !showControls) {
      return; // 让底部进度条可以正常交互
    }

    // 显示控件
    setShowControls(true);

    // 清除之前的鼠标移动延迟
    if (mouseMoveTimeoutRef.current) {
      clearTimeout(mouseMoveTimeoutRef.current);
    }

    // 延迟500ms后开始2秒倒计时（鼠标停止移动后）
    mouseMoveTimeoutRef.current = setTimeout(() => {
      showControlsTemporarily(2000);
    }, 500);
  };

  // 点击处理 - 延迟执行以区分单击和双击（仅桌面端）
  const handleVideoClick = () => {
    // 移动端通过触摸事件处理，避免重复执行
    if ('ontouchstart' in window) {
      return;
    }

    // 清除之前的单击延迟
    if (clickTimeoutRef.current) {
      clearTimeout(clickTimeoutRef.current);
    }

    // 延迟执行单击操作，如果在延迟期间发生双击，则取消单击
    clickTimeoutRef.current = setTimeout(() => {
      const videoElement = videoRef.current;
      if (!videoElement) return;

      // 单击：只播放，不暂停
      if (videoElement.paused) {
        videoElement.play().catch(console.error);
      }
      // 如果正在播放，单击不做任何操作

      showControlsTemporarily();
    }, 200); // 200ms延迟
  };

  // 双击暂停功能 - 立即执行
  const handleVideoDoubleClick = () => {
    // 清除单击延迟，防止单击操作执行
    if (clickTimeoutRef.current) {
      clearTimeout(clickTimeoutRef.current);
      clickTimeoutRef.current = null;
    }

    const videoElement = videoRef.current;
    if (!videoElement) return;

    // 双击：暂停播放
    if (!videoElement.paused) {
      videoElement.pause();
    }

    showControlsTemporarily();
  };

  // 控件按钮点击处理 - 传统的播放/暂停切换
  const handleControlButtonClick = () => {
    const videoElement = videoRef.current;
    if (!videoElement) return;

    // 控件按钮：播放/暂停切换
    if (videoElement.paused) {
      videoElement.play().catch(console.error);
    } else {
      videoElement.pause();
    }

    showControlsTemporarily();
  };

  // 移动端触摸事件处理
  const handleTouchStart = (e: React.TouchEvent) => {
    e.preventDefault(); // 防止默认行为

    const currentTime = Date.now();
    const timeDiff = currentTime - lastTouchTimeRef.current;

    // 如果两次触摸间隔小于300ms，认为是双击
    if (timeDiff < 300 && timeDiff > 0) {
      touchCountRef.current += 1;

      if (touchCountRef.current === 2) {
        // 双击处理 - 暂停播放
        if (clickTimeoutRef.current) {
          clearTimeout(clickTimeoutRef.current);
        }

        const videoElement = videoRef.current;
        if (videoElement && !videoElement.paused) {
          videoElement.pause();
        }

        touchCountRef.current = 0;
        showControlsTemporarily();
        return;
      }
    } else {
      // 重置计数
      touchCountRef.current = 1;
    }

    lastTouchTimeRef.current = currentTime;

    // 延迟执行单击，如果在延迟期间发生双击则取消
    if (clickTimeoutRef.current) {
      clearTimeout(clickTimeoutRef.current);
    }

    clickTimeoutRef.current = setTimeout(() => {
      if (touchCountRef.current === 1) {
        // 单击处理 - 只播放，不暂停
        const videoElement = videoRef.current;
        if (videoElement && videoElement.paused) {
          videoElement.play().catch(console.error);
        }
        // 如果正在播放，单击不做任何操作
      }
      touchCountRef.current = 0;
    }, 300);

    // 显示控件
    showControlsTemporarily();
  };

  // 进度条拖拽处理
  const handleProgressMouseDown = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!videoRef.current) return;

    e.stopPropagation();
    e.preventDefault();
    setIsDragging(true);
    setIsSeeking(true);
    setShowControls(true); // 拖动时显示控件
    if (controlsTimeoutRef.current) {
      clearTimeout(controlsTimeoutRef.current); // 清除自动隐藏
    }
    updateProgressFromEvent(e);
  };

  // 触摸事件处理
  const handleProgressTouchStart = (e: React.TouchEvent<HTMLDivElement>) => {
    if (!videoRef.current) return;

    e.stopPropagation();
    e.preventDefault();
    setIsDragging(true);
    setIsSeeking(true);
    setShowControls(true); // 拖动时显示控件
    if (controlsTimeoutRef.current) {
      clearTimeout(controlsTimeoutRef.current); // 清除自动隐藏
    }

    // 将触摸事件转换为鼠标事件格式
    const touch = e.touches[0];
    const mouseEvent = {
      currentTarget: e.currentTarget,
      clientX: touch.clientX,
      clientY: touch.clientY
    } as any;
    updateProgressFromEvent(mouseEvent);
  };

  const updateProgressFromEvent = (e: React.MouseEvent<HTMLDivElement> | MouseEvent) => {
    if (!videoRef.current || duration === 0) return;

    // 获取进度条元素
    const progressBar = e.currentTarget as HTMLDivElement ||
                       document.querySelector('.video-progress-bar') as HTMLDivElement;
    if (!progressBar) return;

    const rect = progressBar.getBoundingClientRect();
    const percent = Math.max(0, Math.min(1, (e.clientX - rect.left) / rect.width));
    let newTime = percent * duration;

    // 预览模式下限制拖动范围
    if (isPaidVideo && !hasAccess) {
      newTime = Math.min(newTime, previewDuration);
    }

    setCurrentTime(newTime);
    videoRef.current.currentTime = newTime;
  };

  // 设置默认音量为最大
  useEffect(() => {
    const videoElement = videoRef.current;
    if (videoElement) {
      videoElement.volume = 1.0; // 设置音量为最大
      videoElement.muted = false; // 确保不静音
    }
  }, []);

  // 全屏控制 - 根据设备类型选择最佳全屏方法
  const toggleFullscreen = async () => {
    const videoElement = videoRef.current;
    const container = videoElement?.parentElement;

    if (!videoElement || !container) return;

    const fullscreenMethod = getRecommendedFullscreenMethod();

    try {
      if (!isFullscreen) {
        // 进入全屏
        if (fullscreenMethod === 'video') {
          // 优先使用video元素全屏（移动设备）
          if (videoElement.requestFullscreen) {
            await videoElement.requestFullscreen();
          } else if ((videoElement as any).webkitRequestFullscreen) {
            await (videoElement as any).webkitRequestFullscreen();
          } else if ((videoElement as any).webkitEnterFullscreen) {
            // iOS Safari 特殊处理
            (videoElement as any).webkitEnterFullscreen();
          } else if ((videoElement as any).mozRequestFullScreen) {
            await (videoElement as any).mozRequestFullScreen();
          } else if ((videoElement as any).msRequestFullscreen) {
            await (videoElement as any).msRequestFullscreen();
          }
        } else if (fullscreenMethod === 'container') {
          // 使用容器全屏（桌面设备）
          if (container.requestFullscreen) {
            await container.requestFullscreen();
          } else if ((container as any).webkitRequestFullscreen) {
            await (container as any).webkitRequestFullscreen();
          } else if ((container as any).mozRequestFullScreen) {
            await (container as any).mozRequestFullScreen();
          } else if ((container as any).msRequestFullscreen) {
            await (container as any).msRequestFullscreen();
          }
        }
      } else {
        // 退出全屏
        if (document.exitFullscreen) {
          await document.exitFullscreen();
        } else if ((document as any).webkitExitFullscreen) {
          await (document as any).webkitExitFullscreen();
        } else if ((document as any).webkitCancelFullScreen) {
          await (document as any).webkitCancelFullScreen();
        } else if ((document as any).mozCancelFullScreen) {
          await (document as any).mozCancelFullScreen();
        } else if ((document as any).msExitFullscreen) {
          await (document as any).msExitFullscreen();
        }
      }
    } catch (error) {
      console.warn('全屏操作失败:', error);
      // 在某些浏览器中，全屏API可能会失败，但不影响正常使用
    }
  };

  // 监听全屏状态变化
  useEffect(() => {
    const handleFullscreenChange = () => {
      const videoElement = videoRef.current;

      // 检查多种全屏状态
      const isCurrentlyFullscreen = !!(
        document.fullscreenElement ||
        (document as any).webkitFullscreenElement ||
        (document as any).webkitCurrentFullScreenElement ||
        (document as any).mozFullScreenElement ||
        (document as any).msFullscreenElement ||
        // 检查video元素是否在全屏状态
        (videoElement && (
          videoElement === document.fullscreenElement ||
          videoElement === (document as any).webkitFullscreenElement ||
          videoElement === (document as any).webkitCurrentFullScreenElement ||
          videoElement === (document as any).mozFullScreenElement ||
          videoElement === (document as any).msFullscreenElement
        ))
      );

      setIsFullscreen(isCurrentlyFullscreen);

      // 全屏时显示控件
      if (isCurrentlyFullscreen) {
        showControlsTemporarily();
      }
    };

    // 监听各种全屏事件
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
    document.addEventListener('webkitbeginfullscreen', handleFullscreenChange);
    document.addEventListener('webkitendfullscreen', handleFullscreenChange);
    document.addEventListener('mozfullscreenchange', handleFullscreenChange);
    document.addEventListener('MSFullscreenChange', handleFullscreenChange);

    // 初始检查
    handleFullscreenChange();

    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
      document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
      document.removeEventListener('webkitbeginfullscreen', handleFullscreenChange);
      document.removeEventListener('webkitendfullscreen', handleFullscreenChange);
      document.removeEventListener('mozfullscreenchange', handleFullscreenChange);
      document.removeEventListener('MSFullscreenChange', handleFullscreenChange);
    };
  }, []);

  // 全局鼠标事件监听（用于拖动进度条）
  useEffect(() => {
    const handleGlobalMouseMove = (e: MouseEvent) => {
      if (isDragging && videoRef.current && duration > 0) {
        e.preventDefault();
        e.stopPropagation();

        const progressBar = document.querySelector('.video-progress-bar') as HTMLDivElement;
        if (progressBar) {
          const rect = progressBar.getBoundingClientRect();
          const percent = Math.max(0, Math.min(1, (e.clientX - rect.left) / rect.width));
          let newTime = percent * duration;

          // 预览模式下限制拖动范围
          if (isPaidVideo && !hasAccess) {
            newTime = Math.min(newTime, previewDuration);
          }

          setCurrentTime(newTime);
          videoRef.current.currentTime = newTime;
        }
      }
    };

    const handleGlobalMouseUp = (e: MouseEvent) => {
      if (isDragging) {
        e.preventDefault();
        e.stopPropagation();
        setIsDragging(false);
        showControlsTemporarily(); // 恢复2秒自动隐藏
      }
    };

    // 触摸事件处理
    const handleGlobalTouchMove = (e: TouchEvent) => {
      if (isDragging && videoRef.current && duration > 0 && e.touches.length > 0) {
        e.preventDefault();
        e.stopPropagation();

        const progressBar = document.querySelector('.video-progress-bar') as HTMLDivElement;
        if (progressBar) {
          const touch = e.touches[0];
          const rect = progressBar.getBoundingClientRect();
          const percent = Math.max(0, Math.min(1, (touch.clientX - rect.left) / rect.width));
          let newTime = percent * duration;

          // 预览模式下限制拖动范围
          if (isPaidVideo && !hasAccess) {
            newTime = Math.min(newTime, previewDuration);
          }

          setCurrentTime(newTime);
          videoRef.current.currentTime = newTime;
        }
      }
    };

    const handleGlobalTouchEnd = (e: TouchEvent) => {
      if (isDragging) {
        e.preventDefault();
        e.stopPropagation();
        setIsDragging(false);
        showControlsTemporarily(); // 恢复2秒自动隐藏
      }
    };

    // 始终监听全局事件，而不是只在isDragging时
    document.addEventListener('mousemove', handleGlobalMouseMove, { passive: false });
    document.addEventListener('mouseup', handleGlobalMouseUp, { passive: false });
    document.addEventListener('touchmove', handleGlobalTouchMove, { passive: false });
    document.addEventListener('touchend', handleGlobalTouchEnd, { passive: false });

    return () => {
      document.removeEventListener('mousemove', handleGlobalMouseMove);
      document.removeEventListener('mouseup', handleGlobalMouseUp);
      document.removeEventListener('touchmove', handleGlobalTouchMove);
      document.removeEventListener('touchend', handleGlobalTouchEnd);
    };
  }, [isDragging, duration, isPaidVideo, hasAccess, previewDuration]);

  // 格式化时间
  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  if (error) {
    return (
      <div className={`flex items-center justify-center bg-gray-900 text-white ${className}`}>
        <div className="text-center p-4">
          <div className="text-red-500 text-2xl mb-2">⚠️</div>
          <div className="text-sm">{error}</div>
          <button
            onClick={initializeHLS}
            className="mt-2 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
          >
            重试
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`video-container relative bg-black ${className}`}>
      {/* Loading状态 - 简洁的loading圈 */}
      {(isLoading || isSeeking) && (
        <div className="absolute inset-0 flex items-center justify-center z-25 pointer-events-none">
          <div className="relative w-8 h-8">
            <div className="absolute inset-0 border-2 border-white border-opacity-30 rounded-full"></div>
            <div className="absolute inset-0 border-2 border-transparent border-t-pink-500 rounded-full animate-spin"></div>
          </div>
        </div>
      )}

      <video
        ref={videoRef}
        className="w-full h-full"
        controls={false}
        playsInline
        webkit-playsinline="true"
        x-webkit-airplay="allow"
        preload="metadata"
        poster={processCoverUrl(video.cover || '')}
        disablePictureInPicture
        controlsList="nodownload nofullscreen noremoteplayback"
        style={{
          objectFit: 'contain',
          backgroundColor: '#000'
        }}
      />

      {/* 视频交互覆盖层 - 处理点击和双击事件 */}
      <div
        className="absolute inset-0 cursor-pointer z-15"
        onClick={handleVideoClick}
        onDoubleClick={handleVideoDoubleClick}
        onMouseMove={handleMouseMove}
        onTouchStart={handleTouchStart}
      />

      {/* 播放按钮覆盖层（暂停时显示） */}
      {!isPlaying && !isLoading && !error && (
        <div className="absolute inset-0 flex items-center justify-center pointer-events-none z-20">
          <div className="w-16 h-16 bg-black bg-opacity-50 rounded-full flex items-center justify-center text-white text-3xl">
            <svg className="w-8 h-8 ml-1" fill="currentColor" viewBox="0 0 24 24">
              <path d="M8 5v14l11-7z" />
            </svg>
          </div>
        </div>
      )}

      {/* 控制栏 */}
      {showControls && !error && (
        <div className="video-controls absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black via-black/50 to-transparent px-3 py-2 z-30">
          {/* 进度条 */}
          <div className="mb-2">
            <div
              className="video-progress-bar h-1 bg-white bg-opacity-30 rounded-full cursor-pointer relative"
              onMouseDown={handleProgressMouseDown}
              onTouchStart={handleProgressTouchStart}
            >
              {/* 预览限制指示线（预览模式下显示） */}
              {isPaidVideo && !hasAccess && duration > 0 && (
                <div
                  className="absolute top-0 bottom-0 w-0.5 bg-red-500"
                  style={{
                    left: `${(previewDuration / duration) * 100}%`,
                    marginLeft: '-1px'
                  }}
                />
              )}

              {/* 已播放进度 */}
              <div
                className="h-full bg-pink-500 rounded-full transition-all duration-100"
                style={{
                  width: `${duration > 0 ? (currentTime / duration) * 100 : 0}%`
                }}
              />

              {/* 拖动手柄 */}
              <div
                className={`absolute top-1/2 transform -translate-y-1/2 w-3 h-3 bg-pink-500 rounded-full transition-all duration-200 ${
                  isDragging ? 'scale-125' : 'scale-100'
                }`}
                style={{
                  left: `${duration > 0 ? (currentTime / duration) * 100 : 0}%`,
                  marginLeft: '-6px'
                }}
              />
            </div>
          </div>

          {/* 控制按钮 */}
          <div className="flex items-center justify-between text-white">
            <div className="flex items-center space-x-2">
              {/* 播放/暂停按钮 */}
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  handleControlButtonClick();
                }}
                className="w-8 h-8 flex items-center justify-center hover:bg-white hover:bg-opacity-20 rounded-full transition-colors"
              >
                {isPlaying ? (
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M6 4h4v16H6V4zm8 0h4v16h-4V4z" />
                  </svg>
                ) : (
                  <svg className="w-4 h-4 ml-0.5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M8 5v14l11-7z" />
                  </svg>
                )}
              </button>

              {/* 时间显示 */}
              <div className="text-xs font-mono">
                {formatTime(currentTime)} / {formatTime(duration)}
              </div>
            </div>

            <div className="flex items-center space-x-2">
              {/* 移除音量控制，默认最大音量，让用户通过设备调节 */}

              {/* 全屏按钮 */}
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  toggleFullscreen();
                  showControlsTemporarily();
                }}
                className="w-6 h-6 flex items-center justify-center hover:bg-white hover:bg-opacity-20 rounded-full transition-colors"
              >
                {isFullscreen ? (
                  <svg className="w-3.5 h-3.5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M5 16h3v3h2v-5H5v2zm3-8H5v2h5V5H8v3zm6 11h2v-3h3v-2h-5v5zm2-11V5h-2v5h5V8h-3z"/>
                  </svg>
                ) : (
                  <svg className="w-3.5 h-3.5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M7 14H5v5h5v-2H7v-3zm-2-4h2V7h3V5H5v5zm12 7h-3v2h5v-5h-2v3zM14 5v2h3v3h2V5h-5z"/>
                  </svg>
                )}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 简化进度条（控件隐藏时显示） */}
      {!showControls && !error && duration > 0 && (
        <div className="absolute bottom-0 left-0 right-0 z-20">
          <div
            className="video-progress-bar h-1 bg-white bg-opacity-30 cursor-pointer relative hover:h-2 transition-all duration-200"
            onMouseDown={(e) => {
              e.stopPropagation();
              e.preventDefault();
              setIsDragging(true);
              setIsSeeking(true);
              setShowControls(true); // 拖动时显示控件
              if (controlsTimeoutRef.current) {
                clearTimeout(controlsTimeoutRef.current); // 清除自动隐藏
              }
              if (mouseMoveTimeoutRef.current) {
                clearTimeout(mouseMoveTimeoutRef.current); // 清除鼠标移动延迟
              }
              updateProgressFromEvent(e);
            }}
            onTouchStart={(e) => {
              e.stopPropagation();
              e.preventDefault();
              setIsDragging(true);
              setIsSeeking(true);
              setShowControls(true); // 拖动时显示控件
              if (controlsTimeoutRef.current) {
                clearTimeout(controlsTimeoutRef.current); // 清除自动隐藏
              }
              if (mouseMoveTimeoutRef.current) {
                clearTimeout(mouseMoveTimeoutRef.current); // 清除鼠标移动延迟
              }

              // 将触摸事件转换为鼠标事件格式
              const touch = e.touches[0];
              const mouseEvent = {
                currentTarget: e.currentTarget,
                clientX: touch.clientX,
                clientY: touch.clientY
              } as any;
              updateProgressFromEvent(mouseEvent);
            }}
          >
            {/* 预览限制指示线（预览模式下显示） */}
            {isPaidVideo && !hasAccess && duration > 0 && (
              <div
                className="absolute top-0 bottom-0 w-0.5 bg-red-500"
                style={{
                  left: `${(previewDuration / duration) * 100}%`,
                  marginLeft: '-1px'
                }}
              />
            )}

            {/* 已播放进度 */}
            <div
              className="h-full bg-pink-500 transition-all duration-100"
              style={{
                width: `${duration > 0 ? (currentTime / duration) * 100 : 0}%`
              }}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default HLSPlayer;
