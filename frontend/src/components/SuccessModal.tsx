import React from 'react';
import { useNavigate } from 'react-router-dom';

interface SuccessModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  message: string;
  buttonText?: string;
  redirectPath?: string;
  showConfetti?: boolean;
}

const SuccessModal: React.FC<SuccessModalProps> = ({
  isOpen,
  onClose,
  title,
  message,
  buttonText = '确定',
  redirectPath,
  showConfetti = false
}) => {
  const navigate = useNavigate();

  const handleConfirm = () => {
    onClose();
    if (redirectPath) {
      navigate(redirectPath);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-sm w-full mx-4 overflow-hidden shadow-xl">
        {/* 头部 */}
        <div className="bg-green-50 px-4 py-6 text-center">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4 relative">
            {showConfetti && (
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="animate-bounce">🎉</div>
              </div>
            )}
            <svg className="w-8 h-8 text-green-600" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
            </svg>
          </div>
          <h3 className="text-xl font-bold text-gray-900 mb-2">{title}</h3>
          <p className="text-gray-600">{message}</p>
        </div>

        {/* 内容区域 - 可以添加额外信息 */}
        <div className="p-4">
          {showConfetti && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mb-4">
              <div className="flex items-center">
                <div className="text-yellow-600 mr-2">⭐</div>
                <div>
                  <div className="text-sm font-medium text-yellow-900">恭喜您！</div>
                  <div className="text-xs text-yellow-700">
                    您现在可以享受VIP专属特权了
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* VIP特权提示 */}
          {title.includes('VIP') && (
            <div className="space-y-2 mb-4">
              <div className="text-sm font-medium text-gray-900 mb-2">您的VIP特权：</div>
              <div className="space-y-1">
                <div className="flex items-center text-xs text-gray-700">
                  <svg className="w-3 h-3 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  免费观看所有VIP视频
                </div>
                <div className="flex items-center text-xs text-gray-700">
                  <svg className="w-3 h-3 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  高清画质无广告体验
                </div>
                <div className="flex items-center text-xs text-gray-700">
                  <svg className="w-3 h-3 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  专属会员标识
                </div>
              </div>
            </div>
          )}
        </div>

        {/* 底部按钮 */}
        <div className="bg-gray-50 px-4 py-3">
          <button
            onClick={handleConfirm}
            className="w-full py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium"
          >
            {buttonText}
          </button>
        </div>
      </div>
    </div>
  );
};

export default SuccessModal;
