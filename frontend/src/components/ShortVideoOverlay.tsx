import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuthStore } from '../store/authStore';
import { unifiedApiService } from '../services/unifiedApiService';
import type { Video } from '../types';

interface ShortVideoOverlayProps {
  video: Video;
  onFavorite?: (videoId: number, isFavorited: boolean) => void;
  onShare?: (video: Video) => void;
  onPreviewEnd?: () => void;
}

const ShortVideoOverlay: React.FC<ShortVideoOverlayProps> = ({
  video,
  onFavorite,
  onShare,
  onPreviewEnd
}) => {
  const navigate = useNavigate();
  const { user, isAuthenticated } = useAuthStore();
  const [isFavorited, setIsFavorited] = useState(video.isFavorited || false);
  const [loading, setLoading] = useState(false);
  const favoriteTimeoutRef = useRef<number | null>(null);

  // 监听video变化，更新收藏状态
  useEffect(() => {
    setIsFavorited(video.isFavorited || false);
  }, [video.id, video.isFavorited]);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (favoriteTimeoutRef.current) {
        clearTimeout(favoriteTimeoutRef.current);
      }
    };
  }, []);



  const handleFavorite = async () => {
    console.log('短视频收藏按钮被点击，当前状态:', isFavorited);

    // 防抖：清除之前的定时器
    if (favoriteTimeoutRef.current) {
      clearTimeout(favoriteTimeoutRef.current);
      setLoading(false); // 重置加载状态
    }

    // 立即更新UI状态，提供即时反馈
    const newFavoriteState = !isFavorited;
    setIsFavorited(newFavoriteState);

    // 立即通知父组件更新状态
    onFavorite?.(video.id, newFavoriteState);

    // 设置加载状态
    setLoading(true);

    // 防抖延迟执行API请求
    favoriteTimeoutRef.current = window.setTimeout(async () => {
      try {
        let result;
        if (newFavoriteState) {
          result = await unifiedApiService.addFavorite(video.id);
        } else {
          result = await unifiedApiService.removeFavorite(video.id);
        }
        // 确保UI状态与服务器返回的状态一致
        if (result.isFavorited !== newFavoriteState) {
          setIsFavorited(result.isFavorited);
          onFavorite?.(video.id, result.isFavorited);
        }
        console.log('收藏状态已同步到服务器');
      } catch (error) {
        console.error('Favorite error:', error);
        // 如果API请求失败，回滚UI状态
        setIsFavorited(!newFavoriteState);
        onFavorite?.(video.id, !newFavoriteState);
      } finally {
        setLoading(false);
        favoriteTimeoutRef.current = null;
      }
    }, 300); // 300ms 防抖延迟
  };

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: video.title,
        text: video.description,
        url: window.location.href
      });
    } else {
      // 复制链接到剪贴板
      navigator.clipboard.writeText(window.location.href);
      alert('链接已复制到剪贴板');
    }
    onShare?.(video);
  };



  const formatCount = (count: number) => {
    if (count >= 10000) {
      return `${(count / 10000).toFixed(1)}w`;
    }
    if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}k`;
    }
    return count.toString();
  };

  return (
    <div className="absolute inset-0 pointer-events-none">


      {/* 右侧操作栏 */}
      <div className="absolute right-3 bottom-32 flex flex-col items-center space-y-4 pointer-events-auto z-20">
        {/* 收藏按钮 */}
        <button
          onClick={handleFavorite}
          disabled={loading}
          className="flex flex-col items-center space-y-1 active:scale-95 transition-transform disabled:opacity-50"
        >
          <div className={`w-12 h-12 rounded-full flex items-center justify-center text-2xl ${
            isFavorited ? 'bg-yellow-500 text-white' : 'bg-black bg-opacity-50 text-white'
          }`}>
            {isFavorited ? '⭐' : '☆'}
          </div>
          <span className="text-white text-xs font-medium">{isFavorited ? '已收藏' : '收藏'}</span>
        </button>

        {/* 分享按钮 */}
        <button
          onClick={handleShare}
          className="flex flex-col items-center space-y-1 active:scale-95 transition-transform"
        >
          <div className="w-12 h-12 bg-black bg-opacity-50 rounded-full flex items-center justify-center text-white text-2xl">
            📤
          </div>
          <span className="text-white text-xs font-medium">分享</span>
        </button>


      </div>

      {/* 底部信息栏 */}
      <div className="absolute bottom-32 left-4 right-16 pointer-events-auto z-20">
        <div className="space-y-3">
          {/* 价格标识 */}
          <div className="mb-2">
            {video.price && video.price > 0 ? (
              video.hasPurchased ? (
                <span className="bg-gradient-to-r from-green-400 to-green-600 bg-opacity-90 text-white px-2 py-1 rounded-md text-xs font-bold shadow-lg inline-flex items-center">
                  <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"/>
                  </svg>
                  已购买
                </span>
              ) : user?.isVip ? (
                <span className="bg-gradient-to-r from-purple-500 to-pink-600 bg-opacity-90 text-white px-2 py-1 rounded-md text-xs font-bold shadow-lg inline-flex items-center">
                  <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                  </svg>
                  已解锁
                </span>
              ) : (
                <button
                  onClick={() => onPreviewEnd && onPreviewEnd()}
                  className="bg-gradient-to-r from-yellow-400 to-orange-500 text-white px-2 py-1 rounded-md text-xs font-bold shadow-lg inline-flex items-center hover:from-yellow-500 hover:to-orange-600 transition-all duration-200 active:scale-95"
                >
                  <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                  </svg>
                  {video.price}金币
                </button>
              )
            ) : (
              <span className="bg-gradient-to-r from-green-400 to-emerald-500 text-white px-2 py-1 rounded-md text-xs font-bold shadow-lg inline-flex items-center">
                <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"/>
                </svg>
                限时免费
              </span>
            )}
          </div>

          {/* 标题 */}
          <h3 className="text-white font-semibold text-base leading-tight">
            {video.title}
          </h3>

          {/* 描述 */}
          {video.description && (
            <p className="text-white text-sm opacity-90 leading-relaxed line-clamp-2">
              {video.description}
            </p>
          )}

          {/* 标签 */}
          {video.tags && (
            <div className="flex flex-wrap gap-2">
              {video.tags.split(',').slice(0, 3).map((tag: string, index: number) => (
                <span
                  key={index}
                  className="text-white text-xs bg-black bg-opacity-30 px-2 py-1 rounded-full"
                >
                  #{tag.trim()}
                </span>
              ))}
            </div>
          )}

          {/* 统计信息 */}
          <div className="flex items-center space-x-4 text-white text-xs opacity-75">
            <span>👁️ {formatCount(video.viewCount || 0)}</span>
            <span>⏱️ {Math.floor((video.duration || 0) / 60)}:{((video.duration || 0) % 60).toString().padStart(2, '0')}</span>
            {video.category && <span>📂 {video.category}</span>}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ShortVideoOverlay;
