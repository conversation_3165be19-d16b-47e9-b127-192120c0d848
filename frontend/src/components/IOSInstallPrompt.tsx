import React, { useState, useEffect } from 'react';
import './IOSInstallPrompt.css';

interface IOSInstallPromptProps {
  onDismiss?: () => void;
  autoShow?: boolean;
}

const IOSInstallPrompt: React.FC<IOSInstallPromptProps> = ({ onDismiss, autoShow = true }) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isIOS, setIsIOS] = useState(false);
  const [isStandalone, setIsStandalone] = useState(false);
  const [hasBeenDismissed, setHasBeenDismissed] = useState(false);
  const [bottomOffset, setBottomOffset] = useState(80); // 默认底部偏移

  useEffect(() => {
    // 检测是否是iOS设备
    const checkIsIOS = () => {
      const userAgent = navigator.userAgent.toLowerCase();
      return /iphone|ipad|ipod/.test(userAgent);
    };

    // 检测是否已经是standalone模式（已添加到主屏幕）
    const checkIsStandalone = () => {
      return (window.navigator as any).standalone === true ||
             window.matchMedia('(display-mode: standalone)').matches;
    };

    // 检测是否在Safari中
    const checkIsSafari = () => {
      const userAgent = navigator.userAgent.toLowerCase();
      return /safari/.test(userAgent) && !/chrome|crios|fxios/.test(userAgent);
    };

    // 检查是否已经被用户永久关闭
    const checkHasBeenDismissed = () => {
      return localStorage.getItem('ios-install-prompt-dismissed') === 'true';
    };

    const iosDevice = checkIsIOS();
    const standalone = checkIsStandalone();
    const safari = checkIsSafari();
    const dismissed = checkHasBeenDismissed();

    setIsIOS(iosDevice);
    setIsStandalone(standalone);
    setHasBeenDismissed(dismissed);

    // 计算底部偏移量，避免被底部导航遮挡（底部导航现在是固定高度）
    const calculateBottomOffset = () => {
      const screenHeight = window.innerHeight;
      const isSmallScreen = screenHeight < 700;
      const hasTabBar = document.querySelector('[class*="TabBar"]') ||
                       document.querySelector('.fixed.bottom-0') ||
                       document.querySelector('[class*="tab-bar"]');

      if (hasTabBar) {
        // 有底部导航栏时，使用固定偏移量（不考虑安全区域）
        return isSmallScreen ? 80 : 84; // 64px导航栏高度 + 16-20px间距
      } else {
        // 没有底部导航栏时，使用较小偏移量
        return isSmallScreen ? 20 : 30;
      }
    };

    setBottomOffset(calculateBottomOffset());

    // 监听窗口大小变化，动态调整偏移量
    const handleResize = () => {
      setBottomOffset(calculateBottomOffset());
    };

    window.addEventListener('resize', handleResize);

    // 显示条件：iOS设备 + Safari浏览器 + 非standalone模式 + 未被永久关闭 + 自动显示
    if (iosDevice && safari && !standalone && !dismissed && autoShow) {
      // 延迟3秒显示，让用户先体验一下网站
      const timer = setTimeout(() => {
        setIsVisible(true);
      }, 3000);

      return () => {
        clearTimeout(timer);
        window.removeEventListener('resize', handleResize);
      };
    }

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [autoShow]);

  const handleDismiss = (permanent = false) => {
    setIsVisible(false);
    
    if (permanent) {
      localStorage.setItem('ios-install-prompt-dismissed', 'true');
      setHasBeenDismissed(true);
    } else {
      // 临时关闭，24小时后再次显示
      const dismissTime = Date.now();
      localStorage.setItem('ios-install-prompt-temp-dismissed', dismissTime.toString());
    }

    if (onDismiss) {
      onDismiss();
    }
  };

  const handleInstallClick = () => {
    // 滚动到页面顶部，确保用户能看到Safari的分享按钮
    window.scrollTo({ top: 0, behavior: 'smooth' });
    
    // 显示详细安装指南
    setIsVisible(false);
    
    // 可以触发一个更详细的安装指南模态框
    showDetailedInstallGuide();
  };

  const showDetailedInstallGuide = () => {
    // 这里可以显示一个更详细的安装指南
    alert(`安装步骤：
1. 点击Safari底部的分享按钮 📤
2. 向下滚动找到"添加到主屏幕"选项
3. 点击"添加到主屏幕"
4. 确认添加，应用图标将出现在主屏幕上

添加后，您可以像使用原生App一样使用赫本视频！`);
  };

  // 检查是否应该显示（考虑临时关闭）
  const shouldShow = () => {
    if (!isVisible || hasBeenDismissed) return false;

    const tempDismissed = localStorage.getItem('ios-install-prompt-temp-dismissed');
    if (tempDismissed) {
      const dismissTime = parseInt(tempDismissed);
      const now = Date.now();
      const hoursPassed = (now - dismissTime) / (1000 * 60 * 60);
      
      // 24小时后重新显示
      if (hoursPassed < 24) {
        return false;
      } else {
        localStorage.removeItem('ios-install-prompt-temp-dismissed');
      }
    }

    return true;
  };

  if (!shouldShow()) {
    return null;
  }

  return (
    <div
      className="ios-install-prompt fixed left-4 right-4 bg-gradient-to-r from-blue-500 to-indigo-600 text-white p-4 shadow-2xl z-[9999] transform transition-all duration-300 ease-in-out rounded-xl border border-blue-400 border-opacity-30"
      style={{ bottom: `${bottomOffset}px` }}
    >
      <div className="flex items-start space-x-3">
        {/* App图标 */}
        <div className="prompt-icon flex-shrink-0 w-12 h-12 bg-white rounded-xl flex items-center justify-center shadow-md">
          <span className="text-2xl">🎬</span>
        </div>

        {/* 内容 */}
        <div className="flex-1 min-w-0">
          <h3 className="prompt-title font-semibold text-lg mb-1">安装赫本视频App</h3>
          <p className="prompt-description text-blue-100 text-sm leading-relaxed">
            将赫本视频添加到主屏幕，获得更好的使用体验，就像原生App一样！
          </p>

          {/* 特性列表 */}
          <div className="prompt-features mt-2 flex flex-wrap gap-2">
            <span className="bg-blue-400 bg-opacity-50 px-2 py-1 rounded-full text-xs">
              🚀 快速启动
            </span>
            <span className="bg-blue-400 bg-opacity-50 px-2 py-1 rounded-full text-xs">
              📱 全屏体验
            </span>
            <span className="bg-blue-400 bg-opacity-50 px-2 py-1 rounded-full text-xs">
              🔔 消息通知
            </span>
          </div>
        </div>

        {/* 关闭按钮 */}
        <button
          onClick={() => handleDismiss(false)}
          className="flex-shrink-0 w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center hover:bg-opacity-30 transition-colors active:scale-95"
          aria-label="暂时关闭"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      {/* 操作按钮 */}
      <div className="prompt-buttons mt-4 flex space-x-3">
        <button
          onClick={handleInstallClick}
          className="flex-1 bg-white text-blue-600 font-semibold py-3 px-4 rounded-lg hover:bg-blue-50 transition-colors active:scale-95 transform duration-150 shadow-md"
        >
          📤 立即安装
        </button>
        <button
          onClick={() => handleDismiss(true)}
          className="px-4 py-3 text-blue-100 hover:text-white transition-colors text-sm active:scale-95 transform duration-150"
        >
          不再提示
        </button>
      </div>

      {/* 安装提示 */}
      <div className="prompt-hint mt-3 flex items-center justify-center space-x-2 text-blue-200 text-xs">
        <svg className="w-4 h-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        <span className="text-center">点击Safari底部的分享按钮，然后选择"添加到主屏幕"</span>
      </div>
    </div>
  );
};

// 手动显示安装提示的函数
export const showIOSInstallPrompt = () => {
  // 检测是否是iOS Safari
  const userAgent = navigator.userAgent.toLowerCase();
  const isIOS = /iphone|ipad|ipod/.test(userAgent);
  const isSafari = /safari/.test(userAgent) && !/chrome|crios|fxios/.test(userAgent);
  const isStandalone = (window.navigator as any).standalone === true ||
                      window.matchMedia('(display-mode: standalone)').matches;

  if (isIOS && isSafari && !isStandalone) {
    // 创建并显示安装提示
    const event = new CustomEvent('show-ios-install-prompt');
    window.dispatchEvent(event);
    return true;
  }
  
  return false;
};

// 检查是否是iOS WebClip环境
export const isIOSWebClip = () => {
  return (window.navigator as any).standalone === true ||
         window.matchMedia('(display-mode: standalone)').matches;
};

// 检查是否是iOS设备
export const isIOSDevice = () => {
  const userAgent = navigator.userAgent.toLowerCase();
  return /iphone|ipad|ipod/.test(userAgent);
};

export default IOSInstallPrompt;
