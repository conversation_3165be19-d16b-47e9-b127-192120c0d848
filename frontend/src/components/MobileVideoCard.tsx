import React, { useState } from 'react';
import { useAuthStore } from '../store/authStore';
import type { VideoCardProps, Video } from '../types';
import { processCoverUrl } from '../utils/videoUrl';

interface MobileVideoCardProps extends VideoCardProps {
  compact?: boolean;
  onPurchaseClick?: (video: Video) => void;
  isViewed?: boolean;
}

const MobileVideoCard: React.FC<MobileVideoCardProps> = ({
  video,
  onClick,
  showPrice = true,
  showProgress = false,
  compact = false,
  onPurchaseClick,
  isViewed = false
}) => {
  const { user } = useAuthStore();
  const [imageLoaded, setImageLoaded] = useState(false);
  const handleClick = () => {
    if (onClick) {
      onClick(video);
    }
  };

  const formatDuration = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  const formatViewCount = (count: number): string => {
    if (count >= 10000) {
      return `${(count / 10000).toFixed(1)}万`;
    }
    return count.toString();
  };

  const getStatusBadge = () => {
    if (!video.price || video.price === 0) {
      return (
        <span className="bg-gradient-to-r from-green-400 to-emerald-500 text-white text-xs px-1.5 py-0.5 rounded-full font-bold flex items-center shadow-sm">
          <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"/>
          </svg>
          免费
        </span>
      );
    } else if (video.hasPurchased) {
      return (
        <span className="bg-gradient-to-r from-green-400 to-green-600 text-white text-xs px-1.5 py-0.5 rounded-full font-bold flex items-center shadow-sm">
          <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"/>
          </svg>
          已购
        </span>
      );
    } else if (user?.isVip) {
      return (
        <span className="bg-gradient-to-r from-purple-500 to-pink-600 text-white text-xs px-1.5 py-0.5 rounded-full font-bold flex items-center shadow-sm">
          <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
          </svg>
          VIP
        </span>
      );
    } else {
      return (
        <span className="bg-gradient-to-r from-yellow-400 to-orange-500 text-white text-xs px-1.5 py-0.5 rounded-full font-bold shadow-sm flex items-center hover:from-yellow-500 hover:to-orange-600 transition-all duration-200 active:scale-95">
          <svg className="w-2.5 h-2.5 mr-0.5" fill="currentColor" viewBox="0 0 20 20">
            <path d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
          </svg>
          {video.price}
        </span>
      );
    }
  };

  if (compact) {
    return (
      <div className={`bg-white rounded-xl overflow-hidden shadow-sm active:scale-98 transition-transform duration-150 ${isViewed ? 'opacity-75' : ''}`} onClick={handleClick}>
        <div className="relative overflow-hidden aspect-video">
          <img
            src={processCoverUrl(video.cover || '')}
            alt={video.title}
            className={`w-full h-full object-cover image-fade-in ${imageLoaded ? 'loaded' : ''}`}
            loading="lazy"
            decoding="async"
            onLoad={() => setImageLoaded(true)}
            onError={(e) => {
              const target = e.target as HTMLImageElement;
              target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEyMCIgdmlld0JveD0iMCAwIDIwMCAxMjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMTIwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik04NSA0NUw5NSA1MEw4NSA1NVY0NVoiIGZpbGw9IiM5Q0EzQUYiLz4KPC9zdmc+';
              setImageLoaded(true); // 即使错误也显示
            }}
          />

          {/* 时长显示 */}
          <div className="absolute bottom-1.5 right-1.5 bg-black bg-opacity-75 text-white text-xs px-1.5 py-0.5 rounded text-xs font-medium">
            {formatDuration(video.duration)}
          </div>

          {/* 状态标签 */}
          <div className="absolute top-1 right-1">
            {getStatusBadge()}
          </div>

          {/* 已查看标识 */}
          {isViewed && (
            <div className="absolute top-1 left-1">
              <span className="bg-gray-600 bg-opacity-80 text-white text-xs px-1.5 py-0.5 rounded-full font-medium flex items-center">
                <svg className="w-2.5 h-2.5 mr-0.5" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"/>
                  <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd"/>
                </svg>
                已看
              </span>
            </div>
          )}

          {/* 悬停效果覆盖层 */}
          <div className="absolute inset-0 bg-black bg-opacity-0 active:bg-opacity-10 transition-all duration-150"></div>
        </div>

        <div className="p-2">
          <h3 className="text-xs font-medium text-gray-900 line-clamp-2 mb-1 leading-4" style={{ minHeight: '30px' }}>
            {video.description}
          </h3>

          <div className="flex items-center justify-between text-xs text-gray-500">
            <span>{formatViewCount(video.viewCount)}次</span>
            <div className="flex items-center space-x-1">
              {video.isRecommended && (
                <span className="bg-gradient-to-r from-red-400 to-pink-500 text-white text-xs px-1 py-0.5 rounded font-bold shadow-sm">荐</span>
              )}
              {video.isHot && (
                <span className="bg-gradient-to-r from-orange-400 to-red-500 text-white text-xs px-1 py-0.5 rounded font-bold shadow-sm">热</span>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`mobile-video-card active:scale-98 transition-transform duration-150 ${isViewed ? 'opacity-75' : ''}`} onClick={handleClick}>
      <div className="relative overflow-hidden rounded-xl">
        <img
          src={processCoverUrl(video.cover || '')}
          alt={video.title}
          className={`mobile-video-thumbnail image-fade-in ${imageLoaded ? 'loaded' : ''}`}
          loading="lazy"
          decoding="async"
          onLoad={() => setImageLoaded(true)}
          onError={(e) => {
            const target = e.target as HTMLImageElement;
            target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEyMCIgdmlld0JveD0iMCAwIDIwMCAxMjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMTIwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik04NSA0NUw5NSA1MEw4NSA1NVY0NVoiIGZpbGw9IiM5Q0EzQUYiLz4KPC9zdmc+';
            setImageLoaded(true); // 即使错误也显示
          }}
        />

        {/* 时长显示 */}
        <div className="absolute bottom-2 right-2 bg-black bg-opacity-75 text-white text-xs px-1.5 py-0.5 rounded font-medium">
          {formatDuration(video.duration)}
        </div>

        {/* 状态标签 */}
        <div className="absolute top-2 left-2">
          {getStatusBadge()}
        </div>

        {/* 已查看标识 */}
        {isViewed && (
          <div className="absolute top-2 right-2 mr-2">
            <span className="bg-gray-600 bg-opacity-80 text-white text-xs px-2 py-1 rounded-full font-medium flex items-center">
              <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"/>
                <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd"/>
              </svg>
              已看过
            </span>
          </div>
        )}

        {/* 观看进度条 */}
        {showProgress && video.progress && (
          <div className="absolute bottom-0 left-0 right-0 h-1 bg-gray-200">
            <div
              className="h-full bg-pink-500 transition-all duration-300"
              style={{ width: `${(video.progress / video.duration) * 100}%` }}
            />
          </div>
        )}

        {/* 悬停效果覆盖层 */}
        <div className="absolute inset-0 bg-black bg-opacity-0 active:bg-opacity-10 transition-all duration-150"></div>
      </div>

      <div className="mobile-video-info">
        <h3 className="mobile-video-title">{video.title}</h3>

        <div className="mobile-video-meta">
          <div className="flex items-center space-x-2 flex-1">
            <span className="text-gray-500">{formatViewCount(video.viewCount)}次播放</span>
            {video.category && (
              <>
                <span className="text-gray-400">•</span>
                <span className="text-gray-500">{video.category}</span>
              </>
            )}
          </div>

          <div className="flex items-center space-x-1">
            {video.isRecommended && (
              <span className="bg-gradient-to-r from-red-400 to-pink-500 text-white text-xs px-2 py-1 rounded-full font-bold shadow-sm">推荐</span>
            )}
            {video.isHot && (
              <span className="bg-gradient-to-r from-orange-400 to-red-500 text-white text-xs px-2 py-1 rounded-full font-bold shadow-sm">热门</span>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default MobileVideoCard;
