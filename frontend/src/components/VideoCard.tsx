import React from 'react';
import { useAuthStore } from '../store/authStore';
import type { VideoCardProps, Video } from '../types';
import { processCoverUrl } from '../utils/videoUrl';

interface VideoCardPropsExtended extends VideoCardProps {
  onPurchaseClick?: (video: Video) => void;
}

const VideoCard: React.FC<VideoCardPropsExtended> = ({
  video,
  onClick,
  showPrice = true,
  showProgress = false,
  onPurchaseClick
}) => {
  const { user } = useAuthStore();
  const handleClick = () => {
    if (onClick) {
      onClick(video);
    }
  };

  const formatDuration = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  const formatViewCount = (count: number): string => {
    if (count >= 10000) {
      return `${(count / 10000).toFixed(1)}万`;
    }
    return count.toString();
  };

  const getStatusBadge = () => {
    if (!video.price || video.price === 0) {
      return <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded font-medium">免费</span>;
    } else if (video.hasPurchased) {
      return (
        <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded font-bold flex items-center">
          <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"/>
          </svg>
          已购买
        </span>
      );
    } else if (user?.isVip) {
      return (
        <span className="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded font-bold flex items-center">
          <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
          </svg>
          已解锁
        </span>
      );
    } else {
      return (
        <button
          onClick={(e) => {
            e.stopPropagation();
            onPurchaseClick && onPurchaseClick(video);
          }}
          className="bg-gradient-to-r from-yellow-400 to-orange-500 text-white text-xs px-2 py-1 rounded font-bold shadow-md flex items-center hover:from-yellow-500 hover:to-orange-600 transition-all duration-200 active:scale-95"
        >
          <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
          </svg>
          {video.price}金币
        </button>
      );
    }
  };

  return (
    <div className="video-card" onClick={handleClick}>
      <div className="relative">
        <img
          src={processCoverUrl(video.cover || '')}
          alt={video.title}
          className="video-thumbnail"
          loading="lazy"
        />

        {/* 时长显示 */}
        <div className="absolute bottom-2 right-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded">
          {formatDuration(video.duration)}
        </div>

        {/* 状态标签 */}
        <div className="absolute top-1 left-2">
          {getStatusBadge()}
        </div>

        {/* 观看进度条 */}
        {showProgress && video.progress && (
          <div className="absolute bottom-0 left-0 right-0 h-1 bg-gray-200">
            <div
              className="h-full bg-blue-600 transition-all duration-300"
              style={{ width: `${(video.progress / video.duration) * 100}%` }}
            />
          </div>
        )}

        {/* 悬停效果覆盖层 */}
        <div className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-10 transition-all duration-300"></div>
      </div>

      <div className="video-info">
        <h3 className="video-title">{video.title}</h3>

        <div className="flex items-center justify-between">
          <div className="video-meta">
            <span>{formatViewCount(video.viewCount)}次播放</span>
            {video.category && (
              <>
                <span className="mx-1">•</span>
                <span>{video.category}</span>
              </>
            )}
          </div>

          {showPrice && video.price && video.price > 0 && !video.hasPurchased && !user?.isVip && (
            <span
              className="bg-gradient-to-r from-yellow-400 to-orange-500 text-white px-2 py-1 rounded-full text-xs font-bold flex items-center hover:from-yellow-500 hover:to-orange-600 transition-all duration-200 active:scale-95"
            >
              <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
              {video.price}金币
            </span>
          )}
          {showPrice && video.price && video.price > 0 && !video.hasPurchased && user?.isVip && (
            <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs font-bold flex items-center">
              <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
              已解锁
            </span>
          )}
        </div>

        {video.description && (
          <p className="text-sm text-gray-500 mt-2 line-clamp-2">
            {video.description}
          </p>
        )}

        {/* 推荐和热门标签 */}
        <div className="flex gap-2 mt-2">
          {video.isRecommended && (
            <span className="bg-red-100 text-red-800 text-xs px-2 py-1 rounded">推荐</span>
          )}
          {video.isHot && (
            <span className="bg-orange-100 text-orange-800 text-xs px-2 py-1 rounded">热门</span>
          )}
        </div>
      </div>
    </div>
  );
};

export default VideoCard;
