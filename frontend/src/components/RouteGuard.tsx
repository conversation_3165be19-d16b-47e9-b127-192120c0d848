import React, { useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';

interface RouteGuardProps {
  children: React.ReactNode;
  requireHomeVisit?: boolean; // 是否需要先访问首页
}

// 会话存储键名
const HOME_VISITED_KEY = 'home_visited_session';
const ALLOWED_DIRECT_ROUTES = [
  '/',
  '/link-email'
];

const RouteGuard: React.FC<RouteGuardProps> = ({
  children,
  requireHomeVisit = true
}) => {
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    // 检查当前路由是否允许直接访问
    const isAllowedDirectRoute = ALLOWED_DIRECT_ROUTES.includes(location.pathname);

    if (isAllowedDirectRoute) {
      // 如果是首页，标记为已访问
      if (location.pathname === '/') {
        sessionStorage.setItem(HOME_VISITED_KEY, 'true');
      }
      return;
    }

    // 如果不需要首页访问检查，直接渲染
    if (!requireHomeVisit) {
      return;
    }

    // 检查是否已经访问过首页
    const hasVisitedHome = sessionStorage.getItem(HOME_VISITED_KEY) === 'true';

    if (!hasVisitedHome) {
      console.log('用户尝试直接访问深层链接，重定向到首页');

      // 保存用户想要访问的原始路径
      const intendedPath = location.pathname + location.search + location.hash;
      sessionStorage.setItem('intended_path', intendedPath);

      // 重定向到首页
      navigate('/', { replace: true });
      return;
    }

  }, [location.pathname, navigate, requireHomeVisit]);

  return <>{children}</>;
};

// 检查是否有待访问的路径，并导航到该路径
export const checkAndNavigateToIntendedPath = (navigate: ReturnType<typeof useNavigate>) => {
  const intendedPath = sessionStorage.getItem('intended_path');

  if (intendedPath && intendedPath !== '/') {
    // 清除保存的路径
    sessionStorage.removeItem('intended_path');

    // 延迟导航，确保首页已经加载
    setTimeout(() => {
      console.log('导航到用户原本想访问的路径:', intendedPath);
      navigate(intendedPath, { replace: true });
    }, 1000); // 1秒延迟，让用户看到首页
  }
};

// 重置访问状态（用于登出或需要重新验证时）
export const resetHomeVisitStatus = () => {
  sessionStorage.removeItem(HOME_VISITED_KEY);
  sessionStorage.removeItem('intended_path');
};

// 手动标记首页已访问（用于特殊情况）
export const markHomeAsVisited = () => {
  sessionStorage.setItem(HOME_VISITED_KEY, 'true');
};

// 检查是否已访问首页
export const hasVisitedHome = (): boolean => {
  return sessionStorage.getItem(HOME_VISITED_KEY) === 'true';
};

export default RouteGuard;
