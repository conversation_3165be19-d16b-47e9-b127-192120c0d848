import React, { useState, useEffect, useRef, useCallback } from 'react';
import VideoCard from './VideoCard';
import type { Video } from '../types';

interface VirtualVideoGridProps {
  videos: Video[];
  onVideoClick: (video: Video) => void;
  onPurchaseClick?: (video: Video) => void;
  onLoadMore?: () => void;
  hasMore?: boolean;
  loading?: boolean;
  itemHeight?: number;
  itemsPerRow?: number;
  containerHeight?: number;
}

const VirtualVideoGrid: React.FC<VirtualVideoGridProps> = ({
  videos,
  onVideoClick,
  onPurchaseClick,
  onLoadMore,
  hasMore = false,
  loading = false,
  itemHeight = 320, // 每个视频卡片的高度
  itemsPerRow = 4, // 每行显示的视频数量
  containerHeight = 600 // 容器高度
}) => {
  const [scrollTop, setScrollTop] = useState(0);
  const [containerWidth, setContainerWidth] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);
  const scrollElementRef = useRef<HTMLDivElement>(null);

  // 计算实际的每行项目数（响应式）
  const getItemsPerRow = useCallback(() => {
    if (containerWidth < 640) return 1; // 小屏幕
    if (containerWidth < 768) return 2; // 中等屏幕
    if (containerWidth < 1024) return 3; // 大屏幕
    return itemsPerRow; // 超大屏幕
  }, [containerWidth, itemsPerRow]);

  const actualItemsPerRow = getItemsPerRow();
  const rowHeight = itemHeight + 24; // 包含间距
  const totalRows = Math.ceil(videos.length / actualItemsPerRow);
  const totalHeight = totalRows * rowHeight;

  // 计算可见区域
  const visibleStart = Math.floor(scrollTop / rowHeight);
  const visibleEnd = Math.min(
    visibleStart + Math.ceil(containerHeight / rowHeight) + 1,
    totalRows
  );

  // 获取可见的视频项
  const visibleItems = [];
  for (let rowIndex = visibleStart; rowIndex < visibleEnd; rowIndex++) {
    const startIndex = rowIndex * actualItemsPerRow;
    const endIndex = Math.min(startIndex + actualItemsPerRow, videos.length);
    
    for (let i = startIndex; i < endIndex; i++) {
      if (videos[i]) {
        visibleItems.push({
          video: videos[i],
          index: i,
          row: rowIndex,
          col: i % actualItemsPerRow,
          top: rowIndex * rowHeight,
          left: (i % actualItemsPerRow) * (100 / actualItemsPerRow)
        });
      }
    }
  }

  // 监听滚动事件
  const handleScroll = useCallback((e: Event) => {
    const target = e.target as HTMLDivElement;
    setScrollTop(target.scrollTop);

    // 检查是否需要加载更多
    if (
      hasMore &&
      !loading &&
      onLoadMore &&
      target.scrollTop + target.clientHeight >= target.scrollHeight - 100
    ) {
      onLoadMore();
    }
  }, [hasMore, loading, onLoadMore]);

  // 监听容器大小变化
  const handleResize = useCallback(() => {
    if (containerRef.current) {
      setContainerWidth(containerRef.current.clientWidth);
    }
  }, []);

  useEffect(() => {
    const scrollElement = scrollElementRef.current;
    if (scrollElement) {
      scrollElement.addEventListener('scroll', handleScroll, { passive: true });
      return () => scrollElement.removeEventListener('scroll', handleScroll);
    }
  }, [handleScroll]);

  useEffect(() => {
    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [handleResize]);

  return (
    <div ref={containerRef} className="w-full">
      <div
        ref={scrollElementRef}
        className="overflow-auto"
        style={{ height: containerHeight }}
      >
        <div
          className="relative"
          style={{ height: totalHeight }}
        >
          {visibleItems.map(({ video, index, top, left }) => (
            <div
              key={`${video.id}-${index}`}
              className="absolute"
              style={{
                top: `${top}px`,
                left: `${left}%`,
                width: `${100 / actualItemsPerRow}%`,
                height: `${itemHeight}px`,
                padding: '0 12px 24px 12px'
              }}
            >
              <VideoCard
                video={video}
                onClick={onVideoClick}
                onPurchaseClick={onPurchaseClick}
                showPrice={true}
              />
            </div>
          ))}

          {/* 加载更多指示器 */}
          {hasMore && (
            <div
              className="absolute w-full flex justify-center items-center"
              style={{
                top: `${totalHeight}px`,
                height: '60px'
              }}
            >
              {loading ? (
                <div className="flex items-center">
                  <div className="loading-spinner mr-2"></div>
                  <span className="text-gray-600">加载中...</span>
                </div>
              ) : (
                <button
                  onClick={onLoadMore}
                  className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  加载更多
                </button>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default VirtualVideoGrid;
