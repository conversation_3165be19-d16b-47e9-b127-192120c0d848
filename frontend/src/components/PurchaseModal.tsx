import React, { useEffect, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuthStore } from '../store/authStore';
import type { Video } from '../types';
import { useAnalytics } from '../hooks/useAnalytics';
import { Coins, Crown, Mail, Gift, ChevronRight } from 'lucide-react';

interface PurchaseModalProps {
  isOpen: boolean;
  video: Video | null;
  onClose: () => void;
  onPurchase: (video: Video) => void | Promise<void>;
  previewDuration?: number; // 预览时长（分钟），默认1分钟
  mockUser?: any; // 用于测试的模拟用户数据
}

const PurchaseModal: React.FC<PurchaseModalProps> = ({
  isOpen,
  video,
  onClose,
  onPurchase,
  previewDuration = 1,
  mockUser
}) => {
  const navigate = useNavigate();
  const { user: storeUser } = useAuthStore();
  const { trackCustomEvent } = useAnalytics();

  // 使用模拟用户数据（如果提供）或真实用户数据
  const user = mockUser || storeUser;

  // 手势交互状态
  const modalRef = useRef<HTMLDivElement>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [dragY, setDragY] = useState(0);
  const [startY, setStartY] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);
  const [isClosing, setIsClosing] = useState(false);

  // 跟踪购买弹窗显示
  useEffect(() => {
    if (isOpen && video) {
      trackCustomEvent('purchase_modal_show', 'purchase_modal', video.title, video.price, {
        video_id: video.id,
        video_title: video.title,
        video_price: video.price || 0,
        user_coins: user?.coins || 0,
        has_enough_coins: user ? user.coins >= (video.price || 0) : false,
        preview_duration: previewDuration,
      });
    }
  }, [isOpen, video, user, previewDuration, trackCustomEvent]);

  // 全局鼠标事件监听器
  useEffect(() => {
    if (!isDragging) return;

    const handleGlobalMouseMove = (e: MouseEvent) => {
      const currentY = e.clientY;
      const deltaY = currentY - startY;

      // 只允许向下拖拽
      if (deltaY > 0) {
        setDragY(deltaY);
      }
    };

    const handleGlobalMouseUp = () => {
      setIsDragging(false);

      // 如果拖拽距离超过100px，关闭弹框
      if (dragY > 100) {
        onClose();
      } else {
        // 否则回弹到原位
        setDragY(0);
      }
    };

    document.addEventListener('mousemove', handleGlobalMouseMove);
    document.addEventListener('mouseup', handleGlobalMouseUp);

    return () => {
      document.removeEventListener('mousemove', handleGlobalMouseMove);
      document.removeEventListener('mouseup', handleGlobalMouseUp);
    };
  }, [isDragging, startY, dragY, onClose]);

  // 动画结束事件处理
  const handleAnimationEnd = (e: React.AnimationEvent) => {
    if (e.animationName === 'slideUp') {
      setIsAnimating(false);
    } else if (e.animationName === 'slideDown') {
      setIsClosing(false);
      onClose();
    }
  };

  // 重置拖拽状态当弹框打开时
  useEffect(() => {
    if (isOpen) {
      // 立即设置初始状态
      setIsDragging(false);
      setDragY(0);
      setStartY(0);
      setIsClosing(false);
      setIsAnimating(true);
    } else {
      setIsAnimating(false);
      setIsClosing(false);
    }
  }, [isOpen]);

  if (!isOpen || !video) return null;

  // 如果视频已购买，不显示购买弹框
  if (video.hasPurchased) return null;

  // 如果用户是VIP，不显示购买弹框
  if (user?.isVip) return null;

  // 检查金币余额是否足够
  const hasEnoughCoins = user ? user.coins >= (video.price || 0) : false;
  const currentCoins = user?.coins || 0;
  const videoPrice = video.price || 0;

  const handlePurchase = async () => {
    if (!hasEnoughCoins) {
      // 余额不足，跳转到充值页面
      handleRecharge();
      return;
    }
    onClose();
    await onPurchase(video);
  };

  const handleVipUpgrade = () => {
    // 跟踪VIP升级点击
    trackCustomEvent('vip_upgrade_click', 'purchase_modal', video.title, video.price);

    onClose();
    navigate('/vip');
  };

  const handleRecharge = () => {
    // 跟踪充值点击
    trackCustomEvent('recharge_click', 'purchase_modal', video.title, video.price, {
      reason: 'insufficient_balance',
      current_coins: user?.coins || 0,
      required_coins: video.price || 0,
      shortage: (video.price || 0) - (user?.coins || 0),
    });

    onClose();
    navigate('/recharge');
  };

  // 手势处理函数
  const handleTouchStart = (e: React.TouchEvent) => {
    setIsDragging(true);
    setStartY(e.touches[0].clientY);
    setDragY(0);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    if (!isDragging) return;

    const currentY = e.touches[0].clientY;
    const deltaY = currentY - startY;

    // 只允许向下拖拽
    if (deltaY > 0) {
      setDragY(deltaY);
    }
  };

  const handleTouchEnd = () => {
    if (!isDragging) return;

    setIsDragging(false);

    // 如果拖拽距离超过100px，关闭弹框
    if (dragY > 100) {
      onClose();
    } else {
      // 否则回弹到原位
      setDragY(0);
    }
  };

  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(true);
    setStartY(e.clientY);
    setDragY(0);
  };

  // 带动画的关闭函数
  const handleCloseWithAnimation = () => {
    setIsAnimating(false);
    setIsClosing(true);
    // 动画结束后会通过 handleAnimationEnd 自动关闭
  };



  return (
    <div className="fixed inset-0 bg-black bg-opacity-10 flex items-end justify-center z-50 p-0">
      <div
        ref={modalRef}
        className={`bg-white rounded-t-3xl w-full max-w-md mx-auto shadow-2xl draggable-modal ${
          isDragging ? 'dragging' : ''
        } ${isAnimating && !isClosing && !isDragging ? 'animate-slide-up' : ''} ${isClosing ? 'animate-slide-down' : ''} ${!isAnimating && !isClosing && !isDragging ? 'modal-visible' : ''}`}
        style={{
          transform: isDragging ? `translateY(${dragY}px)` : undefined,
          opacity: isDragging ? Math.max(0.5, 1 - dragY / 200) : 1
        }}
        onClick={(e) => e.stopPropagation()}
        onAnimationEnd={handleAnimationEnd}
      >
        {/* 顶部拖拽指示器 */}
        <div
          className="flex justify-center pt-3 pb-1 drag-handle"
          onTouchStart={handleTouchStart}
          onTouchMove={handleTouchMove}
          onTouchEnd={handleTouchEnd}
          onMouseDown={handleMouseDown}
        >
          <div className="w-10 h-1 bg-gray-300 rounded-full hover:bg-gray-400 transition-colors"></div>
        </div>

        <div className="px-4 pb-6">
          {/* 头部图标和标题 */}
          <div className="text-center mb-4">
            <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center mx-auto mb-3">
              <Coins className="w-6 h-6 text-white" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-1">
              解锁完整视频
            </h3>
            <p className="text-gray-500 text-xs">
              您已观看了 {previewDuration} 分钟免费预览
            </p>
          </div>

          {/* 余额卡片 */}
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-3 mb-4 border border-blue-100">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center">
                <div className="w-6 h-6 bg-blue-500 rounded-lg flex items-center justify-center mr-2">
                  <Coins className="w-3 h-3 text-white" />
                </div>
                <span className="text-gray-700 text-sm font-medium">当前余额</span>
              </div>
              <span className="text-lg font-bold text-blue-600">{currentCoins}</span>
            </div>

            <div className="flex items-center justify-between mb-2">
              <span className="text-gray-600 text-sm">视频价格</span>
              <span className="text-base font-semibold text-orange-600">-{videoPrice}</span>
            </div>

            <div className="border-t border-blue-200 pt-2">
              <div className="flex items-center justify-between">
                <span className="text-gray-700 text-sm font-medium">购买后余额</span>
                <span className={`text-base font-bold ${hasEnoughCoins ? 'text-green-600' : 'text-red-500'}`}>
                  {hasEnoughCoins ? (currentCoins - videoPrice) : '不足'}
                </span>
              </div>
            </div>
          </div>

          {/* 邮箱绑定奖励提示（仅在未绑定且余额不足时显示） */}
          {!user?.email && !hasEnoughCoins && (
            <div className="bg-gradient-to-r from-orange-50 to-yellow-50 rounded-xl p-3 mb-4 border border-orange-200">
              <div className="flex items-start">
                <div className="w-8 h-8 bg-orange-500 rounded-lg flex items-center justify-center mr-2 flex-shrink-0">
                  <Gift className="w-4 h-4 text-white" />
                </div>
                <div className="flex-1">
                  <h4 className="font-medium text-orange-800 mb-1 text-sm">首次绑定邮箱奖励</h4>
                  <p className="text-xs text-orange-700 mb-2">
                    立即获得 <span className="font-bold">100金币</span> + <span className="font-bold">1天VIP</span>
                  </p>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleCloseWithAnimation();
                      navigate('/link-email');
                    }}
                    className="bg-orange-500 hover:bg-orange-600 text-white px-3 py-1.5 rounded-lg text-xs font-medium flex items-center transition-colors"
                  >
                    <Mail className="w-3 h-3 mr-1" />
                    立即绑定
                    <ChevronRight className="w-3 h-3 ml-1" />
                  </button>
                </div>
              </div>
            </div>
          )}
          {/* 操作按钮 */}
          <div className="space-y-2">
            {/* 主要购买按钮 */}
            <button
              onClick={(e) => {
                e.stopPropagation();
                handlePurchase();
              }}
              className={`w-full py-3 rounded-xl font-medium text-base transition-all duration-200 active:scale-95 ${
                hasEnoughCoins
                  ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-md hover:shadow-lg'
                  : 'bg-gradient-to-r from-orange-500 to-red-500 text-white shadow-md hover:shadow-lg'
              }`}
            >
              {hasEnoughCoins ? (
                <div className="flex items-center justify-center">
                  <Coins className="w-4 h-4 mr-2" />
                  立即购买 {videoPrice} 金币
                </div>
              ) : (
                <div className="flex items-center justify-center">
                  <Coins className="w-4 h-4 mr-2" />
                  余额不足，去充值
                </div>
              )}
            </button>

            {/* VIP 按钮 */}
            <button
              onClick={(e) => {
                e.stopPropagation();
                handleVipUpgrade();
              }}
              className="w-full py-3 bg-gradient-to-r from-yellow-400 to-yellow-600 text-white rounded-xl font-medium text-base shadow-md hover:shadow-lg transition-all duration-200 active:scale-95"
            >
              <div className="flex items-center justify-center">
                <Crown className="w-4 h-4 mr-2" />
                开通VIP免费观看
              </div>
            </button>

            {/* 关闭按钮 */}
            <button
              onClick={(e) => {
                e.stopPropagation();
                handleCloseWithAnimation();
              }}
              className="w-full py-3 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-xl font-medium text-base transition-all duration-200 active:scale-95"
            >
              取消
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PurchaseModal;
