import React from 'react';

interface VideoTagsProps {
  tags: string;
}

const VideoTags: React.FC<VideoTagsProps> = ({ tags }) => {
  if (!tags) return null;

  return (
    <div>
      <h3 className="text-base font-semibold text-gray-900 mb-2">标签</h3>
      <div className="flex flex-wrap gap-2">
        {tags.split(',').map((tag, index) => (
          <span
            key={index}
            className="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm"
          >
            {tag.trim()}
          </span>
        ))}
      </div>
    </div>
  );
};

export default VideoTags;
