import React from 'react';
import type { Video } from '../../types';

interface VideoActionsProps {
  video: Video;
  onToggleFavorite: () => void;
}

const VideoActions: React.FC<VideoActionsProps> = ({ video, onToggleFavorite }) => {
  return (
    <div className="flex items-center gap-2 flex-shrink-0">
      <button
        onClick={onToggleFavorite}
        className={`flex items-center gap-1.5 px-4 py-2 rounded-lg transition-colors whitespace-nowrap ${
          video.isFavorited
            ? 'bg-pink-500 text-white hover:bg-pink-600'
            : 'bg-pink-50 text-pink-600 hover:bg-pink-100'
        }`}
      >
        {video.isFavorited ? (
          <svg className="w-4 h-4 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clipRule="evenodd" />
          </svg>
        ) : (
          <svg className="w-4 h-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
          </svg>
        )}
        <span className="text-sm font-medium">{video.isFavorited ? '已收藏' : '收藏'}</span>
      </button>
    </div>
  );
};

export default VideoActions;
