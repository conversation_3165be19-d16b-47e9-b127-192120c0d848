import React from 'react';
import { useNavigate } from 'react-router-dom';
import MobileLayout from '../MobileLayout';

interface ErrorStateProps {
  error: string | null;
}

const ErrorState: React.FC<ErrorStateProps> = ({ error }) => {
  const navigate = useNavigate();

  return (
    <MobileLayout title="视频详情" showBack showTabBar={false}>
      <div className="text-center py-20">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">加载失败</h2>
        <p className="text-gray-600 mb-8">{error || '视频不存在'}</p>
        <button
          onClick={() => navigate('/')}
          className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
        >
          返回首页
        </button>
      </div>
    </MobileLayout>
  );
};

export default ErrorState;
