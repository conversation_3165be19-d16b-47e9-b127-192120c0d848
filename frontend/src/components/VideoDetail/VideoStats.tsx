import React from 'react';
import { useAuthStore } from '../../store/authStore';
import type { Video } from '../../types';

interface VideoStatsProps {
  video: Video;
  onPriceClick?: () => void;
}

const VideoStats: React.FC<VideoStatsProps> = ({ video, onPriceClick }) => {
  const { user } = useAuthStore();

  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  return (
    <div className="flex items-center flex-wrap gap-3 text-sm text-gray-600">
      <span>{video.viewCount} 次观看</span>
      <span>{video.likeCount} 点赞</span>
      <span>{formatDuration(video.duration)}</span>
      <span className="px-2 py-1 bg-gradient-to-r from-blue-400 to-indigo-500 text-white rounded-full text-xs font-medium shadow-sm">
        {video.category}
      </span>
      {video.price && video.price > 0 ? (
        video.hasPurchased ? (
          <span className="bg-gradient-to-r from-green-400 to-green-600 text-white px-3 py-1 rounded-full text-xs font-bold flex items-center shadow-md">
            <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"/>
            </svg>
            已购买
          </span>
        ) : user?.isVip ? (
          <span className="bg-gradient-to-r from-purple-500 to-pink-600 text-white px-3 py-1 rounded-full text-xs font-bold flex items-center shadow-md">
            <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
            已解锁
          </span>
        ) : (
          <button
            onClick={onPriceClick}
            className="bg-gradient-to-r from-yellow-400 to-orange-500 text-white px-3 py-1 rounded-full text-xs font-bold shadow-md flex items-center hover:from-yellow-500 hover:to-orange-600 transition-all duration-200 active:scale-95"
          >
            <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
              <path d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
            {video.price}金币
          </button>
        )
      ) : (
        <span className="bg-gradient-to-r from-green-400 to-emerald-500 text-white px-3 py-1 rounded-full text-xs font-bold flex items-center shadow-md">
          <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"/>
          </svg>
          限时免费
        </span>
      )}
    </div>
  );
};

export default VideoStats;
