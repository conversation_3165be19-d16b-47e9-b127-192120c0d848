# VideoDetail 组件库

这个目录包含了视频详情页面的所有组件化模块，提供了可复用、可维护的组件结构。

## 📁 组件结构

```
VideoDetail/
├── index.ts                 # 统一导出文件
├── VideoPlayer.tsx          # 视频播放器容器组件
├── VideoInfo.tsx            # 视频信息主组件（组合其他组件）
├── VideoStats.tsx           # 视频统计信息组件
├── VideoActions.tsx         # 视频操作按钮组件
├── VideoDescription.tsx     # 视频描述组件
├── VideoTags.tsx            # 视频标签组件
├── LoadingState.tsx         # 加载状态组件
├── ErrorState.tsx           # 错误状态组件
└── README.md               # 说明文档
```

## 🧩 组件说明

### VideoPlayer
视频播放器容器组件，封装了 HLSPlayer 的使用。

**Props:**
- `video: Video` - 视频数据
- `isPaidVideo: boolean` - 是否为付费视频
- `hasAccess: boolean` - 用户是否有观看权限
- `shouldPause: boolean` - 是否应该暂停
- `onPreviewEnd: () => void` - 预览结束回调
- `onVideoError: (error: string) => void` - 视频错误回调

### VideoInfo
视频信息主组件，组合了统计信息、操作按钮、描述和标签。

**Props:**
- `video: Video` - 视频数据
- `onToggleFavorite: () => void` - 收藏切换回调
- `onPriceClick: () => void` - 价格点击回调

### VideoStats
视频统计信息组件，显示观看次数、点赞数、时长、分类和价格。

**Props:**
- `video: Video` - 视频数据
- `onPriceClick?: () => void` - 价格点击回调（可选）

### VideoActions
视频操作按钮组件，目前包含收藏功能。

**Props:**
- `video: Video` - 视频数据
- `onToggleFavorite: () => void` - 收藏切换回调

### VideoDescription
视频描述组件，显示视频的详细描述。

**Props:**
- `description: string` - 视频描述

### VideoTags
视频标签组件，显示视频的标签列表。

**Props:**
- `tags: string` - 标签字符串（逗号分隔）

### LoadingState
加载状态组件，显示加载中的界面。

**Props:** 无

### ErrorState
错误状态组件，显示错误信息和返回按钮。

**Props:**
- `error: string | null` - 错误信息

## 🔄 使用示例

```tsx
import {
  VideoPlayer,
  VideoInfo,
  LoadingState,
  ErrorState
} from '../components/VideoDetail';

// 在页面中使用
<VideoPlayer
  video={video}
  isPaidVideo={video.price > 0}
  hasAccess={canWatch(video)}
  shouldPause={showModal}
  onPreviewEnd={() => setShowModal(true)}
  onVideoError={(error) => console.error(error)}
/>

<VideoInfo
  video={video}
  onToggleFavorite={handleToggleFavorite}
  onPriceClick={() => setShowModal(true)}
/>
```

## ✨ 优势

1. **可复用性**: 每个组件都可以独立使用
2. **可维护性**: 逻辑分离，易于维护和测试
3. **可扩展性**: 可以轻松添加新功能或修改现有功能
4. **类型安全**: 使用 TypeScript 确保类型安全
5. **组合灵活**: 可以根据需要组合不同的组件

## 🎯 设计原则

- **单一职责**: 每个组件只负责一个特定功能
- **松耦合**: 组件之间通过 props 通信，减少依赖
- **高内聚**: 相关功能聚合在同一个组件中
- **可测试**: 每个组件都可以独立测试
