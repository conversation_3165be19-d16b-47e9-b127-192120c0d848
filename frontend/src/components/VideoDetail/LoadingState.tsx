import React from 'react';
import MobileLayout from '../MobileLayout';

const LoadingState: React.FC = () => {
  return (
    <MobileLayout title="视频详情" showBack showTabBar={false}>
      <div className="flex items-center justify-center py-20">
        <div className="loading-spinner"></div>
        <span className="ml-3 text-gray-600">加载中...</span>
      </div>
    </MobileLayout>
  );
};

export default LoadingState;
