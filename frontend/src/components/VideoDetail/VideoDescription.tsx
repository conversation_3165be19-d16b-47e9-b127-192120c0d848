import React from 'react';

interface VideoDescriptionProps {
  description: string;
}

const VideoDescription: React.FC<VideoDescriptionProps> = ({ description }) => {
  if (!description) return null;

  return (
    <div className="mb-4">
      <h3 className="text-base font-semibold text-gray-900 mb-2">视频简介</h3>
      <p className="text-gray-700 leading-relaxed text-sm">{description}</p>
    </div>
  );
};

export default VideoDescription;
