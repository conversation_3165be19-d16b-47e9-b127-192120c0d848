import React from 'react';
import type { Video } from '../../types';
import VideoStats from './VideoStats';
import VideoActions from './VideoActions';
import VideoDescription from './VideoDescription';
import VideoTags from './VideoTags';

interface VideoInfoProps {
  video: Video;
  onToggleFavorite: () => void;
  onPriceClick: () => void;
}

const VideoInfo: React.FC<VideoInfoProps> = ({
  video,
  onToggleFavorite,
  onPriceClick
}) => {
  return (
    <div className="bg-white rounded-lg shadow-md p-4 mb-6">
      {/* 视频标题 */}
      <h1
        className="text-lg font-bold text-gray-900 mb-3 leading-relaxed break-words"
        title={video.title}
      >
        {video.title}
      </h1>

      {/* 操作按钮区域 */}
      <div className="flex items-center justify-between mb-4">
        {/* 视频统计信息 */}
        <VideoStats video={video} onPriceClick={onPriceClick} />

        {/* 操作按钮 */}
        <VideoActions video={video} onToggleFavorite={onToggleFavorite} />
      </div>

      {/* 视频描述 */}
      <VideoDescription description={video.description || ''} />

      {/* 视频标签 */}
      <VideoTags tags={video.tags || ''} />
    </div>
  );
};

export default VideoInfo;
