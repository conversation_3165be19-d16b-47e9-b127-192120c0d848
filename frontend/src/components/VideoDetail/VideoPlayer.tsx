import React from 'react';
import HLSPlayer from '../HLSPlayer';
import type { Video } from '../../types';

interface VideoPlayerProps {
  video: Video;
  isPaidVideo: boolean;
  hasAccess: boolean;
  shouldPause: boolean;
  onPreviewEnd: () => void;
  onVideoError: (error: string) => void;
}

const VideoPlayer: React.FC<VideoPlayerProps> = ({
  video,
  isPaidVideo,
  hasAccess,
  shouldPause,
  onPreviewEnd,
  onVideoError
}) => {
  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden mb-6">
      <div className="bg-black relative" style={{ aspectRatio: '16/9' }}>
        <HLSPlayer
          video={video}
          className="w-full h-full"
          isPaidVideo={isPaidVideo}
          hasAccess={hasAccess}
          previewDuration={60}
          autoplay={false}
          shouldPause={shouldPause}
          onPreviewEnd={onPreviewEnd}
          onVideoError={onVideoError}
        />
      </div>
    </div>
  );
};

export default VideoPlayer;
