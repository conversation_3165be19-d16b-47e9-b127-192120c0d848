import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAnalytics } from '../hooks/useAnalytics';

interface InsufficientCoinsModalProps {
  isOpen: boolean;
  onClose: () => void;
  currentCoins: number;
  requiredCoins: number;
  itemName: string;
  itemType: 'vip' | 'video';
}

const InsufficientCoinsModal: React.FC<InsufficientCoinsModalProps> = ({
  isOpen,
  onClose,
  currentCoins,
  requiredCoins,
  itemName,
  itemType
}) => {
  const navigate = useNavigate();
  const { trackCustomEvent } = useAnalytics();
  const shortage = requiredCoins - currentCoins;

  // 跟踪弹框显示
  useEffect(() => {
    if (isOpen) {
      trackCustomEvent('insufficient_coins_modal_show', 'modal', itemName, requiredCoins, {
        item_type: itemType,
        item_name: itemName,
        required_coins: requiredCoins,
        current_coins: currentCoins,
        shortage: shortage,
      });
    }
  }, [isOpen, itemType, itemName, requiredCoins, currentCoins, shortage, trackCustomEvent]);

  const handleRecharge = () => {
    // 跟踪充值点击
    trackCustomEvent('insufficient_coins_recharge_click', 'modal', itemName, requiredCoins, {
      item_type: itemType,
      item_name: itemName,
      required_coins: requiredCoins,
      current_coins: currentCoins,
      shortage: shortage,
    });

    onClose();
    navigate('/recharge');
  };

  const handleClose = () => {
    // 跟踪关闭弹框
    trackCustomEvent('insufficient_coins_modal_close', 'modal', itemName, requiredCoins, {
      item_type: itemType,
      action: 'later',
    });

    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-sm w-full mx-4 overflow-hidden shadow-xl">
        {/* 头部 */}
        <div className="bg-red-50 px-4 py-3 border-b border-red-100">
          <div className="flex items-center">
            <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center mr-3">
              <svg className="w-5 h-5 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">金币余额不足</h3>
              <p className="text-sm text-gray-600">无法完成{itemType === 'vip' ? 'VIP开通' : '视频购买'}</p>
            </div>
          </div>
        </div>

        {/* 内容 */}
        <div className="p-4">
          {/* 商品信息 */}
          <div className="bg-gray-50 rounded-lg p-3 mb-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm text-gray-600">
                {itemType === 'vip' ? 'VIP套餐' : '视频'}
              </span>
              <span className="font-medium text-gray-900">{itemName}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">所需金币</span>
              <span className="font-semibold text-red-600">{requiredCoins} 金币</span>
            </div>
          </div>

          {/* 余额信息 */}
          <div className="space-y-3 mb-6">
            <div className="flex items-center justify-between">
              <span className="text-gray-600">当前余额</span>
              <span className="font-medium text-gray-900">{currentCoins} 金币</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-600">还需充值</span>
              <span className="font-semibold text-red-600">+{shortage} 金币</span>
            </div>
            <div className="border-t pt-3">
              <div className="flex items-center justify-between">
                <span className="text-gray-600">充值后余额</span>
                <span className="font-semibold text-green-600">
                  {currentCoins + Math.ceil(shortage / 10) * 10} 金币
                </span>
              </div>
              <p className="text-xs text-gray-500 mt-1">
                建议充值 {Math.ceil(shortage / 10) * 10} 金币 (¥{Math.ceil(shortage / 10)})
              </p>
            </div>
          </div>

          {/* 充值优惠提示 */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
            <div className="flex items-start">
              <div className="text-blue-600 mr-2">💡</div>
              <div>
                <div className="text-sm font-medium text-blue-900 mb-1">充值优惠</div>
                <div className="text-xs text-blue-700">
                  • 充值满50元送5金币
                  <br />
                  • 充值满100元送15金币
                  <br />
                  • 首次充值额外赠送10金币
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 底部按钮 */}
        <div className="bg-gray-50 px-4 py-3 flex space-x-3">
          <button
            onClick={handleClose}
            className="flex-1 py-2 px-4 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-100 transition-colors font-medium"
          >
            稍后充值
          </button>
          <button
            onClick={handleRecharge}
            className="flex-1 py-2 px-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
          >
            立即充值
          </button>
        </div>
      </div>
    </div>
  );
};

export default InsufficientCoinsModal;
