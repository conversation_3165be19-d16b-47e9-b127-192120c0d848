/* 视频播放器样式 */

/* 隐藏原生视频控件 */
video::-webkit-media-controls {
  display: none !important;
}

video::-webkit-media-controls-panel {
  display: none !important;
}

video::-webkit-media-controls-play-button {
  display: none !important;
}

video::-webkit-media-controls-start-playback-button {
  display: none !important;
}

video::-webkit-media-controls-timeline {
  display: none !important;
}

video::-webkit-media-controls-current-time-display {
  display: none !important;
}

video::-webkit-media-controls-time-remaining-display {
  display: none !important;
}

video::-webkit-media-controls-mute-button {
  display: none !important;
}

video::-webkit-media-controls-volume-slider {
  display: none !important;
}

video::-webkit-media-controls-fullscreen-button {
  display: none !important;
}

video::-webkit-media-controls-toggle-closed-captions-button {
  display: none !important;
}

/* Firefox */
video::-moz-media-controls {
  display: none !important;
}

/* 全屏时的样式 */
video:fullscreen {
  width: 100% !important;
  height: 100% !important;
  object-fit: contain !important;
  background-color: #000 !important;
}

video:-webkit-full-screen {
  width: 100% !important;
  height: 100% !important;
  object-fit: contain !important;
  background-color: #000 !important;
}

video:-moz-full-screen {
  width: 100% !important;
  height: 100% !important;
  object-fit: contain !important;
  background-color: #000 !important;
}

video:-ms-fullscreen {
  width: 100% !important;
  height: 100% !important;
  object-fit: contain !important;
  background-color: #000 !important;
}

/* 容器全屏时的样式 */
.video-container:fullscreen {
  background-color: #000 !important;
}

.video-container:-webkit-full-screen {
  background-color: #000 !important;
}

.video-container:-moz-full-screen {
  background-color: #000 !important;
}

.video-container:-ms-fullscreen {
  background-color: #000 !important;
}

/* 全屏时控件样式调整 */
.video-container:fullscreen .video-controls,
.video-container:-webkit-full-screen .video-controls,
.video-container:-moz-full-screen .video-controls,
.video-container:-ms-fullscreen .video-controls {
  padding: 16px 24px !important;
  font-size: 16px !important;
}

.video-container:fullscreen .video-progress-bar,
.video-container:-webkit-full-screen .video-progress-bar,
.video-container:-moz-full-screen .video-progress-bar,
.video-container:-ms-fullscreen .video-progress-bar {
  height: 6px !important;
}

.video-container:fullscreen .video-controls button,
.video-container:-webkit-full-screen .video-controls button,
.video-container:-moz-full-screen .video-controls button,
.video-container:-ms-fullscreen .video-controls button {
  width: 48px !important;
  height: 48px !important;
}

.video-container:fullscreen .video-controls svg,
.video-container:-webkit-full-screen .video-controls svg,
.video-container:-moz-full-screen .video-controls svg,
.video-container:-ms-fullscreen .video-controls svg {
  width: 24px !important;
  height: 24px !important;
}

/* 移动设备优化 */
@media (max-width: 768px) {
  /* 确保移动设备上视频不会被系统UI遮挡 */
  video:fullscreen {
    padding: env(safe-area-inset-top) env(safe-area-inset-right) env(safe-area-inset-bottom) env(safe-area-inset-left);
  }
  
  video:-webkit-full-screen {
    padding: env(safe-area-inset-top) env(safe-area-inset-right) env(safe-area-inset-bottom) env(safe-area-inset-left);
  }
}

/* iOS Safari 特殊处理 */
@supports (-webkit-touch-callout: none) {
  /* iOS Safari 全屏视频样式 */
  video::-webkit-media-controls {
    display: none !important;
    -webkit-appearance: none !important;
  }
  
  video {
    -webkit-playsinline: true;
    -webkit-appearance: none;
  }
}

/* 防止视频在某些浏览器中显示默认控件 */
video[controls] {
  controls: none !important;
}

/* 确保自定义控件在全屏时可见 */
.video-controls {
  z-index: 20 !important; /* 最高z-index */
}

/* 全屏时隐藏页面滚动条 */
body:has(.video-container:fullscreen),
body:has(.video-container:-webkit-full-screen),
body:has(.video-container:-moz-full-screen),
body:has(.video-container:-ms-fullscreen) {
  overflow: hidden !important;
}

/* 加载状态在全屏时的样式 */
.video-container:fullscreen .loading-spinner,
.video-container:-webkit-full-screen .loading-spinner,
.video-container:-moz-full-screen .loading-spinner,
.video-container:-ms-fullscreen .loading-spinner {
  width: 64px !important;
  height: 64px !important;
}

/* 播放按钮在全屏时的样式 */
.video-container:fullscreen .play-button,
.video-container:-webkit-full-screen .play-button,
.video-container:-moz-full-screen .play-button,
.video-container:-ms-fullscreen .play-button {
  width: 96px !important;
  height: 96px !important;
}

.video-container:fullscreen .play-button svg,
.video-container:-webkit-full-screen .play-button svg,
.video-container:-moz-full-screen .play-button svg,
.video-container:-ms-fullscreen .play-button svg {
  width: 48px !important;
  height: 48px !important;
}
