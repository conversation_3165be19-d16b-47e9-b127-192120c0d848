import React, { useState, useEffect } from 'react';
import { toast } from '../store/toastStore';

interface AppHttpsNoticeProps {
  onRetry?: () => void;
  onDismiss?: () => void;
}

const AppHttpsNotice: React.FC<AppHttpsNoticeProps> = ({ onRetry, onDismiss }) => {
  const [isVisible, setIsVisible] = useState(false);
  const [appInfo, setAppInfo] = useState({
    isApp: false,
    isMobile: false,
    platform: 'web' as 'ios' | 'android' | 'web',
    requiresHttps: false,
    currentApiUrl: '',
    httpsApiUrl: ''
  });

  useEffect(() => {
    // 检测App环境
    const userAgent = navigator.userAgent.toLowerCase();
    const currentApiUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3002/api';
    
    // 检测平台
    const isIOS = /iphone|ipad|ipod/.test(userAgent);
    const isAndroid = /android/.test(userAgent);
    const isMobile = isIOS || isAndroid;
    
    // 检测是否是App环境
    const isApp = (
      /wkwebview|uiwebview|wv|webview/.test(userAgent) ||
      !!(window as any).ReactNativeWebView ||
      !!(window as any).flutter_inappwebview ||
      /smartvideo-app/.test(userAgent) ||
      !!(window as any).webkit?.messageHandlers ||
      !!(window as any).Android
    );

    // App端现在强制要求HTTPS
    const requiresHttps = isApp || (isMobile && !window.location.hostname.includes('localhost'));
    
    // 生成HTTPS版本的API URL
    const httpsApiUrl = currentApiUrl.replace(/^http:/, 'https:');
    
    // 确定平台
    let platform: 'ios' | 'android' | 'web' = 'web';
    if (isIOS) platform = 'ios';
    else if (isAndroid) platform = 'android';

    const info = {
      isApp,
      isMobile,
      platform,
      requiresHttps,
      currentApiUrl,
      httpsApiUrl
    };

    setAppInfo(info);

    // 如果是App环境且API使用HTTP，显示提示
    if (requiresHttps && currentApiUrl.startsWith('http://')) {
      setIsVisible(true);
      console.warn('🔒 App端HTTPS要求:', {
        platform,
        isApp,
        message: `${platform.toUpperCase()} App要求使用HTTPS连接`,
        currentUrl: currentApiUrl,
        recommendedUrl: httpsApiUrl
      });
    }
  }, []);

  const handleRetry = () => {
    toast.info(`尝试使用HTTPS连接: ${appInfo.httpsApiUrl}`);
    
    if (onRetry) {
      onRetry();
    } else {
      // 刷新页面重试
      window.location.reload();
    }
  };

  const handleDismiss = () => {
    setIsVisible(false);
    if (onDismiss) {
      onDismiss();
    }
  };

  const handleContactSupport = () => {
    toast.info('请联系技术支持配置HTTPS服务器');
    // 这里可以添加联系技术支持的逻辑
  };

  if (!isVisible) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
        <div className="flex items-center mb-4">
          <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-4">
            <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
            </svg>
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">App安全连接要求</h3>
            <p className="text-sm text-gray-500">
              {appInfo.platform.toUpperCase()} App需要HTTPS连接
            </p>
          </div>
        </div>

        <div className="mb-6">
          <p className="text-gray-700 mb-3">
            {appInfo.platform === 'ios' && (
              <>iOS App Transport Security (ATS) 要求所有网络连接使用HTTPS协议。</>
            )}
            {appInfo.platform === 'android' && (
              <>Android 网络安全配置要求App使用安全连接。</>
            )}
            {appInfo.platform === 'web' && appInfo.isApp && (
              <>App环境要求使用HTTPS协议进行安全通信。</>
            )}
          </p>
          
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-3">
            <h4 className="font-medium text-blue-800 mb-2">当前状态：</h4>
            <div className="text-sm text-blue-700 space-y-1">
              <div>• 平台: {appInfo.platform.toUpperCase()}</div>
              <div>• 环境: {appInfo.isApp ? 'App内WebView' : '浏览器'}</div>
              <div>• 当前API: {appInfo.currentApiUrl}</div>
              <div>• 推荐API: {appInfo.httpsApiUrl}</div>
            </div>
          </div>

          <div className="bg-green-50 border border-green-200 rounded-lg p-3">
            <h4 className="font-medium text-green-800 mb-1">解决方案：</h4>
            <ul className="text-sm text-green-700 space-y-1">
              <li>• 配置API服务器支持HTTPS</li>
              <li>• 获取有效的SSL证书</li>
              <li>• 使用CDN服务提供HTTPS支持</li>
              <li>• 联系技术支持配置安全连接</li>
            </ul>
          </div>
        </div>

        <div className="flex flex-col space-y-3">
          <button
            onClick={handleRetry}
            className="w-full bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg font-medium transition-colors"
          >
            🔒 尝试HTTPS连接
          </button>
          
          <button
            onClick={handleContactSupport}
            className="w-full bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg font-medium transition-colors"
          >
            📞 联系技术支持
          </button>
          
          <button
            onClick={handleDismiss}
            className="w-full bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-lg font-medium transition-colors"
          >
            暂时忽略
          </button>
        </div>

        <div className="mt-4 text-xs text-gray-500 text-center">
          <p>App端现在要求使用HTTPS进行安全通信</p>
          <p className="mt-1">
            这是{appInfo.platform === 'ios' ? 'Apple' : appInfo.platform === 'android' ? 'Google' : '平台'}的安全要求
          </p>
        </div>
      </div>
    </div>
  );
};

export default AppHttpsNotice;
