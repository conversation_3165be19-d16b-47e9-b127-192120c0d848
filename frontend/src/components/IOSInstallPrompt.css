/* iOS安装提示组件样式 */

.ios-install-prompt {
  /* 确保提示框始终在最顶层 */
  z-index: 9999 !important;
  
  /* 添加backdrop-filter支持 */
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  
  /* 优化阴影效果 */
  box-shadow: 
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04),
    0 0 0 1px rgba(255, 255, 255, 0.1);
  
  /* 确保在iOS Safari中正确显示 */
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  
  /* 防止内容溢出 */
  max-height: calc(100vh - 120px);
  overflow-y: auto;
}

/* 针对不同屏幕尺寸的适配 */
@media (max-height: 600px) {
  .ios-install-prompt {
    /* 小屏幕设备上减少内边距 */
    padding: 12px !important;
    max-height: calc(100vh - 80px);
  }
  
  .ios-install-prompt .prompt-title {
    font-size: 16px !important;
    margin-bottom: 4px !important;
  }
  
  .ios-install-prompt .prompt-description {
    font-size: 13px !important;
  }
  
  .ios-install-prompt .prompt-features {
    margin-top: 8px !important;
    gap: 4px !important;
  }
  
  .ios-install-prompt .prompt-buttons {
    margin-top: 12px !important;
    gap: 8px !important;
  }
}

@media (max-height: 700px) {
  .ios-install-prompt {
    padding: 14px !important;
  }
}

/* 针对iPhone SE等小屏幕设备 */
@media (max-width: 375px) and (max-height: 667px) {
  .ios-install-prompt {
    left: 8px !important;
    right: 8px !important;
    padding: 12px !important;
  }
  
  .ios-install-prompt .prompt-icon {
    width: 40px !important;
    height: 40px !important;
  }
  
  .ios-install-prompt .prompt-icon span {
    font-size: 20px !important;
  }
}

/* 针对iPad等大屏幕设备 */
@media (min-width: 768px) {
  .ios-install-prompt {
    left: 50% !important;
    right: auto !important;
    transform: translateX(-50%) !important;
    width: 400px !important;
    max-width: calc(100vw - 32px) !important;
  }
}

/* 横屏模式适配 */
@media (orientation: landscape) and (max-height: 500px) {
  .ios-install-prompt {
    max-height: calc(100vh - 60px);
    padding: 10px !important;
  }
  
  .ios-install-prompt .prompt-features {
    display: none !important;
  }
  
  .ios-install-prompt .prompt-hint {
    display: none !important;
  }
}

/* 动画效果 */
.ios-install-prompt.entering {
  animation: slideUpFadeIn 0.3s ease-out forwards;
}

.ios-install-prompt.leaving {
  animation: slideDownFadeOut 0.3s ease-in forwards;
}

@keyframes slideUpFadeIn {
  from {
    opacity: 0;
    transform: translateY(100%) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes slideDownFadeOut {
  from {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
  to {
    opacity: 0;
    transform: translateY(100%) scale(0.95);
  }
}

/* 按钮点击效果 */
.ios-install-prompt button {
  transition: all 0.15s ease-in-out;
}

.ios-install-prompt button:active {
  transform: scale(0.95);
}

/* 确保在WebClip模式下正确显示 */
@media (display-mode: standalone) {
  .ios-install-prompt {
    /* WebClip模式下隐藏安装提示 */
    display: none !important;
  }
}

/* 安全区域适配 */
@supports (padding: max(0px)) {
  .ios-install-prompt {
    padding-bottom: max(16px, env(safe-area-inset-bottom));
    margin-bottom: max(0px, env(safe-area-inset-bottom));
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .ios-install-prompt {
    border: 2px solid rgba(255, 255, 255, 0.8) !important;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .ios-install-prompt {
    transition: none !important;
  }
  
  .ios-install-prompt.entering,
  .ios-install-prompt.leaving {
    animation: none !important;
  }
  
  .ios-install-prompt button {
    transition: none !important;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .ios-install-prompt {
    /* 在深色模式下保持蓝色渐变，但调整透明度 */
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.95), rgba(99, 102, 241, 0.95)) !important;
    border-color: rgba(255, 255, 255, 0.2) !important;
  }
}
