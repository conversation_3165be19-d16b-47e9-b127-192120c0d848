import React from 'react';
import HLSShortVideoPlayer from './HLSShortVideoPlayer';
import type { Video } from '../types';

interface ShortVideoPlayerProps {
  video: Video;
  isActive: boolean;
  onVideoEnd?: () => void;
  onVideoError?: (error: any) => void;
  // 付费视频预览相关
  isPaidVideo?: boolean;
  hasAccess?: boolean;
  previewDuration?: number;
  onPreviewEnd?: () => void;
  // 播放控制相关
  requireDoubleClickToPause?: boolean;
  shouldPause?: boolean; // 新增：外部控制暂停
}

const ShortVideoPlayer: React.FC<ShortVideoPlayerProps> = ({
  video,
  isActive,
  onVideoEnd,
  onVideoError,
  isPaidVideo,
  hasAccess,
  previewDuration,
  onPreviewEnd,
  requireDoubleClickToPause,
  shouldPause
}) => {
  // 使用HLS.js播放器 - 更好的HLS支持和密钥缓存
  return (
    <HLSShortVideoPlayer
      video={video}
      isActive={isActive}
      onVideoError={onVideoError}
      isPaidVideo={isPaidVideo}
      hasAccess={hasAccess}
      previewDuration={previewDuration}
      onPreviewEnd={onPreviewEnd}
      shouldPause={shouldPause}
    />
  );
};

export default ShortVideoPlayer;
