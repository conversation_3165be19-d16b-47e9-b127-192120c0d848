import React, { useState, useEffect } from 'react';
import { Setting<PERSON>, Shield, ShieldOff, Eye, EyeOff } from 'lucide-react';
import EncryptionControlPanel from './EncryptionControlPanel';
import { useEncryptionStore } from '../store/encryptionStore';

const EncryptionFloatingButton: React.FC = () => {
  const [showPanel, setShowPanel] = useState(false);
  const [isVisible, setIsVisible] = useState(false);
  const { isEncryptionEnabled, setEncryption } = useEncryptionStore();

  useEffect(() => {
    // 检查是否启用了开发者工具
    const checkDevTools = () => {
      const devToolsEnabled = (window as any).devTools === true;
      setIsVisible(devToolsEnabled);
    };

    checkDevTools();

    // 监听 devTools 变量变化
    const interval = setInterval(checkDevTools, 1000);

    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    // 监听加密状态变化事件
    const handleEncryptionToggled = (event: CustomEvent) => {
      console.log('🔐 收到加密状态变化事件:', event.detail);
    };

    window.addEventListener('encryptionToggled', handleEncryptionToggled as EventListener);

    return () => {
      window.removeEventListener('encryptionToggled', handleEncryptionToggled as EventListener);
    };
  }, []);

  if (!isVisible) {
    return null;
  }

  return (
    <>
      {/* 浮动按钮 */}
      <div className="fixed bottom-20 right-4 z-50">
        <button
          onClick={() => setShowPanel(!showPanel)}
          className={`
            w-14 h-14 rounded-full shadow-lg flex items-center justify-center
            transition-all duration-300 hover:scale-110
            ${isEncryptionEnabled 
              ? 'bg-green-500 hover:bg-green-600' 
              : 'bg-red-500 hover:bg-red-600'
            }
          `}
          title={`加密状态: ${isEncryptionEnabled ? '已启用' : '已禁用'}`}
        >
          {isEncryptionEnabled ? (
            <Shield className="w-6 h-6 text-white" />
          ) : (
            <ShieldOff className="w-6 h-6 text-white" />
          )}
        </button>
        
        {/* 状态指示器 */}
        <div className="absolute -top-2 -right-2">
          <div className={`
            w-4 h-4 rounded-full border-2 border-white
            ${isEncryptionEnabled ? 'bg-green-400' : 'bg-red-400'}
          `} />
        </div>
      </div>

      {/* 控制面板 */}
      {showPanel && (
        <EncryptionControlPanel
          onClose={() => setShowPanel(false)}
          isEncryptionEnabled={isEncryptionEnabled}
          onEncryptionToggle={setEncryption}
        />
      )}
    </>
  );
};

export default EncryptionFloatingButton;
