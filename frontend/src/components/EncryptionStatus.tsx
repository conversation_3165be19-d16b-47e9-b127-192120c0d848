// 加密状态组件
import React, { useState, useEffect } from 'react';
import { Shield, ShieldCheck, ShieldX, RefreshCw, Eye, EyeOff, AlertTriangle } from 'lucide-react';
import { encryptedApi } from '../services/encryptedApi';

interface EncryptionStatusProps {
  showDetails?: boolean;
  className?: string;
}

const EncryptionStatus: React.FC<EncryptionStatusProps> = ({ 
  showDetails = false, 
  className = '' 
}) => {
  const [status, setStatus] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [showStatusPanel, setShowStatusPanel] = useState(false);
  const [testResult, setTestResult] = useState<boolean | null>(null);

  // 获取加密状态
  const fetchStatus = async () => {
    try {
      const encryptionStatus = encryptedApi.getEncryptionInfo();
      setStatus(encryptionStatus);
    } catch (error) {
      console.error('获取加密状态失败:', error);
    }
  };

  // 刷新密钥
  const handleRefreshKey = async () => {
    setLoading(true);
    try {
      const success = await encryptedApi.refreshKey();
      if (success) {
        await fetchStatus();
        setTestResult(null); // 重置测试结果
      }
    } catch (error) {
      console.error('刷新密钥失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 测试加密连接
  const handleTestEncryption = async () => {
    setLoading(true);
    try {
      const result = await encryptedApi.testEncryption();
      setTestResult(result);
    } catch (error) {
      console.error('Encryption failed:', error);
      setTestResult(false);
    } finally {
      setLoading(false);
    }
  };

  // 切换加密开关
  const handleToggleEncryption = () => {
    const newState = !status?.isEnabled;
    encryptedApi.setEncryptionEnabled(newState);
    setStatus({ ...status, isEnabled: newState });
  };

  // 格式化时间
  const formatTime = (ms: number) => {
    if (ms < 1000) return `${ms}ms`;
    if (ms < 60000) return `${Math.round(ms / 1000)}s`;
    return `${Math.round(ms / 60000)}m`;
  };

  // 获取状态图标和颜色
  const getStatusIcon = () => {
    if (!status) return <Shield className="w-4 h-4 text-gray-400" />;
    
    if (!status.isEnabled) {
      return <ShieldX className="w-4 h-4 text-red-500" />;
    }
    
    if (status.hasSessionKey && !status.needsRefresh) {
      return <ShieldCheck className="w-4 h-4 text-green-500" />;
    }
    
    return <AlertTriangle className="w-4 h-4 text-yellow-500" />;
  };

  const getStatusText = () => {
    if (!status) return '未知';
    
    if (!status.isEnabled) return '已禁用';
    if (!status.hasSessionKey) return '未初始化';
    if (status.needsRefresh) return '需要刷新';
    return '正常';
  };

  const getStatusColor = () => {
    if (!status || !status.isEnabled) return 'text-red-500';
    if (!status.hasSessionKey || status.needsRefresh) return 'text-yellow-500';
    return 'text-green-500';
  };

  useEffect(() => {
    fetchStatus();
    
    // 定期更新状态
    const interval = setInterval(fetchStatus, 30000); // 30秒更新一次
    
    return () => clearInterval(interval);
  }, []);

  if (!showDetails) {
    // 简单状态显示
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        {getStatusIcon()}
        <span className={`text-sm ${getStatusColor()}`}>
          {getStatusText()}
        </span>
      </div>
    );
  }

  return (
    <div className={`relative ${className}`}>
      {/* 状态指示器 */}
      <button
        onClick={() => setShowStatusPanel(!showStatusPanel)}
        className="flex items-center space-x-2 px-3 py-2 rounded-lg bg-gray-100 hover:bg-gray-200 transition-colors"
      >
        {getStatusIcon()}
        <span className={`text-sm font-medium ${getStatusColor()}`}>
          加密状态: {getStatusText()}
        </span>
        {showStatusPanel ? (
          <EyeOff className="w-4 h-4 text-gray-400" />
        ) : (
          <Eye className="w-4 h-4 text-gray-400" />
        )}
      </button>

      {/* 详细状态面板 */}
      {showStatusPanel && (
        <div className="absolute top-full left-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 p-4 z-50">
          <div className="space-y-4">
            {/* 标题 */}
            <div className="flex items-center justify-between">
              <h3 className="font-medium text-gray-900">🔐 加密状态详情</h3>
              <button
                onClick={() => setShowStatusPanel(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                ✕
              </button>
            </div>

            {/* 状态信息 */}
            {status && (
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">加密功能:</span>
                  <div className="flex items-center space-x-2">
                    <span className={`text-sm font-medium ${status.isEnabled ? 'text-green-600' : 'text-red-600'}`}>
                      {status.isEnabled ? '已启用' : '已禁用'}
                    </span>
                    <button
                      onClick={handleToggleEncryption}
                      className={`w-8 h-4 rounded-full transition-colors ${
                        status.isEnabled ? 'bg-green-500' : 'bg-gray-300'
                      }`}
                    >
                      <div className={`w-3 h-3 bg-white rounded-full transition-transform ${
                        status.isEnabled ? 'translate-x-4' : 'translate-x-0.5'
                      }`} />
                    </button>
                  </div>
                </div>

                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">会话密钥:</span>
                  <span className={`text-sm ${status.hasSessionKey ? 'text-green-600' : 'text-red-600'}`}>
                    {status.hasSessionKey ? '已建立' : '未建立'}
                  </span>
                </div>

                {status.hasSessionKey && (
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">密钥年龄:</span>
                    <span className="text-sm text-gray-900">
                      {formatTime(status.keyAge)}
                    </span>
                  </div>
                )}

                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">需要刷新:</span>
                  <span className={`text-sm ${status.needsRefresh ? 'text-yellow-600' : 'text-green-600'}`}>
                    {status.needsRefresh ? '是' : '否'}
                  </span>
                </div>

                {status.retryCount > 0 && (
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">重试次数:</span>
                    <span className="text-sm text-yellow-600">
                      {status.retryCount}
                    </span>
                  </div>
                )}
              </div>
            )}

            {/* 测试结果 */}
            {testResult !== null && (
              <div className={`p-3 rounded-lg ${
                testResult ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'
              }`}>
                <div className="flex items-center space-x-2">
                  {testResult ? (
                    <ShieldCheck className="w-4 h-4 text-green-600" />
                  ) : (
                    <ShieldX className="w-4 h-4 text-red-600" />
                  )}
                  <span className={`text-sm font-medium ${
                    testResult ? 'text-green-800' : 'text-red-800'
                  }`}>
                    加密测试{testResult ? '成功' : '失败'}
                  </span>
                </div>
              </div>
            )}

            {/* 操作按钮 */}
            <div className="flex space-x-2">
              <button
                onClick={handleRefreshKey}
                disabled={loading}
                className="flex-1 flex items-center justify-center space-x-2 px-3 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
                <span className="text-sm">刷新密钥</span>
              </button>

              <button
                onClick={handleTestEncryption}
                disabled={loading}
                className="flex-1 flex items-center justify-center space-x-2 px-3 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                <Shield className="w-4 h-4" />
                <span className="text-sm">测试加密</span>
              </button>
            </div>

            {/* 安全提示 */}
            <div className="text-xs text-gray-500 bg-gray-50 p-2 rounded">
              💡 加密功能保护您的敏感数据传输，建议保持启用状态
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default EncryptionStatus;
