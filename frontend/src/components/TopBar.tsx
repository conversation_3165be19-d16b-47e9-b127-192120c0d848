import React from 'react';
import { useNavigate } from 'react-router-dom';

interface TopBarProps {
  title?: string;
  showBack?: boolean;
  showSearch?: boolean;
  rightContent?: React.ReactNode;
}

const TopBar: React.FC<TopBarProps> = ({
  title,
  showBack = false,
  showSearch = false,
  rightContent
}) => {
  const navigate = useNavigate();

  const handleBack = () => {
    navigate(-1);
  };

  const handleSearch = () => {
    navigate('/discover');
  };

  return (
    <div className="fixed top-0 left-0 right-0 bg-white border-b border-gray-100 navbar-safe-top z-50 pl-3 pr-3">
      <div className="mx-auto max-w-full md:max-w-[768px] xl:max-w-[1024px] 2xl:max-w-[1200px] md:shadow-lg 2xl:shadow-xl">
        <div className="grid grid-cols-3 items-center h-14 md:h-16 px-4 md:px-8 lg:px-12 gap-4 safe-area-left safe-area-right">
        {/* 左侧 */}
        <div className="flex items-center justify-start">
          {showBack ? (
            <button
              onClick={handleBack}
              className="p-2 -ml-2 text-gray-600 active:text-gray-800 active:bg-gray-100 rounded-lg touch-button"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>
          ) : (
            <div className="flex items-center">
              <div className="text-lg md:text-xl font-bold text-pink-500 mr-2">📺</div>
              <span className="text-base md:text-lg font-bold text-gray-800">赫本</span>
            </div>
          )}
        </div>

        {/* 中间标题 - 使用grid中间列，确保正确截断 */}
        <div className="flex justify-center items-center min-w-0">
          {title && (
            <h1
              className="text-base md:text-lg font-semibold text-gray-800 truncate text-center w-full"
              title={title}
            >
              {title}
            </h1>
          )}
        </div>

        {/* 右侧 */}
        <div className="flex items-center justify-end space-x-1">
          {showSearch && (
            <button
              onClick={handleSearch}
              className="p-2 text-gray-600 active:text-gray-800 active:bg-gray-100 rounded-lg touch-button"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </button>
          )}

          {rightContent}
        </div>
        </div>
      </div>
    </div>
  );
};

export default TopBar;
