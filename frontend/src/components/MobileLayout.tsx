import React from 'react';
import TopBar from './TopBar';
import TabBar from './TabBar';

interface MobileLayoutProps {
  children: React.ReactNode;
  title?: string;
  showBack?: boolean;
  showSearch?: boolean;
  showTabBar?: boolean;
  rightContent?: React.ReactNode;
  className?: string;
  fullscreen?: boolean; // 全屏模式，不添加padding
  hideTopBar?: boolean; // 隐藏顶部导航栏
  darkTabBar?: boolean; // 深色底部Tab栏
}

const MobileLayout: React.FC<MobileLayoutProps> = ({
  children,
  title,
  showBack = false,
  showSearch = false,
  showTabBar = true,
  rightContent,
  className = '',
  fullscreen = false,
  hideTopBar = false,
  darkTabBar = false
}) => {
  return (
    <div className="mobile-container h-screen">
      {/* 顶部导航栏 */}
      {!hideTopBar && (
        <TopBar
          title={title}
          showBack={showBack}
          showSearch={showSearch}
          rightContent={rightContent}
        />
      )}

      {/* 主要内容区域 */}
      <main className={`mobile-content ${className}`}>
        {fullscreen ? (
          // 全屏模式：不添加padding，让内容占满整个屏幕
          <div className="h-screen">
            {children}
          </div>
        ) : (
          // 普通模式：添加padding避免被导航栏遮挡，正确计算内容高度
          <div className={`bg-gray-50 ${hideTopBar ? 'safe-area-inset-top' : 'content-safe-top'} ${showTabBar ? 'pb-16 md:pb-20 content-min-height-with-tab' : 'pb-4 md:pb-6 content-min-height'}`}>
            {children}
          </div>
        )}
      </main>

      {/* 底部Tab栏 */}
      {showTabBar && <TabBar dark={darkTabBar} />}
    </div>
  );
};

export default MobileLayout;
