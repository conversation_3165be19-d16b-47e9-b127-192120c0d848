import React, { useRef, useEffect, useState, useCallback } from 'react';
import Hls from 'hls.js';
import { processVideoUrl } from '../utils/videoUrl';
import './VideoPlayer.css';

interface HLSShortVideoPlayerProps {
  video: {
    id: number;
    title: string;
    videoUrl: string;
    price?: number;
  };
  isActive?: boolean;
  hasAccess?: boolean;
  isPaidVideo?: boolean;
  previewDuration?: number;
  onPreviewEnd?: () => void;
  onVideoError?: (error: string) => void;
  shouldPause?: boolean;
}

const HLSShortVideoPlayer: React.FC<HLSShortVideoPlayerProps> = ({
  video,
  isActive = true,
  hasAccess = true,
  isPaidVideo = false,
  previewDuration = 60,
  onPreviewEnd,
  onVideoError,
  shouldPause = false
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const hlsRef = useRef<Hls | null>(null);
  const clickTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastTouchTimeRef = useRef<number>(0);
  const touchCountRef = useRef<number>(0);

  const [isLoading, setIsLoading] = useState(true);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isBuffering, setIsBuffering] = useState(false);
  const [duration, setDuration] = useState(0);
  const [currentTime, setCurrentTime] = useState(0);
  const [isDragging, setIsDragging] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasShownPurchaseModal, setHasShownPurchaseModal] = useState(false);


  // 初始化HLS播放器
  const initializeHLS = useCallback(() => {
    if (!videoRef.current || !video.videoUrl) return;

    const videoElement = videoRef.current;
    const processedVideoUrl = processVideoUrl(video.videoUrl);

    // 清理现有的HLS实例
    if (hlsRef.current) {
      hlsRef.current.destroy();
      hlsRef.current = null;
    }

    // 检查是否为HLS流
    if (processedVideoUrl.includes('.m3u8')) {
      if (Hls.isSupported()) {
        const hls = new Hls({
          enableWorker: true,
          lowLatencyMode: true,
          backBufferLength: 30,
          maxBufferLength: 10,
          maxMaxBufferLength: 30,
          maxBufferSize: 20 * 1000 * 1000,
          manifestLoadingTimeOut: 5000,
          manifestLoadingMaxRetry: 2,
          manifestLoadingRetryDelay: 200,
          fragLoadingTimeOut: 10000,
          fragLoadingMaxRetry: 3,
          fragLoadingRetryDelay: 300,
          levelLoadingTimeOut: 5000,
          levelLoadingMaxRetry: 2,
          levelLoadingRetryDelay: 500,
          startLevel: -1,
          capLevelToPlayerSize: true,
          debug: false
        });

        hlsRef.current = hls;

        hls.on(Hls.Events.MANIFEST_PARSED, () => {
          setIsLoading(false);
        });

        hls.on(Hls.Events.ERROR, (event, data) => {
          if (data.fatal) {
            setError('视频加载失败');
            if (onVideoError) {
              onVideoError('HLS播放错误');
            }
          }
        });

        hls.loadSource(processedVideoUrl);
        hls.attachMedia(videoElement);

      } else if (videoElement.canPlayType('application/vnd.apple.mpegurl')) {
        videoElement.src = processedVideoUrl;
        setIsLoading(false);
      } else {
        setError('不支持HLS播放');
      }
    } else {
      videoElement.src = processedVideoUrl;
      setIsLoading(false);
    }
  }, [video.videoUrl, onVideoError]);

  // 初始化播放器
  useEffect(() => {
    initializeHLS();

    return () => {
      if (hlsRef.current) {
        hlsRef.current.destroy();
        hlsRef.current = null;
      }
    };
  }, [initializeHLS]);

  // 视频事件处理
  useEffect(() => {
    const videoElement = videoRef.current;
    if (!videoElement) return;

    const handleLoadedMetadata = () => {
      setDuration(videoElement.duration);
      setIsLoading(false);
    };

    const handleTimeUpdate = () => {
      if (!isDragging) {
        setCurrentTime(videoElement.currentTime);
      }

      // 预览时间限制
      if (isPaidVideo && !hasAccess && previewDuration > 0) {
        if (videoElement.currentTime >= previewDuration) {
          videoElement.pause();
          if (onPreviewEnd) {
            onPreviewEnd();
          }
        }
      }
    };

    const handlePlay = () => {
      setIsPlaying(true);
      setIsBuffering(false);
    };

    const handlePause = () => {
      setIsPlaying(false);
    };

    const handleWaiting = () => {
      setIsBuffering(true);
    };

    const handleCanPlay = () => {
      setIsBuffering(false);
    };

    const handleEnded = () => {
      setIsPlaying(false);

      // 如果显示过购买弹框，不自动循环播放
      if (hasShownPurchaseModal) {
        return;
      }

      // 短视频循环播放
      videoElement.currentTime = 0;
      videoElement.play().catch(console.error);
    };

    videoElement.addEventListener('loadedmetadata', handleLoadedMetadata);
    videoElement.addEventListener('timeupdate', handleTimeUpdate);
    videoElement.addEventListener('play', handlePlay);
    videoElement.addEventListener('pause', handlePause);
    videoElement.addEventListener('ended', handleEnded);
    videoElement.addEventListener('waiting', handleWaiting);
    videoElement.addEventListener('canplay', handleCanPlay);

    return () => {
      videoElement.removeEventListener('loadedmetadata', handleLoadedMetadata);
      videoElement.removeEventListener('timeupdate', handleTimeUpdate);
      videoElement.removeEventListener('play', handlePlay);
      videoElement.removeEventListener('pause', handlePause);
      videoElement.removeEventListener('ended', handleEnded);
      videoElement.removeEventListener('waiting', handleWaiting);
      videoElement.removeEventListener('canplay', handleCanPlay);
    };
  }, [isPaidVideo, hasAccess, previewDuration, isDragging, hasShownPurchaseModal, onPreviewEnd]);

  // 播放状态控制
  useEffect(() => {
    const videoElement = videoRef.current;
    if (!videoElement || isLoading) return;

    if (shouldPause && !videoElement.paused) {
      // 外部要求暂停
      videoElement.pause();
      setHasShownPurchaseModal(true);
      return;
    }


  }, [isLoading, shouldPause]);

  useEffect(()=>{
    const videoElement = videoRef.current;
    if (!videoElement) return;
    if (!isActive) {
      // 非活跃视频暂停
      videoElement.pause?.();
    } 
  },[isActive])

  // 切换播放状态
  const togglePlayback = useCallback(() => {
    const videoElement = videoRef.current;
    if (!videoElement) return;

    // 检查预览限制
    if (isPaidVideo && !hasAccess && previewDuration > 0) {
      if (videoElement.currentTime >= previewDuration) {
        if (onPreviewEnd) {
          onPreviewEnd();
        }
        return;
      }
    }

    if (videoElement.paused) {
      videoElement.play().catch(console.error);
      if (hasShownPurchaseModal) {
        setHasShownPurchaseModal(false);
      }
    } else {
      videoElement.pause();
    }
  }, [isPaidVideo, hasAccess, previewDuration, onPreviewEnd, hasShownPurchaseModal]);

  // 点击处理
  const handleVideoClick = useCallback(() => {
    if (clickTimeoutRef.current) {
      clearTimeout(clickTimeoutRef.current);
    }

    clickTimeoutRef.current = setTimeout(() => {
      togglePlayback();
    }, 150);
  }, [togglePlayback]);

  // 触摸处理
  const handleVideoTouchStart = useCallback((e: React.TouchEvent) => {
    const target = e.target as HTMLElement;
    if (target.tagName === 'INPUT' || target.closest('[data-progress-bar]')) {
      return;
    }

    e.preventDefault();

    const currentTime = Date.now();
    const timeDiff = currentTime - lastTouchTimeRef.current;

    if (timeDiff < 250 && timeDiff > 0) {
      touchCountRef.current += 1;

      if (touchCountRef.current === 2) {
        if (clickTimeoutRef.current) {
          clearTimeout(clickTimeoutRef.current);
        }

        const videoElement = videoRef.current;
        if (videoElement && !videoElement.paused) {
          videoElement.pause();
        }

        touchCountRef.current = 0;
        return;
      }
    } else {
      touchCountRef.current = 1;
    }

    lastTouchTimeRef.current = currentTime;

    if (clickTimeoutRef.current) {
      clearTimeout(clickTimeoutRef.current);
    }

    clickTimeoutRef.current = setTimeout(() => {
      if (touchCountRef.current === 1) {
        togglePlayback();
      }
      touchCountRef.current = 0;
    }, 200);
  }, [togglePlayback]);

  // 进度条处理
  const handleProgressChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const videoElement = videoRef.current;
    if (!videoElement || !duration) return;

    const newTime = (parseFloat(e.target.value) / 100) * duration;

    // 检查预览限制
    if (isPaidVideo && !hasAccess && previewDuration > 0) {
      if (newTime > previewDuration) {
        return;
      }
    }

    setCurrentTime(newTime);
    videoElement.currentTime = newTime;
  }, [duration, isPaidVideo, hasAccess, previewDuration]);

  const handleProgressStart = useCallback(() => {
    setIsDragging(true);
  }, []);

  const handleProgressEnd = useCallback(() => {
    setIsDragging(false);
  }, []);

  if (error) {
    return (
      <div className="flex items-center justify-center h-full bg-gray-900 text-white">
        <div className="text-center">
          <div className="text-red-500 text-2xl mb-2">⚠️</div>
          <div className="text-sm">{error}</div>
        </div>
      </div>
    );
  }

  return (
    <div className="relative w-full h-full bg-black">
      {/* Loading状态 */}
      {(isLoading || isBuffering) && (
        <div className="absolute inset-0 flex items-center justify-center z-25 pointer-events-none">
          <div className="relative w-8 h-8">
            <div className="absolute inset-0 border-2 border-white border-opacity-30 rounded-full"></div>
            <div className="absolute inset-0 border-2 border-transparent border-t-pink-500 rounded-full animate-spin"></div>
          </div>
        </div>
      )}

      <video
        ref={videoRef}
        className="w-full h-full object-contain cursor-pointer"
        controls={false}
        playsInline
        webkit-playsinline="true"
        x-webkit-airplay="allow"
        preload="metadata"
        disablePictureInPicture
        controlsList="nodownload nofullscreen noremoteplayback"
        onClick={handleVideoClick}
        onTouchStart={handleVideoTouchStart}
        style={{
          objectFit: 'contain',
          backgroundColor: '#000'
        }}
      />

      {/* 播放按钮覆盖层 */}
      {!isLoading && !isBuffering && !isPlaying && (
        <div className="absolute inset-0 flex items-center justify-center pointer-events-none z-20">
          <div className="w-16 h-16 bg-black bg-opacity-50 rounded-full flex items-center justify-center text-white text-3xl">
            <svg className="w-8 h-8 ml-1" fill="currentColor" viewBox="0 0 24 24">
              <path d="M8 5v14l11-7z" />
            </svg>
          </div>
        </div>
      )}

      {/* 进度条 */}
      <div className="absolute bottom-24 left-0 right-0 z-30 px-3" data-progress-bar>
        <div className="relative">
          <div className={`relative transition-all duration-300 ${isDragging ? 'h-2' : 'h-1'}`}>
            <div className="absolute inset-0 bg-white bg-opacity-25 rounded-full backdrop-blur-sm"></div>
            <div
              className={`absolute inset-0 bg-gradient-to-r from-pink-400 via-pink-500 to-purple-500 rounded-full transition-all duration-150 ${
                isDragging ? 'shadow-lg shadow-pink-500/30' : ''
              }`}
              style={{ width: `${duration > 0 ? (currentTime / duration) * 100 : 0}%` }}
            >
              <div className={`absolute right-0 top-1/2 transform translate-x-1/2 -translate-y-1/2 bg-white rounded-full shadow-lg border-2 border-pink-500 transition-all duration-200 ${
                isDragging ? 'w-5 h-5 scale-110' : 'w-4 h-4'
              }`}>
                <div className="absolute inset-1 bg-gradient-to-br from-pink-400 to-purple-500 rounded-full"></div>
              </div>
            </div>
          </div>

          {isDragging && duration > 0 && (
            <div className="absolute -top-8 left-0 right-0 flex justify-between text-white text-xs font-medium">
              <span>
                {Math.floor(currentTime / 60)}:{String(Math.floor(currentTime % 60)).padStart(2, '0')}
              </span>
              <span>
                {Math.floor(duration / 60)}:{String(Math.floor(duration % 60)).padStart(2, '0')}
              </span>
            </div>
          )}

          <input
            type="range"
            min="0"
            max="100"
            value={duration > 0 ? (currentTime / duration) * 100 : 0}
            onChange={handleProgressChange}
            onMouseDown={handleProgressStart}
            onMouseUp={handleProgressEnd}
            onTouchStart={handleProgressStart}
            onTouchEnd={handleProgressEnd}
            className="absolute inset-0 w-full h-10 opacity-0 cursor-pointer"
            style={{ marginTop: '-18px' }}
          />
        </div>
      </div>
    </div>
  );
};

export default HLSShortVideoPlayer;
