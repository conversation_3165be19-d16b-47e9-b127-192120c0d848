@tailwind base;
@tailwind components;
@tailwind utilities;

/* Video.js 自定义样式 */
.video-js {
  font-family: inherit;
  width: 100% !important;
  height: 100% !important;
  max-width: 100% !important;
  max-height: 100% !important;
}

.video-js video {
  width: 100% !important;
  height: 100% !important;
  max-width: 100% !important;
  max-height: 100% !important;
  object-fit: contain !important;
  display: block !important;
}

.video-js .vjs-tech {
  width: 100% !important;
  height: 100% !important;
  max-width: 100% !important;
  max-height: 100% !important;
  object-fit: contain !important;
}

.video-js .vjs-big-play-button {
  background-color: rgba(0, 0, 0, 0.5);
  border: none;
  border-radius: 50%;
  width: 3em;
  height: 3em;
  line-height: 3em;
  font-size: 1.5em;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.video-js .vjs-big-play-button:hover {
  background-color: rgba(0, 0, 0, 0.7);
}

/* 短视频模式下隐藏控制栏 */
.shorts-player .video-js .vjs-control-bar {
  display: none !important;
}

/* 隐藏Video.js默认控制栏 */
.video-js .vjs-control-bar {
  display: none !important;
}

/* 短视频模式下隐藏控制栏 */
.shorts-player .video-js .vjs-control-bar {
  display: none !important;
}

/* HLS加载状态样式 */
.video-js .vjs-loading-spinner {
  border-color: #ec4899 transparent transparent transparent;
}

/* 确保视频容器正确显示 */
.video-js .vjs-poster {
  background-size: contain !important;
  background-position: center !important;
  background-repeat: no-repeat !important;
}

/* 响应式字体适配 */
@layer base {
  html {
    font-size: 14px; /* 移动端使用更小的基础字体 */
    -webkit-text-size-adjust: 100%;
    -webkit-tap-highlight-color: transparent;
    height: 100%;
    width: 100%;
  }

  /* 小平板字体适配 */
  @media (min-width: 768px) and (max-width: 1023px) {
    html {
      font-size: 15px; /* 小平板使用稍大的字体 */
    }
  }

  /* 大平板字体适配 (iPad Pro) */
  @media (min-width: 1024px) and (max-width: 1366px) {
    html {
      font-size: 16px; /* 大平板使用标准字体 */
    }
  }

  /* 桌面端字体适配 */
  @media (min-width: 1367px) {
    html {
      font-size: 16px; /* 桌面端使用标准字体 */
    }
  }

  body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
      'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
      sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    background-color: #f5f5f5;
    overflow-x: hidden;
    margin: 0;
    padding: 0;
    height: 100%;
    width: 100%;
    position: relative;
  }

  #root {
    height: 100%;
    width: 100%;
    position: relative;
  }

  * {
    box-sizing: border-box;
    /* 全局消除iOS点击闪烁 */
    -webkit-tap-highlight-color: transparent;
  }

  /* 所有按钮元素的触摸优化 */
  button,
  [role="button"],
  .btn,
  .mobile-btn,
  input[type="button"],
  input[type="submit"],
  input[type="reset"] {
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
    touch-action: manipulation;
    outline: none;
  }

  /* 所有链接元素的触摸优化 */
  a,
  [role="link"] {
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    text-decoration: none;
    outline: none;
  }

  /* 可点击元素的触摸优化 */
  [onclick],
  .cursor-pointer,
  .clickable {
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
    touch-action: manipulation;
  }
}

/* iOS刘海屏安全区域适配 */
@layer utilities {
  /* 基础安全区域类 */
  .safe-area-top {
    padding-top: env(safe-area-inset-top);
  }

  .safe-area-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .safe-area-left {
    padding-left: env(safe-area-inset-left);
  }

  .safe-area-right {
    padding-right: env(safe-area-inset-right);
  }

  /* 增强的安全区域适配 */
  .safe-area-inset-top {
    padding-top: max(env(safe-area-inset-top), 20px);
  }

  .safe-area-inset-bottom {
    padding-bottom: max(env(safe-area-inset-bottom), 20px);
  }

  .safe-area-inset-left {
    padding-left: max(env(safe-area-inset-left), 0px);
  }

  .safe-area-inset-right {
    padding-right: max(env(safe-area-inset-right), 0px);
  }

  /* 顶部导航栏专用安全区域 */
  .navbar-safe-top {
    padding-top: max(env(safe-area-inset-top), 0px);
    min-height: calc(56px + env(safe-area-inset-top));
  }

  /* 平板导航栏高度适配 */
  @media (min-width: 768px) {
    .navbar-safe-top {
      min-height: calc(64px + env(safe-area-inset-top));
    }
  }

  /* 内容区域安全区域适配 - 只保留顶部 */
  .content-safe-top {
    padding-top: calc(56px + env(safe-area-inset-top));
  }

  /* 平板内容区域适配 */
  @media (min-width: 768px) {
    .content-safe-top {
      padding-top: calc(64px + env(safe-area-inset-top));
    }
  }

  /* 内容区域最小高度 - 减去顶部导航栏高度 */
  .content-min-height {
    min-height: calc(100vh - env(safe-area-inset-top));
  }

  /* 平板内容最小高度适配 */
  @media (min-width: 768px) {
    .content-min-height {
      min-height: calc(100vh - env(safe-area-inset-top));
    }
  }

  /* 带底部Tab栏的内容区域最小高度 */
  .content-min-height-with-tab {
    min-height: calc(100vh - 56px - env(safe-area-inset-top) - 64px);
  }

  /* 平板带Tab栏的内容高度适配 */
  @media (min-width: 768px) {
    .content-min-height-with-tab {
      min-height: calc(100vh - 64px - env(safe-area-inset-top) - 80px);
    }
  }

  /* 全屏内容安全区域 */
  .fullscreen-safe {
    padding-top: env(safe-area-inset-top);
    padding-bottom: env(safe-area-inset-bottom);
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }
}

/* 移动端视频卡片样式 */
@layer components {
  .mobile-video-card {
    @apply bg-white rounded-xl overflow-hidden shadow-sm mb-4 mx-3;
    min-height: 200px;
  }

  /* 平板视频卡片适配 */
  @media (min-width: 768px) {
    .mobile-video-card {
      @apply mx-4 mb-6;
      min-height: 240px;
    }
  }

  .mobile-video-thumbnail {
    @apply w-full aspect-video object-cover;
    height: 180px;
  }

  /* 平板视频缩略图适配 */
  @media (min-width: 768px) {
    .mobile-video-thumbnail {
      height: 220px;
    }
  }

  .mobile-video-info {
    @apply p-3;
  }

  .mobile-video-title {
    @apply text-sm font-medium text-gray-900 line-clamp-2 mb-2 leading-5;
    min-height: 40px;
  }

  .mobile-video-meta {
    @apply flex items-center justify-between text-xs text-gray-500;
  }

  /* 响应式布局容器 */
  .mobile-container {
    @apply w-full mx-auto bg-white min-h-screen;
    /* 移动端: 全宽 */
    max-width: 100%;
  }

  /* 小平板适配 (iPad mini, 普通iPad) */
  @media (min-width: 768px) and (max-width: 1023px) {
    .mobile-container {
      max-width: 768px;
      @apply shadow-lg;
    }
  }

  /* 大平板适配 (iPad Pro, 大屏平板) */
  @media (min-width: 1024px) and (max-width: 1366px) {
    .mobile-container {
      max-width: 1024px;
      @apply shadow-lg;
    }
  }

  /* 桌面端适配 */
  @media (min-width: 1367px) {
    .mobile-container {
      max-width: 1200px;
      @apply shadow-xl;
    }
  }

  .mobile-content {
    @apply px-0 py-0;
  }

  .mobile-section {
    @apply mb-4;
  }

  .mobile-section-title {
    @apply text-lg font-semibold text-gray-900 px-4 py-3 bg-white sticky top-14 z-10;
  }

  /* 加载动画 */
  .loading-spinner {
    @apply inline-block w-4 h-4 border-2 border-gray-300 border-t-pink-500 rounded-full animate-spin;
  }

  /* 文本截断 */
  .line-clamp-1 {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* 响应式按钮 */
  .mobile-btn {
    @apply px-4 py-3 rounded-xl font-medium text-sm transition-all duration-200 active:scale-95;
    min-height: 44px;
  }

  /* 平板按钮适配 */
  @media (min-width: 768px) {
    .mobile-btn {
      @apply px-6 py-4 text-base;
      min-height: 48px;
    }
  }

  .mobile-btn-primary {
    @apply mobile-btn bg-pink-500 text-white active:bg-pink-600;
  }

  .mobile-btn-secondary {
    @apply mobile-btn bg-gray-100 text-gray-700 active:bg-gray-200;
  }

  /* 移动端专用工具类 */
  .active\:scale-98:active {
    transform: scale(0.98);
  }

  .active\:scale-95:active {
    transform: scale(0.95);
  }

  /* 隐藏滚动条 */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* iOS触摸优化 - 消除点击闪烁 */
  .ios-touch-optimized {
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    touch-action: manipulation;
  }

  /* 移动端触摸优化 */
  .touch-manipulation {
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
  }

  /* 按钮和可点击元素的触摸优化 */
  .touch-button {
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
    touch-action: manipulation;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    outline: none;
    border: none;
    background: none;
    cursor: pointer;
  }

  /* 链接的触摸优化 */
  .touch-link {
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    text-decoration: none;
    outline: none;
  }

  /* 移动端安全区域 */
  @supports (padding: max(0px)) {
    .safe-area-inset-top {
      padding-top: max(env(safe-area-inset-top), 0px);
    }

    .safe-area-inset-bottom {
      padding-bottom: max(env(safe-area-inset-bottom), 0px);
    }
  }

  /* 图片渐入效果 */
  .image-fade-in {
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
  }

  .image-fade-in.loaded {
    opacity: 1;
  }

  /* 底部弹窗滑入动画 */
  .animate-slide-up {
    animation: slideUp 0.3s ease-out forwards;
  }

  /* 底部弹窗滑出动画 */
  .animate-slide-down {
    animation: slideDown 0.3s ease-in forwards;
  }

  @keyframes slideUp {
    from {
      transform: translateY(100%);
    }
    to {
      transform: translateY(0);
    }
  }

  @keyframes slideDown {
    from {
      transform: translateY(0);
    }
    to {
      transform: translateY(100%);
    }
  }

  /* 拖拽时禁用动画 */
  .draggable-modal.dragging {
    animation: none !important;
    transition: none !important;
  }

  /* 拖拽相关样式 */
  .draggable-modal {
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    transform: translateY(100%); /* 默认隐藏在底部 */
  }

  /* 弹框可见状态 */
  .draggable-modal.modal-visible {
    transform: translateY(0) !important;
  }

  /* 确保动画正常工作 */
  .draggable-modal.animate-slide-up {
    animation: slideUp 0.3s ease-out forwards !important;
  }

  .draggable-modal.animate-slide-down {
    animation: slideDown 0.3s ease-in forwards !important;
  }

  .draggable-modal.dragging {
    transition: none !important;
  }

  .draggable-modal.dragging .drag-handle {
    pointer-events: none;
  }

  /* 确保按钮在拖拽时仍可点击 */
  .draggable-modal button {
    pointer-events: auto !important;
  }

  .drag-handle {
    touch-action: none;
    cursor: grab;
  }

  .drag-handle:active {
    cursor: grabbing;
  }
}

@layer base {
  * {
    box-sizing: border-box;
  }

  body {
    margin: 0;
    font-family: 'Inter', system-ui, -apple-system, sans-serif;
    line-height: 1.5;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  html {
    scroll-behavior: smooth;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none;
  }

  .btn-primary {
    @apply btn bg-blue-600 text-white hover:bg-blue-700 h-10 py-2 px-4;
  }

  .btn-secondary {
    @apply btn bg-gray-200 text-gray-900 hover:bg-gray-300 h-10 py-2 px-4;
  }

  .btn-ghost {
    @apply btn hover:bg-gray-100 h-10 py-2 px-4;
  }

  .input {
    @apply flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }

  .card {
    @apply rounded-lg border border-gray-200 bg-white shadow-sm;
  }

  .video-card {
    @apply card overflow-hidden transition-transform hover:scale-105 cursor-pointer;
  }

  .video-thumbnail {
    @apply w-full h-48 object-cover;
  }

  .video-info {
    @apply p-4;
  }

  .video-title {
    @apply font-semibold text-gray-900 line-clamp-2 mb-2;
  }

  .video-meta {
    @apply text-sm text-gray-600;
  }

  .container {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  .section {
    @apply py-8;
  }

  .section-title {
    @apply text-2xl font-bold text-gray-900 mb-6;
  }

  .grid-videos {
    @apply grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6;
  }

  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .loading-spinner {
    @apply w-6 h-6 border-2 border-gray-300 border-t-blue-600 rounded-full animate-spin;
  }

  /* 短视频播放记录恢复动画 */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* 短视频相关样式 */
  .shorts-container {
    height: 100vh;
    overflow: hidden;
    position: relative;
    background: black;
  }

  .shorts-video {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  /* 视频播放器样式 */
  video::-webkit-media-controls {
    display: none !important;
  }

  video::-webkit-media-controls-enclosure {
    display: none !important;
  }

  video::-webkit-media-controls-start-playback-button {
    display: none !important;
  }

  /* 禁用视频的默认控件 */
  video {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
  }

  /* 防止iOS Safari的视频全屏播放 */
  video::-webkit-media-controls-fullscreen-button {
    display: none !important;
  }

  /* 触摸优化 */
  .touch-none {
    touch-action: none;
  }

  .touch-pan-y {
    touch-action: pan-y;
  }
}
