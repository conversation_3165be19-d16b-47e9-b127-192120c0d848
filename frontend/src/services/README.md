# 🔐 统一API服务使用指南

## 📋 概述

为了确保应用中所有API请求都经过加密传输，我们实现了统一的API服务。**所有API调用都必须通过 `unifiedApiService` 进行**，这是应用中唯一的API调用入口。

## 🎯 核心原则

### ✅ 正确做法
```typescript
// 使用统一API服务 - 自动加密所有请求
import { unifiedApiService } from '../services/unifiedApiService';

// 方式1: 使用预定义的方法
const userInfo = await unifiedApiService.getUserInfo();
const videos = await unifiedApiService.getVideos();

// 方式2: 使用通用调用方法
const result = await unifiedApiService.call('/user/info');
const data = await unifiedApiService.post('/user/update', { nickname: 'test' });
```

### ❌ 错误做法
```typescript
// 不要直接使用其他API服务
import { apiService } from './api'; // ❌ 禁止
import { encryptedApi } from './encryptedApi'; // ❌ 禁止
import { secureApiService } from './secureApiService'; // ❌ 禁止

// 不要直接使用fetch
fetch('/api/user/info'); // ❌ 禁止
```

## 🔧 使用方法

### 1. 基本导入
```typescript
import { unifiedApiService } from '../services/unifiedApiService';

// 或者使用别名
import { apiService } from '../services/unifiedApiService';
import { api } from '../services/unifiedApiService';
```

### 2. 预定义方法调用
```typescript
// 用户相关
const user = await unifiedApiService.getUserInfo();
const updatedUser = await unifiedApiService.updateProfile({ nickname: 'newName' });

// 视频相关
const videos = await unifiedApiService.getVideos({ page: 1, limit: 10 });
const videoDetail = await unifiedApiService.getVideoDetail(123);

// 订单相关
const orders = await unifiedApiService.getUserOrders();
const order = await unifiedApiService.createCoinRechargeOrder(100, 1000);
```

### 3. 通用方法调用
```typescript
// POST请求
const result = await unifiedApiService.post('/user/update', {
  nickname: 'newName'
});

// GET请求
const data = await unifiedApiService.get('/user/info');

// 通用调用
const response = await unifiedApiService.call('/custom/endpoint', data, 'POST');
```

## 🛡️ 安全特性

### 自动加密
- 所有API请求自动使用AES-256-GCM加密
- 控制台和抓包工具无法看到明文数据
- 支持密钥自动轮换和会话管理

### 强制加密
- 不允许回退到非加密传输
- 仅加密系统本身的接口（如密钥交换）使用普通传输
- 确保100%的业务数据加密传输

### 统一管理
- 所有API调用都经过统一的错误处理
- 统一的请求日志和监控
- 统一的加密状态管理

## 📊 监控和调试

### 获取加密状态
```typescript
// 获取加密统计信息
const stats = unifiedApiService.getEncryptionStats();
console.log('加密率:', stats.encryptionRate + '%');
console.log('成功率:', stats.successRate + '%');

// 获取加密状态
const status = unifiedApiService.getEncryptionStatus();
console.log('加密已启用:', status.enabled);
```

### 调试信息
所有API调用都会在控制台输出调试信息：
```
🔐 统一加密API调用: POST /user/info
✅ API调用成功: POST /user/info
```

## 🔄 迁移指南

### 从旧API服务迁移

#### 1. 替换导入
```typescript
// 旧代码
import { apiService } from './api';
import { secureApiService } from './secureApiService';

// 新代码
import { unifiedApiService } from './unifiedApiService';
```

#### 2. 替换方法调用
```typescript
// 旧代码
const user = await apiService.get('/user/info');
const user2 = await secureApiService.getUserInfo();

// 新代码
const user = await unifiedApiService.getUserInfo();
// 或者
const user = await unifiedApiService.get('/user/info');
```

#### 3. 更新组件
```typescript
// 旧代码
import { secureApiService } from '../services/secureApiService';

const MyComponent = () => {
  const [user, setUser] = useState(null);
  
  useEffect(() => {
    secureApiService.getUserInfo().then(setUser);
  }, []);
  
  return <div>{user?.nickname}</div>;
};

// 新代码
import { unifiedApiService } from '../services/unifiedApiService';

const MyComponent = () => {
  const [user, setUser] = useState(null);
  
  useEffect(() => {
    unifiedApiService.getUserInfo().then(setUser);
  }, []);
  
  return <div>{user?.nickname}</div>;
};
```

## 🚨 重要注意事项

1. **唯一入口**: `unifiedApiService` 是应用中唯一的API调用入口
2. **强制加密**: 所有业务API都强制使用加密传输
3. **不允许回退**: 加密失败时不会回退到普通传输
4. **统一错误处理**: 所有API错误都通过统一的错误处理机制
5. **向后兼容**: 提供了别名以支持现有代码的平滑迁移

## 📞 技术支持

如果在使用过程中遇到问题：
1. 检查是否正确导入了 `unifiedApiService`
2. 确认没有直接使用其他API服务
3. 查看控制台的加密调试信息
4. 使用 `getEncryptionStats()` 检查加密状态

---

**🔐 数据安全无价，统一加密传输！**

*通过使用统一API服务，确保应用中所有数据传输都经过企业级加密保护。*
