# 视频URL处理工具

## 概述

`videoUrl.ts` 提供了统一的视频和封面URL处理功能，支持完整URL和相对路径的自动转换。

## 功能特性

### 🔗 URL类型支持

1. **完整URL** - 直接使用，不做修改
   ```
   https://example.com/videos/sample.m3u8
   http://cdn.example.com/hls/stream.m3u8
   ```

2. **绝对路径** - 自动添加基础URL前缀
   ```
   /33/114770.m3u8 → http://*************:9000/heben/videos/33/114770.m3u8
   /videos/sample.mp4 → http://*************:9000/heben/videos/videos/sample.mp4
   ```

3. **相对路径** - 自动添加基础URL前缀和斜杠
   ```
   videos/sample.mp4 → http://*************:9000/heben/videos/videos/sample.mp4
   hls/stream.m3u8 → http://*************:9000/heben/videos/hls/stream.m3u8
   ```

### 🛠️ 主要函数

#### `processVideoUrl(videoUrl: string): string`
处理视频URL，支持完整URL和相对路径。

#### `processCoverUrl(coverUrl: string): string`
处理封面图片URL，使用与视频URL相同的逻辑。

#### `processVideoUrls(video: VideoObject): VideoObject`
批量处理视频对象中的URL字段。

#### `isValidVideoUrl(url: string): boolean`
验证URL是否有效，包括格式检查和安全验证。

## 环境配置

### 环境变量

在 `.env` 文件中配置视频服务器基础URL：

```bash
# 开发环境
VITE_VIDEO_BASE_URL=http://localhost:9000/heben/videos

# 生产环境
VITE_VIDEO_BASE_URL=http://*************:9000/heben/videos
```

### 默认配置

如果未设置环境变量，系统会使用默认值：
- 开发环境：`http://localhost:9000/heben/videos`
- 生产环境：`http://*************:9000/heben/videos`

## 使用示例

### 在组件中使用

```typescript
import { processVideoUrl, processCoverUrl } from '../utils/videoUrl';

// 处理视频URL
const videoUrl = processVideoUrl('/33/114770.m3u8');
// 结果: http://*************:9000/heben/videos/33/114770.m3u8

// 处理封面URL
const coverUrl = processCoverUrl('/covers/video1.jpg');
// 结果: http://*************:9000/heben/videos/covers/video1.jpg

// 在JSX中使用
<video src={processVideoUrl(video.videoUrl)} />
<img src={processCoverUrl(video.cover)} />
```

### 批量处理

```typescript
import { processVideoUrls } from '../utils/videoUrl';

const video = {
  id: 1,
  title: '示例视频',
  videoUrl: '/33/114770.m3u8',
  cover: '/covers/video1.jpg'
};

const processedVideo = processVideoUrls(video);
// 结果: videoUrl 和 cover 都会被自动处理
```

## 安全特性

### URL验证
- 检查URL格式有效性
- 防止路径遍历攻击
- 过滤危险字符

### 错误处理
- 无效URL时返回空字符串
- 开发环境下提供详细警告信息
- 生产环境下静默处理错误

## 已集成的组件

以下组件已经集成了URL处理功能：

- ✅ `HLSPlayer` - 视频播放器
- ✅ `HLSShortVideoPlayer` - 短视频播放器
- ✅ `MobileVideoCard` - 移动端视频卡片
- ✅ `VideoCard` - 桌面端视频卡片

## 注意事项

1. **环境变量优先级**：自定义环境变量 > 默认配置
2. **URL清理**：自动处理重复斜杠和空格
3. **类型安全**：提供完整的TypeScript类型定义
4. **性能优化**：避免重复处理相同URL

## 故障排除

### 常见问题

1. **视频无法播放**
   - 检查 `VITE_VIDEO_BASE_URL` 环境变量是否正确设置
   - 确认视频服务器是否可访问

2. **封面图片不显示**
   - 检查封面URL格式是否正确
   - 确认图片文件是否存在

3. **开发环境警告**
   - 检查控制台警告信息
   - 确认URL格式是否符合预期

### 调试方法

```typescript
import { getVideoBaseUrlForDebug, isValidVideoUrl } from '../utils/videoUrl';

// 查看当前基础URL配置
console.log('当前基础URL:', getVideoBaseUrlForDebug());

// 验证URL是否有效
console.log('URL有效性:', isValidVideoUrl('/33/114770.m3u8'));
```
