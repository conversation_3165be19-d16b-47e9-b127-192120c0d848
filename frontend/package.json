{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@heroicons/react": "^2.2.0", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@types/crypto-js": "^4.2.2", "@videojs/http-streaming": "^3.17.0", "axios": "^1.9.0", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "gtag": "^1.0.1", "hls.js": "^1.6.2", "localforage": "^1.10.0", "lodash-es": "^4.17.21", "lucide-react": "^0.511.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-player": "^2.16.0", "react-router-dom": "^7.6.0", "tailwind-merge": "^3.3.0", "tailwindcss": "^3.4.17", "video.js": "^8.22.0", "videojs-contrib-hls": "^5.15.0", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/lodash-es": "^4.17.12", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.3", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}