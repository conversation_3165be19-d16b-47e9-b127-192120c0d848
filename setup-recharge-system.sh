#!/bin/bash

# 充值系统快速设置脚本
# 使用方法: ./setup-recharge-system.sh

set -e

echo "🚀 开始设置充值系统..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# 检查Node.js和npm
check_prerequisites() {
    print_info "检查系统环境..."
    
    if ! command -v node &> /dev/null; then
        print_error "Node.js 未安装，请先安装 Node.js"
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        print_error "npm 未安装，请先安装 npm"
        exit 1
    fi
    
    NODE_VERSION=$(node --version)
    print_message "Node.js 版本: $NODE_VERSION"
}

# 设置后端
setup_backend() {
    print_info "设置后端..."
    
    if [ ! -d "backend" ]; then
        print_error "backend 目录不存在"
        exit 1
    fi
    
    cd backend
    
    # 安装依赖
    print_info "安装后端依赖..."
    npm install
    
    # 检查环境变量文件
    if [ ! -f ".env.production" ]; then
        print_warning ".env.production 文件不存在，创建示例文件..."
        cp .env.example .env.production 2>/dev/null || true
    fi
    
    # 编译TypeScript
    print_info "编译TypeScript..."
    npm run build
    
    # 初始化数据库（如果需要）
    print_info "初始化数据库..."
    npm run setup-sqlite 2>/dev/null || print_warning "数据库初始化跳过（可能已存在）"
    
    # 初始化充值SKU
    print_info "初始化充值套餐..."
    npm run init-skus init
    
    # 测试充值系统
    print_info "测试充值系统..."
    npm run test-recharge env
    
    cd ..
    print_message "后端设置完成"
}

# 设置前端
setup_frontend() {
    print_info "设置前端..."
    
    if [ ! -d "frontend" ]; then
        print_error "frontend 目录不存在"
        exit 1
    fi
    
    cd frontend
    
    # 安装依赖
    print_info "安装前端依赖..."
    npm install
    
    # 检查环境变量文件
    if [ ! -f ".env" ]; then
        print_warning ".env 文件不存在，创建示例文件..."
        echo "REACT_APP_API_BASE_URL=http://localhost:3002/api" > .env
    fi
    
    # 构建前端
    print_info "构建前端..."
    npm run build
    
    cd ..
    print_message "前端设置完成"
}

# 验证设置
verify_setup() {
    print_info "验证设置..."
    
    # 检查后端文件
    if [ -f "backend/dist/index.js" ]; then
        print_message "后端编译文件存在"
    else
        print_error "后端编译失败"
        exit 1
    fi
    
    # 检查前端文件
    if [ -d "frontend/dist" ] || [ -d "frontend/build" ]; then
        print_message "前端构建文件存在"
    else
        print_warning "前端构建文件不存在（可能使用开发模式）"
    fi
    
    # 测试后端充值系统
    cd backend
    print_info "运行充值系统测试..."
    npm run test-recharge all || print_warning "充值系统测试失败（可能需要配置第三方支付参数）"
    cd ..
}

# 显示启动说明
show_startup_instructions() {
    echo ""
    echo "🎉 充值系统设置完成！"
    echo ""
    echo "📋 启动说明："
    echo ""
    echo "1. 启动后端服务："
    echo "   cd backend"
    echo "   npm run dev        # 开发模式"
    echo "   npm start          # 生产模式"
    echo ""
    echo "2. 启动前端服务："
    echo "   cd frontend"
    echo "   npm run dev        # 开发模式"
    echo "   npm run preview    # 预览构建版本"
    echo ""
    echo "3. 访问应用："
    echo "   前端: http://localhost:5173"
    echo "   后端: http://localhost:3002"
    echo "   测试页面: http://localhost:5173/test/recharge"
    echo ""
    echo "📋 管理命令："
    echo ""
    echo "1. 管理充值套餐："
    echo "   cd backend"
    echo "   npm run init-skus list      # 查看套餐"
    echo "   npm run init-skus init      # 初始化套餐"
    echo "   npm run init-skus activate <skuid>   # 激活套餐"
    echo "   npm run init-skus deactivate <skuid> # 停用套餐"
    echo ""
    echo "2. 测试充值功能："
    echo "   npm run test-recharge all   # 运行所有测试"
    echo "   npm run test-recharge flow  # 测试充值流程"
    echo ""
    echo "⚠️  重要提醒："
    echo "1. 请在 backend/.env.production 中配置正确的第三方支付参数"
    echo "2. 请确保数据库连接正常"
    echo "3. 生产环境请使用 HTTPS"
    echo ""
}

# 主函数
main() {
    echo "💳 充值系统快速设置脚本"
    echo "=========================="
    echo ""
    
    check_prerequisites
    setup_backend
    setup_frontend
    verify_setup
    show_startup_instructions
}

# 错误处理
trap 'print_error "设置过程中发生错误，请检查上面的错误信息"; exit 1' ERR

# 运行主函数
main

echo ""
print_message "设置完成！享受你的充值系统吧！ 🎉"
