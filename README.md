# SmartVideoWeb

一个现代化的视频流媒体Web应用，支持移动端优化和完整的商业化功能。

## 技术栈

### 后端
- **框架**: Node.js + TypeScript + Koa
- **数据库**: Sequelize ORM + SQLite/MySQL
- **认证**: JWT + bcrypt
- **功能**: RESTful API、用户认证、视频管理、订单系统

### 前端
- **框架**: React 19 + TypeScript + Vite
- **UI库**: Radix UI + Tailwind CSS
- **路由**: React Router DOM
- **状态管理**: Zustand
- **视频播放**: Video.js + HLS.js (支持m3u8格式)

## 项目结构

```
smartVideoWeb/
├── backend/          # 后端API服务
│   ├── src/
│   │   ├── models/   # 数据模型
│   │   ├── routes/   # 路由定义
│   │   ├── controllers/ # 控制器
│   │   └── config/   # 配置文件
├── frontend/         # 前端React应用
│   ├── src/
│   │   ├── pages/    # 页面组件
│   │   ├── components/ # 通用组件
│   │   ├── store/    # 状态管理
│   │   └── services/ # API服务
└── database.sqlite   # SQLite数据库文件
```

## 核心功能

### 用户系统
- 用户注册/登录
- JWT身份认证
- 用户资料管理

### 视频功能
- 视频播放（支持HLS流媒体）
- 视频分类和搜索
- 收藏、历史记录
- 短视频功能（类似TikTok）

### 付费系统
- VIP会员制度
- 虚拟币充值
- 视频购买
- 订单管理

### 移动端优化
- 响应式设计
- 移动端专用布局
- 底部标签栏导航
- 手势操作支持

## 快速开始

### 环境要求
- Node.js >= 16
- npm 或 yarn

### 🚀 一键启动 (推荐)

我们提供了多种一键启动方式，选择适合您的方式：

#### 方式 1: npm 脚本 (推荐)
```bash
# 安装所有依赖
npm run install:all

# 一键启动前端和后端
npm run dev
```

#### 方式 2: Shell 脚本 (Linux/macOS)
```bash
# 给脚本执行权限 (首次运行)
chmod +x start.sh

# 一键启动
./start.sh
```

#### 方式 3: 批处理脚本 (Windows)
```cmd
# 双击运行或命令行执行
start.bat
```

#### 方式 4: Makefile (Linux/macOS)
```bash
# 首次设置 (安装依赖 + 初始化数据库)
make setup

# 启动开发服务器
make dev

# 查看所有可用命令
make help
```

### 📋 可用命令

| 命令 | 描述 |
|------|------|
| `npm run dev` | 同时启动前端和后端开发服务器 |
| `npm run dev:backend` | 仅启动后端服务器 |
| `npm run dev:frontend` | 仅启动前端服务器 |
| `npm run build` | 构建生产版本 |
| `npm run start` | 启动生产服务器 |
| `npm run install:all` | 安装所有依赖 |
| `npm run clean` | 清理 node_modules 和构建文件 |
| `npm run seed` | 初始化数据库数据 |

### 🌐 服务器地址

启动成功后，您可以访问：

- **前端应用**: http://localhost:5173
- **后端API**: http://localhost:3002
- **API健康检查**: http://localhost:3002/health

### 手动安装依赖 (可选)

如果您想手动安装依赖：

```bash
# 安装根目录依赖 (concurrently)
npm install

# 安装后端依赖
cd backend
npm install

# 安装前端依赖
cd ../frontend
npm install
```

### 数据库初始化

```bash
# 运行数据库种子文件
npm run seed
# 或
cd backend
npm run seed
```

## 开发说明

### Git 忽略文件配置

项目包含三个 `.gitignore` 文件：

1. **根目录 `.gitignore`**: 全局忽略规则
2. **`backend/.gitignore`**: 后端特定忽略规则
3. **`frontend/.gitignore`**: 前端特定忽略规则

主要忽略的文件类型：
- `node_modules/` - 依赖包
- `dist/`, `build/` - 构建输出
- `*.sqlite`, `*.db` - 数据库文件
- `.env*` - 环境变量文件
- `*.log` - 日志文件
- IDE 配置文件
- 操作系统生成的文件

### 环境变量

创建 `.env` 文件在 `backend/` 目录下：

```env
PORT=3002
NODE_ENV=development
JWT_SECRET=your-jwt-secret
DB_HOST=localhost
DB_PORT=3306
DB_NAME=smartvideo
DB_USER=root
DB_PASS=password
```

## 部署

### 构建生产版本

```bash
# 构建后端
cd backend
npm run build

# 构建前端
cd frontend
npm run build
```

### 启动生产服务器

```bash
# 启动后端生产服务器
cd backend
npm start
```

## 贡献

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 ISC 许可证。
