# MySQL数据库迁移文档

## 迁移概述

本文档记录了从SQLite数据库迁移到MySQL数据库的完整过程。

### 迁移信息
- **源数据库**: SQLite (database.sqlite)
- **目标数据库**: MySQL 8.0
- **数据库名**: hepburn
- **主机**: localhost:3306
- **用户**: root
- **密码**: GJiashuai123

## 迁移步骤

### 1. 数据库配置更新

#### 修改数据库连接配置
文件: `backend/src/config/database.ts`

```typescript
// 原SQLite配置
const sequelize = new Sequelize({
  dialect: 'sqlite',
  storage: './database.sqlite',
  // ...
});

// 新MySQL配置
const sequelize = new Sequelize({
  dialect: 'mysql',
  host: 'localhost',
  port: 3306,
  database: 'hepburn',
  username: 'root',
  password: 'GJiashuai123',
  logging: process.env.NODE_ENV === 'development' ? console.log : false,
  pool: {
    max: 10,
    min: 0,
    acquire: 30000,
    idle: 10000
  },
  dialectOptions: {
    charset: 'utf8mb4',
  },
  define: {
    charset: 'utf8mb4',
  }
});
```

### 2. 创建MySQL初始化脚本

文件: `backend/src/scripts/initMysql.ts`

该脚本负责：
- 创建MySQL数据库（如果不存在）
- 测试Sequelize连接
- 同步数据库表结构

### 3. 更新package.json脚本

```json
{
  "scripts": {
    "init-mysql": "ts-node src/scripts/initMysql.ts",
    "setup": "npm run init-mysql && npm run seed",
    "setup-sqlite": "npm run migrate && npm run seed"
  }
}
```

### 4. 执行迁移

```bash
# 1. 初始化MySQL数据库
cd backend
npm run init-mysql

# 2. 填充种子数据
npm run seed

# 3. 启动应用
npm run dev
```

## 数据库表结构

迁移后的MySQL数据库包含以下表：

### 用户表 (users)
- 用户基本信息
- VIP状态和到期时间
- 金币余额
- 邀请码系统

### 视频表 (videos)
- 视频基本信息
- 分类和标签
- 价格和状态
- 推荐和热门标记

### 订单表 (orders)
- 充值订单
- VIP购买订单
- 视频购买订单
- 支付状态

### 用户视频关系表 (user_videos)
- 收藏关系
- 购买关系
- 观看历史

### 邀请表 (invitations)
- 邀请关系
- 邀请码管理
- 多级邀请结构

### 佣金表 (commissions)
- 邀请佣金记录
- 佣金状态和类型

## 字符集配置

MySQL数据库使用UTF8MB4字符集，支持完整的Unicode字符，包括emoji表情符号。

```sql
CREATE DATABASE IF NOT EXISTS `hepburn` 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;
```

## 性能优化

### 连接池配置
```typescript
pool: {
  max: 10,        // 最大连接数
  min: 0,         // 最小连接数
  acquire: 30000, // 获取连接超时时间
  idle: 10000     // 连接空闲时间
}
```

### 索引优化
Sequelize会自动创建以下索引：
- 主键索引
- 唯一约束索引
- 外键索引
- 自定义业务索引

## 注意事项

### 1. 数据类型差异
- SQLite的动态类型 → MySQL的严格类型
- 自动处理数据类型转换

### 2. 字符集问题
- 确保使用UTF8MB4字符集
- 避免字符编码问题

### 3. 性能考虑
- MySQL支持更好的并发性能
- 适合生产环境使用

### 4. 备份策略
- 定期备份MySQL数据库
- 使用mysqldump或其他备份工具

## 验证迁移

### 1. 连接测试
```bash
# 检查数据库连接
npm run init-mysql
```

### 2. 数据验证
```bash
# 检查种子数据
npm run seed
```

### 3. 应用测试
```bash
# 启动应用并测试功能
npm run dev
```

## 故障排除

### 常见问题

1. **连接被拒绝**
   - 检查MySQL服务是否启动
   - 验证连接参数

2. **字符集警告**
   - 移除不兼容的collate配置
   - 使用标准的charset配置

3. **权限问题**
   - 确保MySQL用户有足够权限
   - 检查数据库访问权限

### 日志分析
应用启动时会显示详细的数据库连接和同步日志，可用于诊断问题。

## 总结

MySQL迁移已成功完成，应用现在使用MySQL作为主数据库，提供了更好的性能、可靠性和扩展性。所有原有功能保持不变，数据结构完全兼容。
