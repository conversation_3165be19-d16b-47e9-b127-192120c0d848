# 视频数据批量导入指南

## 📋 概述

本系统支持批量导入视频数据，可以使用现有的ID或自动生成新ID。支持JSON和CSV两种格式的数据导入。

## 🔧 API接口

### 1. 批量导入接口

**接口地址：** `POST /api/admin/videos/batch-import`

**请求参数：**
```json
{
  "videos": [
    // 视频数据数组
  ],
  "options": {
    "skipDuplicates": false,    // 是否跳过重复ID
    "updateExisting": true,     // 是否更新已存在的记录
    "validateUrls": false,      // 是否验证URL有效性
    "preserveStats": false      // 是否保留现有统计数据
  }
}
```

### 2. 获取导入模板

**接口地址：** `GET /api/admin/videos/import-template`

返回字段说明和示例数据。

### 3. 导入历史

**接口地址：** `GET /api/admin/videos/import-history`

查看历史导入记录。

## 📊 数据字段说明

### 必填字段 (Required)

| 字段名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| `title` | String(200) | 视频标题 | "精彩短视频合集" |
| `cover` | String(URL) | 封面图片URL | "https://example.com/cover.jpg" |
| `videoUrl` | String(URL) | 视频文件URL | "https://example.com/video.m3u8" |
| `duration` | Integer | 视频时长(秒) | 120 |
| `category` | String(50) | 视频分类 | "娱乐" |

### 可选字段 (Optional)

| 字段名 | 类型 | 默认值 | 说明 | 示例 |
|--------|------|--------|------|------|
| `id` | Integer | 自动生成 | 视频唯一标识 | 1001 |
| `status` | Enum | 'free' | 视频状态 | 'free', 'paid', 'vip_free' |
| `price` | Integer | 0 | 价格(金币) | 10 |
| `description` | Text | '' | 视频描述 | "这是一个精彩的短视频..." |
| `tags` | String | '' | 标签(逗号分隔) | "搞笑,娱乐,热门" |
| `viewCount` | Integer | 0 | 观看次数 | 1000 |
| `likeCount` | Integer | 0 | 点赞次数 | 50 |
| `isRecommended` | Boolean | false | 是否推荐 | true |
| `isHot` | Boolean | false | 是否热门 | true |
| `sortOrder` | Integer | 0 | 排序权重 | 100 |

## 📄 数据格式示例

### JSON格式

```json
[
  {
    "id": 1001,
    "title": "搞笑短视频1",
    "cover": "https://example.com/cover1.jpg",
    "videoUrl": "https://example.com/video1.m3u8",
    "duration": 60,
    "category": "娱乐",
    "status": "free",
    "price": 0,
    "description": "超级搞笑的短视频",
    "tags": "搞笑,娱乐",
    "isRecommended": true,
    "isHot": false,
    "sortOrder": 100,
    "viewCount": 1500,
    "likeCount": 25
  }
]
```

### CSV格式

```csv
id,title,cover,videoUrl,duration,category,status,price,description,tags,isRecommended,isHot,sortOrder,viewCount,likeCount
1001,"搞笑短视频1","https://example.com/cover1.jpg","https://example.com/video1.m3u8",60,"娱乐","free",0,"超级搞笑的短视频","搞笑,娱乐",true,false,100,1500,25
```

## ⚙️ 导入选项说明

### skipDuplicates (跳过重复)
- `true`: 遇到重复ID时跳过，不导入
- `false`: 遇到重复ID时根据updateExisting决定操作

### updateExisting (更新已存在)
- `true`: 更新已存在的记录
- `false`: 不更新，报错或跳过

### validateUrls (验证URL)
- `true`: 验证封面和视频URL的有效性
- `false`: 跳过URL验证（推荐，提高导入速度）

### preserveStats (保留统计)
- `true`: 更新时保留现有的观看次数和点赞数
- `false`: 使用导入数据中的统计数据

## 🎯 使用场景

### 场景1：首次批量导入
```json
{
  "videos": [...],
  "options": {
    "skipDuplicates": false,
    "updateExisting": false,
    "validateUrls": false,
    "preserveStats": false
  }
}
```

### 场景2：更新现有数据
```json
{
  "videos": [...],
  "options": {
    "skipDuplicates": false,
    "updateExisting": true,
    "validateUrls": false,
    "preserveStats": true
  }
}
```

### 场景3：安全导入（跳过重复）
```json
{
  "videos": [...],
  "options": {
    "skipDuplicates": true,
    "updateExisting": false,
    "validateUrls": false,
    "preserveStats": false
  }
}
```

## 📝 使用步骤

### 1. 准备数据文件
- 创建JSON或CSV格式的数据文件
- 确保必填字段完整
- 检查数据格式正确性

### 2. 调用导入接口
```bash
curl -X POST http://localhost:3002/api/admin/videos/batch-import \
  -H "Content-Type: application/json" \
  -d '{
    "videos": [...],
    "options": {...}
  }'
```

### 3. 检查导入结果
```json
{
  "success": true,
  "message": "导入完成: 成功 10, 更新 0, 跳过 0, 错误 0",
  "data": {
    "success": true,
    "total": 10,
    "imported": 10,
    "updated": 0,
    "skipped": 0,
    "errors": []
  }
}
```

## 🏷️ **分类管理说明**

### **分类处理方式**
系统采用**动态分类**方式，**无需提前录入分类**：
- ✅ 分类从现有视频中自动提取
- ✅ 导入时可使用任意分类名称
- ✅ 支持智能分类标准化

### **推荐分类列表**
```
娱乐、教育、生活、科技、音乐、体育、游戏、
美食、旅行、时尚、汽车、财经、新闻、电影、
电视剧、动漫、纪录片、综艺、短视频、其他
```

### **分类标准化规则**
1. **英文自动转换**: `entertainment` → `娱乐`
2. **大小写不敏感**: `EDUCATION` → `教育`
3. **自动去除空格**: `  娱乐  ` → `娱乐`
4. **智能映射**: `tech` → `科技`
5. **保留自定义**: 不在映射表中的分类保持原样

### **常见英文分类映射**
| 英文 | 中文 | 英文 | 中文 |
|------|------|------|------|
| entertainment | 娱乐 | education | 教育 |
| technology/tech | 科技 | music | 音乐 |
| sports/fitness | 体育 | game/gaming | 游戏 |
| life/lifestyle | 生活 | food/cooking | 美食 |
| travel/trip | 旅行 | fashion/style | 时尚 |
| car/auto | 汽车 | finance/business | 财经 |
| movie/film | 电影 | tv/series | 电视剧 |
| anime/animation | 动漫 | documentary | 纪录片 |

## 🚨 注意事项

### 数据验证
1. **标题不能为空**
2. **封面URL必须有效**
3. **视频URL必须有效**
4. **时长必须大于0**
5. **分类不能为空**
6. **分类长度不超过50字符**
7. **价格范围：0-1000金币**

### ID使用规则
1. **指定ID**: 使用数据中的ID值
2. **自动生成**: 不提供ID字段或ID为空
3. **ID冲突**: 根据导入选项处理

### 分类使用建议
1. **优先使用推荐分类**: 确保分类统一性
2. **英文会自动转换**: 可直接使用英文分类名
3. **避免拼写错误**: 错误的分类名会被保留
4. **自定义分类**: 可以使用，但建议谨慎

### 性能建议
1. **批量大小**: 建议每次导入不超过1000条
2. **URL验证**: 大批量导入时建议关闭URL验证
3. **分批导入**: 超大数据集建议分批处理

## 🛠️ 测试工具

### 使用测试脚本
```bash
# 测试基本导入功能
node scripts/test-import.js

# 导入示例数据
node scripts/import-example-data.js
```

### 示例数据文件
- `data/video-import-example.json` - JSON格式示例
- `data/video-import-example.csv` - CSV格式示例

## 📞 技术支持

如遇到导入问题，请检查：
1. 数据格式是否正确
2. 必填字段是否完整
3. 网络连接是否正常
4. 服务器日志错误信息
