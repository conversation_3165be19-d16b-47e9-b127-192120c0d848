# Server Configuration
PORT=3002
NODE_ENV=production

# Database Configuration (使用 SQLite 进行开发测试)
DB_HOST=*************
DB_USER=hebendb
DB_PASSWORD=5DyJmZjaYMFEpMBy
DB_NAME=hebendb
DB_PORT=62102

# JWT Configuration secret 为 heben 的 md5
JWT_SECRET=52d1f4397b9f8c00eeec118db52cf7bf
JWT_EXPIRES_IN=7d

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# CORS Configuration
CORS_ORIGIN=http://localhost:5173

# 邮件发送配置
# 个人邮箱配置，支持多个邮箱，用逗号分隔
# 格式：邮箱:密码:SMTP服务器:端口
# 示例：<EMAIL>:app-password:smtp.gmail.com:587,<EMAIL>:app-password:smtp.qq.com:587
# 使用163邮箱（更稳定）
PERSONAL_EMAILS=<EMAIL>:UT2bN38SB5TegVdF:smtp.163.com:465

# 如果要使用多个邮箱，可以这样配置：
# PERSONAL_EMAILS="<EMAIL>:GJiashuai123:smtp.gmail.com:587"

# 邮件发送配置
EMAIL_FROM_NAME=赫本视频
