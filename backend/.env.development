# Server Configuration
PORT=3002
NODE_ENV=development

# Database Configuration (使用 SQLite 进行开发测试)
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=GJiashuai123
DB_NAME=hepburn
DB_PORT=3306


# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# CORS Configuration
CORS_ORIGIN=http://localhost:5173

# 邮件发送配置
# 个人邮箱配置，支持多个邮箱，用逗号分隔
# 格式：邮箱:密码:SMTP服务器:端口
# 示例：<EMAIL>:app-password:smtp.gmail.com:587,<EMAIL>:app-password:smtp.qq.com:587
# 使用163邮箱（更稳定）
PERSONAL_EMAILS=<EMAIL>:UT2bN38SB5TegVdF:smtp.163.com:465

# 如果要使用多个邮箱，可以这样配置：
# PERSONAL_EMAILS="<EMAIL>:GJiashuai123:smtp.gmail.com:587"

# 邮件发送配置
EMAIL_FROM_NAME=智能视频平台
