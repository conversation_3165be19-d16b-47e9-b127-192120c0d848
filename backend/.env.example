# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=hepburn
DB_USER=root
DB_PASSWORD=GJiashuai123

# JWT配置
JWT_SECRET=your-jwt-secret-key

# 第三方支付配置
THIRD_PARTY_PROJECT_ID=67cf0c9cacfcf1db5e546914
THIRD_PARTY_SECRET=sk_62513e688e98d9bd4c5f6171b323a6d0

# 第三方支付回调配置
# 注意：这些URL必须使用真实的域名，不能使用localhost或IP地址

# 支付成功后的重定向链接，指向订单页面，需要包含完整域名
# 用户支付完成后会跳转到这个页面
THIRD_PARTY_REDIRECT_URL=https://yourdomain.com/orders

# 订单支付成功或退款时的后端回调通知，必须使用域名https，不能通过ip访问
# 第三方支付平台会向这个URL发送POST请求通知支付结果
# 必须在5秒内处理完毕并返回200/201状态码，否则会认为回调失败
# 回调失败时会进行重试(最多5次)
THIRD_PARTY_CALLBACK_URL=https://yourdomain.com/api/third-party-recharge/callback

# 邮件发送配置
# 个人邮箱配置，支持多个邮箱，用逗号分隔
# 格式：邮箱:密码:SMTP服务器:端口
PERSONAL_EMAILS=<EMAIL>:your-app-password1:smtp.gmail.com:587,<EMAIL>:your-app-password2:smtp.qq.com:587

# 邮件发送配置
EMAIL_FROM_NAME=智能视频平台

# 开发环境示例（仅用于本地测试，生产环境必须使用真实域名）
# THIRD_PARTY_REDIRECT_URL=http://localhost:5173/orders
# THIRD_PARTY_CALLBACK_URL=http://localhost:3002/api/third-party-recharge/callback
