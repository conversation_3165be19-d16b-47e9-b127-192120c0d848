{"name": "smart-video-backend", "version": "1.0.0", "main": "dist/index.js", "scripts": {"dev": "nodemon --exec ts-node src/index.ts", "build": "npm run build:compile && npm run build:copy", "build:compile": "tsc", "build:copy": "npm run copy:env && npm run copy:config", "copy:env": "cp -f .env.production dist/ 2>/dev/null || true && cp -f .env dist/ 2>/dev/null || true && cp -f .env.example dist/ 2>/dev/null || true", "copy:config": "cp -f package.json dist/ 2>/dev/null || true && cp -f package-lock.json dist/ 2>/dev/null || true", "start": "node dist/index.js", "seed": "ts-node src/scripts/seed.ts", "migrate": "ts-node src/scripts/migrateUserTable.ts", "init-mysql": "ts-node src/scripts/initMysql.ts", "setup": "npm run init-mysql && npm run seed", "setup-sqlite": "npm run migrate && npm run seed", "init-skus": "ts-node src/scripts/initThirdPartySkus.ts", "test-recharge": "ts-node src/scripts/testRechargeFlow.ts", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["video", "streaming", "koa", "typescript"], "author": "", "license": "ISC", "description": "Smart Video Web Backend API", "dependencies": {"@koa/cors": "^5.0.0", "@types/bcrypt": "^5.0.2", "@types/nodemailer": "^6.4.17", "@types/uuid": "^10.0.0", "axios": "^1.9.0", "bcrypt": "^6.0.0", "bcryptjs": "^3.0.2", "crypto": "^1.0.1", "dotenv": "^16.5.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "koa": "^3.0.0", "koa-bodyparser": "^4.4.1", "koa-jwt": "^4.0.4", "koa-ratelimit": "^5.1.0", "koa-router": "^13.0.1", "mysql2": "^3.14.1", "node-rsa": "^1.1.1", "nodemailer": "^7.0.3", "sequelize": "^6.37.7", "sqlite3": "^5.1.7", "uuid": "^11.1.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.9", "@types/koa": "^2.15.0", "@types/koa__cors": "^5.0.0", "@types/koa-bodyparser": "^4.3.12", "@types/koa-cors": "^0.0.6", "@types/koa-ratelimit": "^5.0.5", "@types/koa-router": "^7.4.8", "@types/node": "^22.15.21", "@types/node-rsa": "^1.1.4", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}