赫本平台后端部署信息
========================

构建时间: 2025年 6月11日 星期三 01时49分05秒 CST
包名称: backend-20250611-014901.tar.gz
下载地址: https://storage.free-connect.sbs/backend/backend-20250611-014901.tar.gz

部署步骤:
1. 下载后端包: wget https://storage.free-connect.sbs/backend/backend-20250611-014901.tar.gz
2. 解压: tar -xzf backend-20250611-014901.tar.gz
3. 进入目录: cd backend
4. 运行启动脚本: ./start.sh

或使用 PM2:
1. 安装 PM2: npm install -g pm2
2. 启动应用: pm2 start ecosystem.config.js
3. 查看状态: pm2 status
4. 查看日志: pm2 logs smart-video-backend

环境要求:
- Node.js >= 16.0.0
- npm >= 8.0.0
- MySQL 数据库
- 端口 3002 可用

配置文件:
- 复制 .env.example 为 .env
- 修改数据库连接信息
- 设置其他环境变量

注意事项:
- 确保数据库已创建并可连接
- 检查防火墙设置
- 建议使用 PM2 进行进程管理
