# 🚀 SmartVideoWeb 快速启动指南

欢迎使用 SmartVideoWeb！这是一个现代化的视频流媒体应用，支持移动端优化。

## 📋 前置要求

在开始之前，请确保您的系统已安装：

- **Node.js** >= 16.0.0
- **npm** >= 7.0.0 (或 yarn)
- **Git** (用于版本控制)

检查版本：
```bash
node --version
npm --version
git --version
```

## 🎯 一键启动 (推荐方式)

### 方式 1: npm 脚本 (最简单)

```bash
# 1. 克隆项目 (如果还没有)
git clone <repository-url>
cd smartVideoWeb

# 2. 安装所有依赖
npm run install:all

# 3. 初始化数据库 (首次运行)
npm run seed

# 4. 一键启动前端和后端
npm run dev
```

### 方式 2: Shell 脚本 (Linux/macOS)

```bash
# 1. 给脚本执行权限
chmod +x start.sh

# 2. 运行启动脚本
./start.sh
```

### 方式 3: 批处理脚本 (Windows)

```cmd
# 双击 start.bat 文件或在命令行运行
start.bat
```

### 方式 4: Makefile (开发者推荐)

```bash
# 首次设置 (安装依赖 + 初始化数据库)
make setup

# 启动开发服务器
make dev

# 查看所有可用命令
make help
```

## 🌐 访问应用

启动成功后，打开浏览器访问：

- **前端应用**: http://localhost:5173
- **后端API**: http://localhost:3002
- **API健康检查**: http://localhost:3002/health

## 🛠️ 常用命令

| 命令 | 功能 |
|------|------|
| `npm run dev` | 同时启动前端和后端 |
| `npm run dev:backend` | 仅启动后端服务器 |
| `npm run dev:frontend` | 仅启动前端服务器 |
| `npm run build` | 构建生产版本 |
| `npm run start` | 启动生产服务器 |
| `npm run seed` | 初始化数据库数据 |
| `npm run clean` | 清理项目文件 |

## 🔧 故障排除

### 端口被占用

如果遇到端口占用错误：

```bash
# 查看端口占用
lsof -i :3002  # 后端端口
lsof -i :5173  # 前端端口

# 杀死占用进程
kill -9 <PID>
```

### 依赖安装失败

```bash
# 清理缓存
npm cache clean --force

# 删除 node_modules 重新安装
npm run clean
npm run install:all
```

### 数据库问题

```bash
# 重新初始化数据库
rm -f backend/database.sqlite database.sqlite
npm run seed
```

## 📁 项目结构

```
smartVideoWeb/
├── backend/          # 后端 API (Koa + TypeScript)
├── frontend/         # 前端应用 (React + TypeScript)
├── start.sh          # Linux/macOS 启动脚本
├── start.bat         # Windows 启动脚本
├── Makefile          # Make 命令
└── package.json      # 根目录配置
```

## 🎨 开发模式特性

- **热重载**: 代码修改后自动刷新
- **并行启动**: 前端和后端同时启动
- **端口检测**: 自动检测端口占用
- **依赖检查**: 自动安装缺失的依赖
- **错误处理**: 友好的错误提示

## 📱 移动端测试

应用针对移动端进行了优化，您可以：

1. 在浏览器中按 F12 打开开发者工具
2. 点击设备模拟器图标
3. 选择移动设备进行测试

或者在手机浏览器中访问：`http://[您的IP]:5173`

## 🚀 生产部署

```bash
# 构建生产版本
npm run build

# 启动生产服务器
npm run start
```

## 📞 获取帮助

如果遇到问题：

1. 查看控制台错误信息
2. 检查 README.md 文档
3. 运行 `./test-start.sh` 进行诊断
4. 查看项目 Issues

---

**祝您使用愉快！** 🎉
