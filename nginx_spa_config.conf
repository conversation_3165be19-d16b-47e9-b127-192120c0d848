server
{
    listen 80;
    server_name *************;
    index index.html index.htm default.htm default.html;
    root /www/wwwroot/heben;
    
    #CERT-APPLY-CHECK--START
    # 用于SSL证书申请时的文件验证相关配置 -- 请勿删除并保持这段设置在优先级高的位置
    include /www/server/panel/vhost/nginx/well-known/*************.conf;
    #CERT-APPLY-CHECK--END

    #SSL-START SSL相关配置，请勿删除或修改下一行带注释的404规则
    #error_page 404/404.html;
    #SSL-END

    #ERROR-PAGE-START  错误页配置，可以注释、删除或修改
    #error_page 404 /404.html;
    #error_page 502 /502.html;
    #ERROR-PAGE-END

    #REWRITE-START URL重写规则引用,修改后将导致面板设置的伪静态规则失效
    include /www/server/panel/vhost/rewrite/html_*************.conf;
    #REWRITE-END

    # 🎯 单页应用路由支持 - 核心配置
    # 这个配置解决了 SPA 刷新 404 的问题
    location / {
        try_files $uri $uri/ /index.html;
    }

    # 🔌 API 代理配置 (如果后端API在同一服务器)
    # 根据您的后端端口调整 proxy_pass
    location /api/ {
        proxy_pass http://localhost:3000/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 处理 CORS (如果需要)
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
        add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';
        
        # 处理 OPTIONS 请求
        if ($request_method = 'OPTIONS') {
            return 204;
        }
    }

    #禁止访问的文件或目录
    location ~ ^/(\.user.ini|\.htaccess|\.git|\.env|\.svn|\.project|LICENSE|README.md)
    {
        return 404;
    }

    #一键申请SSL证书验证目录相关设置
    location ~ \.well-known{
        allow all;
    }

    #禁止在证书验证目录放入敏感文件
    if ( $uri ~ "^/\.well-known/.*\.(php|jsp|py|js|css|lua|ts|go|zip|tar\.gz|rar|7z|sql|bak)$" ) {
        return 403;
    }

    # 📷 图片资源缓存配置 (30天)
    location ~ .*\.(gif|jpg|jpeg|png|bmp|swf|ico|svg|webp|avif)$
    {
        expires 30d;
        add_header Cache-Control "public, immutable";
        add_header Vary "Accept-Encoding";
        error_log /dev/null;
        access_log /dev/null;
    }

    # 📦 静态资源缓存配置 (7天)
    location ~ .*\.(js|css|woff|woff2|ttf|eot|otf)$
    {
        expires 7d;
        add_header Cache-Control "public, immutable";
        add_header Vary "Accept-Encoding";
        error_log /dev/null;
        access_log /dev/null;
    }

    # 📄 HTML 文件不缓存，确保更新及时生效
    location ~ .*\.html$ {
        expires -1;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Vary "Accept-Encoding";
    }

    # 🎬 视频文件缓存配置 (如果有本地视频文件)
    location ~ .*\.(mp4|webm|ogg|avi|mov|wmv|flv|m4v)$ {
        expires 7d;
        add_header Cache-Control "public";
        add_header Vary "Accept-Encoding";
        
        # 支持断点续传
        add_header Accept-Ranges bytes;
    }

    # 📊 JSON/XML 数据文件
    location ~ .*\.(json|xml)$ {
        expires 1h;
        add_header Cache-Control "public";
        add_header Vary "Accept-Encoding";
    }

    # 🔒 安全头配置
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    # 📝 日志配置
    access_log  /www/wwwlogs/*************.log;
    error_log  /www/wwwlogs/*************.error.log;
}
