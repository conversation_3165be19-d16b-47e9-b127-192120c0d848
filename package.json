{"name": "smart-video-web", "version": "1.0.0", "description": "赫本流媒体Web应用", "private": true, "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "dev:all": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\" \"npm run dev:admin\"", "dev:admin": "cd admin-system && npm run dev", "build": "npm run build:backend && npm run build:frontend", "build:backend": "cd backend && npm run build", "build:frontend": "cd frontend && npm run build", "build:all": "npm run build:backend && npm run build:frontend && npm run build:admin", "build:admin": "cd admin-system && npm run build", "start": "concurrently \"npm run start:backend\" \"npm run start:frontend\"", "start:backend": "cd backend && npm start", "start:frontend": "cd frontend && npm run preview", "start:all": "concurrently \"npm run start:backend\" \"npm run start:frontend\" \"npm run start:admin\"", "start:admin": "cd admin-system && npm run start", "install:all": "npm install && cd backend && npm install && cd ../frontend && npm install && cd ../admin-system && npm run install:all", "clean": "npm run clean:backend && npm run clean:frontend && npm run clean:admin", "clean:backend": "cd backend && rm -rf node_modules dist", "clean:frontend": "cd frontend && rm -rf node_modules dist", "clean:admin": "cd admin-system && npm run clean", "seed": "cd backend && npm run seed", "seed:admin": "cd admin-system && npm run setup:backend", "lint": "npm run lint:backend && npm run lint:frontend && npm run lint:admin", "lint:backend": "echo 'Backend linting not configured'", "lint:frontend": "cd frontend && npm run lint", "lint:admin": "cd admin-system && npm run lint"}, "devDependencies": {"concurrently": "^8.2.2"}, "keywords": ["video", "streaming", "react", "koa", "typescript"], "author": "", "license": "ISC", "dependencies": {"axios": "^1.9.0", "crypto-js": "^4.2.0"}}