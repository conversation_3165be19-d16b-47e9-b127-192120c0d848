import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ConfigProvider, App as AntdApp, theme } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { QueryClient, QueryClientProvider } from 'react-query';
import { HelmetProvider } from 'react-helmet-async';
import { ErrorBoundary } from 'react-error-boundary';

// 页面组件
import LoginPage from '@/pages/Login';
import DashboardPage from '@/pages/Dashboard';
import UsersPage from '@/pages/Users';
import VideosPage from '@/pages/Videos';
import OrdersPage from '@/pages/Orders';
import RechargeConfigPage from '@/pages/RechargeConfig';
import VipConfigPage from '@/pages/VipConfig';
import SkuManagement from '@/pages/SkuManagement';

import SettingsPage from '@/pages/Settings';
import InvitationsPage from '@/pages/Invitations';
import CategoriesPage from '@/pages/Categories';
import AnalyticsPage from '@/pages/Analytics';

import LayoutWrapper from '@/components/Layout/LayoutWrapper';
import ProtectedRoute from '@/components/Auth/ProtectedRoute';
import ErrorFallback from '@/components/Common/ErrorFallback';
import LoadingPage from '@/components/Common/LoadingPage';

// Store
import { useAuthStore } from '@/store/auth';

// 样式
import 'antd/dist/reset.css';
import './App.less';

// 创建React Query客户端
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
      staleTime: 5 * 60 * 1000, // 5分钟
    },
    mutations: {
      retry: 1,
    },
  },
});

// Antd主题配置
const antdTheme = {
  algorithm: theme.defaultAlgorithm,
  token: {
    colorPrimary: '#1890ff',
    colorSuccess: '#52c41a',
    colorWarning: '#faad14',
    colorError: '#f5222d',
    colorInfo: '#1890ff',
    borderRadius: 6,
    wireframe: false,
  },
  components: {
    Layout: {
      headerBg: '#fff',
      siderBg: '#001529',
    },
    Menu: {
      darkItemBg: '#001529',
      darkSubMenuItemBg: '#000c17',
    },
  },
};

const App: React.FC = () => {
  const { isAuthenticated, refreshProfile, loading } = useAuthStore();

  // 应用初始化
  useEffect(() => {
    const initApp = async () => {
      // 如果有token但没有用户信息，尝试获取用户信息
      const token = localStorage.getItem('admin_token');
      if (token && isAuthenticated) {
        try {
          await refreshProfile();
        } catch (error) {
          console.error('获取用户信息失败:', error);
          // 清除无效token
          localStorage.removeItem('admin_token');
        }
      }
    };

    initApp();
  }, [isAuthenticated, refreshProfile]);

  // 加载中状态
  if (loading) {
    return <LoadingPage />;
  }

  return (
    <ErrorBoundary FallbackComponent={ErrorFallback}>
      <HelmetProvider>
        <QueryClientProvider client={queryClient}>
          <ConfigProvider 
            locale={zhCN} 
            theme={antdTheme}
            componentSize="middle"
          >
            <AntdApp>
              <Router>
                <div className="app">
                  <Routes>
                    {/* 登录页面 */}
                    <Route 
                      path="/login" 
                      element={
                        isAuthenticated ? (
                          <Navigate to="/dashboard" replace />
                        ) : (
                          <LoginPage />
                        )
                      } 
                    />

                    {/* 受保护的路由 */}
                    <Route 
                      path="/*" 
                      element={
                        <ProtectedRoute>
                          <LayoutWrapper>
                            <Routes>
                              {/* 仪表板 */}
                              <Route path="/dashboard" element={<DashboardPage />} />

                              {/* 用户管理 */}
                              <Route path="/users" element={<UsersPage />} />

                              {/* 视频管理 */}
                              <Route path="/videos" element={<VideosPage />} />

                              {/* 订单管理 */}
                              <Route path="/orders" element={<OrdersPage />} />

                              {/* 支付管理 */}
                              <Route path="/payment" element={<Navigate to="/payment/recharge" replace />} />
                              <Route path="/payment/recharge" element={<RechargeConfigPage />} />
                              <Route path="/payment/vip" element={<VipConfigPage />} />
                              <Route path="/payment/skus" element={<SkuManagement />} />

                              {/* 邀请系统 */}
                              <Route path="/invitations" element={<Navigate to="/invitations/codes" replace />} />
                              <Route path="/invitations/*" element={<InvitationsPage />} />

                              {/* 分类管理 */}
                              <Route path="/categories" element={<CategoriesPage />} />

                              {/* 数据分析 */}
                              <Route path="/analytics" element={<Navigate to="/analytics/overview" replace />} />
                              <Route path="/analytics/*" element={<AnalyticsPage />} />



                              {/* 系统设置 */}
                              <Route path="/settings" element={<Navigate to="/settings/config" replace />} />
                              <Route path="/settings/*" element={<SettingsPage />} />

                              {/* 默认重定向到仪表板 */}
                              <Route path="/" element={<Navigate to="/dashboard" replace />} />

                              {/* 404页面 */}
                              <Route path="*" element={
                                <div style={{
                                  padding: '50px',
                                  textAlign: 'center',
                                  fontSize: '18px',
                                  color: '#999'
                                }}>
                                  页面不存在
                                </div>
                              } />
                            </Routes>
                          </LayoutWrapper>
                        </ProtectedRoute>
                      } 
                    />
                  </Routes>
                </div>
              </Router>
            </AntdApp>
          </ConfigProvider>
        </QueryClientProvider>
      </HelmetProvider>
    </ErrorBoundary>
  );
};

export default App;
