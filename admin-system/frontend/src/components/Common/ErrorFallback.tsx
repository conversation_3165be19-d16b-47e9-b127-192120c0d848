import React from 'react';
import { Button, Result } from 'antd';
import { ReloadOutlined, BugOutlined } from '@ant-design/icons';

interface ErrorFallbackProps {
  error: Error;
  resetErrorBoundary: () => void;
}

const ErrorFallback: React.FC<ErrorFallbackProps> = ({ 
  error, 
  resetErrorBoundary 
}) => {
  const handleReload = () => {
    window.location.reload();
  };

  return (
    <div className="error-container">
      <Result
        status="error"
        title="系统出现错误"
        subTitle="抱歉，系统遇到了一个意外错误。请尝试刷新页面或联系技术支持。"
        extra={[
          <Button 
            type="primary" 
            key="retry" 
            icon={<ReloadOutlined />}
            onClick={resetErrorBoundary}
          >
            重试
          </Button>,
          <Button 
            key="reload" 
            icon={<ReloadOutlined />}
            onClick={handleReload}
          >
            刷新页面
          </Button>
        ]}
      >
        <div className="error-details">
          <details style={{ whiteSpace: 'pre-wrap', marginTop: 16 }}>
            <summary>
              <BugOutlined style={{ marginRight: 8 }} />
              错误详情
            </summary>
            <div style={{ 
              marginTop: 16, 
              padding: 16, 
              backgroundColor: '#f5f5f5', 
              borderRadius: 4,
              fontSize: 12,
              fontFamily: 'monospace'
            }}>
              {error.message}
              {error.stack && (
                <div style={{ marginTop: 8, opacity: 0.7 }}>
                  {error.stack}
                </div>
              )}
            </div>
          </details>
        </div>
      </Result>
    </div>
  );
};

export default ErrorFallback;
