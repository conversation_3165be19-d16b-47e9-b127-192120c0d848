import React from 'react';
import { Spin } from 'antd';
import { LoadingOutlined } from '@ant-design/icons';

interface LoadingPageProps {
  tip?: string;
  size?: 'small' | 'default' | 'large';
}

const LoadingPage: React.FC<LoadingPageProps> = ({ 
  tip = '加载中...', 
  size = 'large' 
}) => {
  const antIcon = <LoadingOutlined style={{ fontSize: 48 }} spin />;

  return (
    <div className="loading-container">
      <Spin 
        indicator={antIcon} 
        size={size} 
        tip={tip}
      />
    </div>
  );
};

export default LoadingPage;
