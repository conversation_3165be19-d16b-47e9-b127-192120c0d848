import React, { useState } from 'react';
import { Layout, Menu, Button, Avatar, Dropdown, Space, Typography, Breadcrumb } from 'antd';
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  DashboardOutlined,
  UserOutlined,
  VideoCameraOutlined,
  ShoppingCartOutlined,
  SettingOutlined,
  LogoutOutlined,
  BellOutlined,
  TeamOutlined,
  AppstoreOutlined,
  Bar<PERSON>hartOutlined,
  FileTextOutlined,
  PayCircleOutlined
} from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuthStore } from '@/store/auth';
import type { MenuProps } from 'antd';

const { Header, Sider, Content } = Layout;
const { Title } = Typography;

interface LayoutWrapperProps {
  children: React.ReactNode;
}

// 菜单项配置
const menuItems: MenuProps['items'] = [
  {
    key: '/dashboard',
    icon: <DashboardOutlined />,
    label: '仪表板',
  },
  {
    key: '/users',
    icon: <UserOutlined />,
    label: '用户管理',
  },
  {
    key: '/videos',
    icon: <VideoCameraOutlined />,
    label: '视频管理',
  },
  {
    key: '/orders',
    icon: <ShoppingCartOutlined />,
    label: '订单管理',
  },
  {
    key: '/payment',
    icon: <PayCircleOutlined />,
    label: '支付管理',
    children: [
      {
        key: '/payment/recharge',
        label: '充值配置',
      },
      {
        key: '/payment/vip',
        label: 'VIP套餐',
      },
      {
        key: '/payment/skus',
        label: 'SKU管理',
      },
    ],
  },
  {
    key: '/invitations',
    icon: <TeamOutlined />,
    label: '邀请系统',
    children: [
      {
        key: '/invitations/codes',
        label: '邀请码管理',
      },
      {
        key: '/invitations/relations',
        label: '邀请关系',
      },
      {
        key: '/invitations/commissions',
        label: '佣金管理',
      },
    ],
  },
  {
    key: '/categories',
    icon: <AppstoreOutlined />,
    label: '分类管理',
  },
  {
    key: '/analytics',
    icon: <BarChartOutlined />,
    label: '数据分析',
    children: [
      {
        key: '/analytics/overview',
        label: '数据概览',
      },
      {
        key: '/analytics/users',
        label: '用户分析',
      },
      {
        key: '/analytics/videos',
        label: '视频分析',
      },
      {
        key: '/analytics/revenue',
        label: '收入分析',
      },
    ],
  },
  {
    key: '/settings',
    icon: <SettingOutlined />,
    label: '系统设置',
    children: [
      {
        key: '/settings/config',
        label: '基础配置',
      },
      {
        key: '/settings/admins',
        label: '管理员管理',
      },
      {
        key: '/settings/logs',
        label: '操作日志',
      },
    ],
  },
];

// 面包屑映射
const breadcrumbMap: Record<string, string> = {
  '/dashboard': '仪表板',
  '/users': '用户管理',
  '/videos': '视频管理',
  '/orders': '订单管理',
  '/payment': '支付管理',
  '/payment/recharge': '充值配置',
  '/payment/vip': 'VIP套餐',
  '/payment/skus': 'SKU管理',
  '/invitations': '邀请系统',
  '/invitations/codes': '邀请码管理',
  '/invitations/relations': '邀请关系',
  '/invitations/commissions': '佣金管理',
  '/categories': '分类管理',
  '/analytics': '数据分析',
  '/analytics/overview': '数据概览',
  '/analytics/users': '用户分析',
  '/analytics/videos': '视频分析',
  '/analytics/revenue': '收入分析',
  '/settings': '系统设置',
  '/settings/config': '基础配置',
  '/settings/admins': '管理员管理',
  '/settings/logs': '操作日志',
};

const LayoutWrapper: React.FC<LayoutWrapperProps> = ({ children }) => {
  const [collapsed, setCollapsed] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const { admin, logout } = useAuthStore();

  // 处理菜单点击
  const handleMenuClick = ({ key }: { key: string }) => {
    navigate(key);
  };

  // 处理用户菜单点击
  const handleUserMenuClick = ({ key }: { key: string }) => {
    switch (key) {
      case 'profile':
        // 打开个人资料模态框
        break;
      case 'logout':
        logout();
        navigate('/login');
        break;
    }
  };

  // 用户下拉菜单
  const userMenuItems: MenuProps['items'] = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人资料',
    },
    {
      type: 'divider',
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
    },
  ];

  // 生成面包屑
  const generateBreadcrumb = () => {
    const pathSnippets = location.pathname.split('/').filter(i => i);
    const breadcrumbItems = [
      {
        title: '首页',
        href: '/dashboard',
      }
    ];

    let currentPath = '';
    pathSnippets.forEach((snippet) => {
      currentPath += `/${snippet}`;
      const title = breadcrumbMap[currentPath];
      if (title) {
        breadcrumbItems.push({
          title,
          href: currentPath === location.pathname ? undefined : currentPath,
        });
      }
    });

    return breadcrumbItems;
  };

  return (
    <Layout style={{ minHeight: '100vh' }}>
      {/* 侧边栏 */}
      <Sider
        trigger={null}
        collapsible
        collapsed={collapsed}
        style={{
          overflow: 'auto',
          height: '100vh',
          position: 'fixed',
          left: 0,
          top: 0,
          bottom: 0,
        }}
      >
        <div style={{
          height: '64px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          borderBottom: '1px solid #f0f0f0'
        }}>
          <Title
            level={4}
            style={{
              color: 'white',
              margin: 0,
              fontSize: collapsed ? '16px' : '18px'
            }}
          >
            {collapsed ? 'SV' : 'SmartVideo'}
          </Title>
        </div>
        <Menu
          theme="dark"
          mode="inline"
          selectedKeys={[location.pathname]}
          items={menuItems}
          onClick={handleMenuClick}
        />
      </Sider>

      {/* 主要内容区域 */}
      <Layout style={{ marginLeft: collapsed ? 80 : 200, transition: 'margin-left 0.2s' }}>
        {/* 顶部导航栏 */}
        <Header style={{
          padding: '0 24px',
          background: '#fff',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          borderBottom: '1px solid #f0f0f0'
        }}>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <Button
              type="text"
              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={() => setCollapsed(!collapsed)}
              style={{ fontSize: '16px', width: 64, height: 64 }}
            />
            <Breadcrumb
              items={generateBreadcrumb()}
              style={{ marginLeft: '16px' }}
            />
          </div>

          <Space>
            <Button
              type="text"
              icon={<BellOutlined />}
              style={{ fontSize: '16px' }}
            />
            <Dropdown
              menu={{
                items: userMenuItems,
                onClick: handleUserMenuClick
              }}
              placement="bottomRight"
            >
              <Space style={{ cursor: 'pointer' }}>
                <Avatar
                  src={admin?.avatar}
                  icon={<UserOutlined />}
                  size="small"
                />
                <span>{admin?.nickname || admin?.username}</span>
              </Space>
            </Dropdown>
          </Space>
        </Header>

        {/* 内容区域 */}
        <Content style={{
          margin: 0,
          minHeight: 'calc(100vh - 64px)',
          background: '#f0f2f5'
        }}>
          {children}
        </Content>
      </Layout>
    </Layout>
  );
};

export default LayoutWrapper;
