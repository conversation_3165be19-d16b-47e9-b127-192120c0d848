import React, { useState, useEffect } from 'react';
import {
  Card,
  Tabs,
  Form,
  Input,
  Button,
  Switch,
  InputNumber,
  Select,
  Upload,
  message,
  Divider,
  Space,
  Table,
  Modal,
  Tag,
  Popconfirm
} from 'antd';
import {
  SaveOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { api } from '@/services/api';
import type { ColumnsType } from 'antd/es/table';
import { useNavigate, useLocation } from 'react-router-dom';

const { TabPane } = Tabs;
const { Option } = Select;

interface SystemConfig {
  siteName: string;
  siteDescription: string;
  siteLogo: string;
  contactEmail: string;
  contactPhone: string;
  enableRegistration: boolean;
  enableEmailVerification: boolean;
  defaultCoins: number;
  coinToYuan: number;
  vipPrices: {
    daily: number;
    weekly: number;
    monthly: number;
    yearly: number;
  };
}

interface Admin {
  id: number;
  username: string;
  email: string;
  nickname: string;
  role: string;
  isActive: boolean;
  lastLoginAt: string;
  createdAt: string;
}

interface OperationLog {
  id: number;
  adminUsername: string;
  action: string;
  resource: string;
  details: string;
  ip: string;
  createdAt: string;
}

const SettingsPage: React.FC = () => {
  const [configForm] = Form.useForm();
  const [adminForm] = Form.useForm();
  const [isAdminModalVisible, setIsAdminModalVisible] = useState(false);
  const [editingAdmin, setEditingAdmin] = useState<Admin | null>(null);
  const [activeTab, setActiveTab] = useState('config');
  const queryClient = useQueryClient();
  const navigate = useNavigate();
  const location = useLocation();

  // 根据URL路径设置活动标签页
  useEffect(() => {
    const path = location.pathname;
    if (path === '/settings/config') {
      setActiveTab('config');
    } else if (path === '/settings/admins') {
      setActiveTab('admins');
    } else if (path === '/settings/logs') {
      setActiveTab('logs');
    } else {
      setActiveTab('config');
    }
  }, [location.pathname]);

  // 处理标签页切换
  const handleTabChange = (key: string) => {
    setActiveTab(key);
    switch (key) {
      case 'config':
        navigate('/settings/config');
        break;
      case 'admins':
        navigate('/settings/admins');
        break;
      case 'logs':
        navigate('/settings/logs');
        break;
      default:
        navigate('/settings/config');
    }
  };

  // 获取系统配置
  const { data: configData, isLoading: configLoading } = useQuery(
    'system-config',
    () => api.get('/settings/config')
  );

  // 获取管理员列表
  const { data: adminsData, isLoading: adminsLoading } = useQuery(
    'admins',
    () => api.get('/settings/admins')
  );

  // 获取操作日志
  const { data: logsData, isLoading: logsLoading } = useQuery(
    'operation-logs',
    () => api.get('/settings/logs')
  );



  // 更新系统配置
  const updateConfigMutation = useMutation(
    (data: SystemConfig) => api.put('/settings/config', data),
    {
      onSuccess: () => {
        message.success('配置更新成功');
        queryClient.invalidateQueries('system-config');
      },
      onError: () => {
        message.error('配置更新失败');
      }
    }
  );

  // 创建/更新管理员
  const saveAdminMutation = useMutation(
    ({ id, data }: { id?: number; data: any }) => 
      id ? api.put(`/settings/admins/${id}`, data) : api.post('/settings/admins', data),
    {
      onSuccess: () => {
        message.success(editingAdmin ? '更新成功' : '创建成功');
        queryClient.invalidateQueries('admins');
        setIsAdminModalVisible(false);
        setEditingAdmin(null);
        adminForm.resetFields();
      },
      onError: () => {
        message.error(editingAdmin ? '更新失败' : '创建失败');
      }
    }
  );

  // 删除管理员
  const deleteAdminMutation = useMutation(
    (id: number) => api.delete(`/settings/admins/${id}`),
    {
      onSuccess: () => {
        message.success('删除成功');
        queryClient.invalidateQueries('admins');
      },
      onError: () => {
        message.error('删除失败');
      }
    }
  );



  // 管理员表格列
  const adminColumns: ColumnsType<Admin> = [
    {
      title: 'ID',
      dataIndex: 'id',
      width: 80,
    },
    {
      title: '用户名',
      dataIndex: 'username',
      width: 120,
    },
    {
      title: '昵称',
      dataIndex: 'nickname',
      width: 120,
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      width: 200,
    },
    {
      title: '角色',
      dataIndex: 'role',
      width: 100,
      render: (role: string) => (
        <Tag color={role === 'super_admin' ? 'red' : 'blue'}>
          {role === 'super_admin' ? '超级管理员' : '管理员'}
        </Tag>
      ),
    },
    {
      title: '状态',
      dataIndex: 'isActive',
      width: 80,
      render: (isActive: boolean) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? '启用' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '最后登录',
      dataIndex: 'lastLoginAt',
      width: 180,
      render: (date: string) => date ? new Date(date).toLocaleString() : '从未登录',
    },
    {
      title: '操作',
      key: 'actions',
      width: 150,
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEditAdmin(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个管理员吗？"
            onConfirm={() => deleteAdminMutation.mutate(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              size="small"
              danger
              icon={<DeleteOutlined />}
              disabled={record.role === 'super_admin'}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 操作日志表格列
  const logColumns: ColumnsType<OperationLog> = [
    {
      title: '管理员',
      dataIndex: 'adminUsername',
      width: 120,
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 120,
    },
    {
      title: '资源',
      dataIndex: 'resource',
      width: 120,
    },
    {
      title: '详情',
      dataIndex: 'details',
      ellipsis: true,
    },
    {
      title: 'IP地址',
      dataIndex: 'ip',
      width: 120,
    },
    {
      title: '时间',
      dataIndex: 'createdAt',
      width: 180,
      render: (date: string) => new Date(date).toLocaleString(),
    },
  ];

  // 处理配置保存
  const handleConfigSave = (values: SystemConfig) => {
    updateConfigMutation.mutate(values);
  };

  // 处理编辑管理员
  const handleEditAdmin = (admin: Admin) => {
    setEditingAdmin(admin);
    adminForm.setFieldsValue(admin);
    setIsAdminModalVisible(true);
  };

  // 处理管理员保存
  const handleAdminSave = (values: any) => {
    saveAdminMutation.mutate({
      id: editingAdmin?.id,
      data: values
    });
  };



  // 当配置数据加载完成后，更新表单值
  useEffect(() => {
    if (configData?.data) {
      const config = configData.data.data || configData.data;
      configForm.setFieldsValue(config);
    }
  }, [configData, configForm]);



  // 安全获取数组数据的辅助函数
  const getArrayData = (data: any, fallback: any[] = []) => {
    if (Array.isArray(data?.data?.data)) return data.data.data;
    if (Array.isArray(data?.data)) return data.data;
    if (Array.isArray(data)) return data;
    return fallback;
  };

  return (
    <div style={{ padding: '24px' }}>
      <Card>
        <Tabs
          activeKey={activeTab}
          onChange={handleTabChange}
        >
          {/* 基础配置 */}
          <TabPane tab="基础配置" key="config">
            <Form
              form={configForm}
              layout="vertical"
              initialValues={configData?.data?.data || configData?.data}
              onFinish={handleConfigSave}
              style={{ maxWidth: 600 }}
            >
              <Form.Item
                name="siteName"
                label="网站名称"
                rules={[{ required: true, message: '请输入网站名称' }]}
              >
                <Input />
              </Form.Item>
              
              <Form.Item
                name="siteDescription"
                label="网站描述"
              >
                <Input.TextArea rows={3} />
              </Form.Item>

              <Form.Item
                name="contactEmail"
                label="联系邮箱"
                rules={[{ type: 'email', message: '请输入有效的邮箱地址' }]}
              >
                <Input />
              </Form.Item>

              <Form.Item
                name="contactPhone"
                label="联系电话"
              >
                <Input />
              </Form.Item>

              <Divider>功能设置</Divider>

              <Form.Item
                name="enableRegistration"
                label="启用用户注册"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>

              <Form.Item
                name="enableEmailVerification"
                label="启用邮箱验证"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>

              <Divider>经济设置</Divider>

              <Form.Item
                name="defaultCoins"
                label="新用户默认金币"
                rules={[{ required: true, message: '请输入默认金币数量' }]}
              >
                <InputNumber min={0} style={{ width: '100%' }} />
              </Form.Item>

              <Form.Item
                name="coinToYuan"
                label="金币兑换比例（1元=?币）"
                rules={[{ required: true, message: '请输入兑换比例' }]}
              >
                <InputNumber min={1} style={{ width: '100%' }} />
              </Form.Item>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  icon={<SaveOutlined />}
                  loading={updateConfigMutation.isLoading}
                >
                  保存配置
                </Button>
              </Form.Item>
            </Form>
          </TabPane>

          {/* 管理员管理 */}
          <TabPane tab="管理员管理" key="admins">
            <div style={{ marginBottom: 16 }}>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => {
                  setEditingAdmin(null);
                  adminForm.resetFields();
                  setIsAdminModalVisible(true);
                }}
              >
                添加管理员
              </Button>
            </div>

            <Table
              columns={adminColumns}
              dataSource={getArrayData(adminsData)}
              rowKey="id"
              loading={adminsLoading}
              pagination={{
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
              }}
            />
          </TabPane>







          {/* 操作日志 */}
          <TabPane tab="操作日志" key="logs">
            <Table
              columns={logColumns}
              dataSource={getArrayData({ data: logsData?.data?.data?.items || logsData?.data?.items }, [])}
              rowKey="id"
              loading={logsLoading}
              pagination={{
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
              }}
            />
          </TabPane>
        </Tabs>
      </Card>

      {/* 管理员编辑模态框 */}
      <Modal
        title={editingAdmin ? '编辑管理员' : '添加管理员'}
        open={isAdminModalVisible}
        onCancel={() => {
          setIsAdminModalVisible(false);
          setEditingAdmin(null);
          adminForm.resetFields();
        }}
        onOk={() => adminForm.submit()}
        confirmLoading={saveAdminMutation.isLoading}
      >
        <Form
          form={adminForm}
          layout="vertical"
          onFinish={handleAdminSave}
        >
          <Form.Item
            name="username"
            label="用户名"
            rules={[{ required: true, message: '请输入用户名' }]}
          >
            <Input disabled={!!editingAdmin} />
          </Form.Item>

          <Form.Item
            name="nickname"
            label="昵称"
            rules={[{ required: true, message: '请输入昵称' }]}
          >
            <Input />
          </Form.Item>

          <Form.Item
            name="email"
            label="邮箱"
            rules={[
              { required: true, message: '请输入邮箱' },
              { type: 'email', message: '请输入有效的邮箱地址' }
            ]}
          >
            <Input />
          </Form.Item>

          {!editingAdmin && (
            <Form.Item
              name="password"
              label="密码"
              rules={[{ required: true, message: '请输入密码' }]}
            >
              <Input.Password />
            </Form.Item>
          )}

          <Form.Item
            name="role"
            label="角色"
            rules={[{ required: true, message: '请选择角色' }]}
          >
            <Select>
              <Option value="admin">管理员</Option>
              <Option value="super_admin">超级管理员</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="isActive"
            label="状态"
            valuePropName="checked"
          >
            <Switch checkedChildren="启用" unCheckedChildren="禁用" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default SettingsPage;
