import React, { useState } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Modal,
  Form,
  Input,
  InputNumber,
  Switch,
  Select,
  message,
  Popconfirm,
  Tag,
  Tree,
  Row,
  Col
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  DragOutlined,
  FolderOutlined,
  FileOutlined
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { apiService } from '@/services/api';
import type { ColumnsType } from 'antd/es/table';

// 分类接口
interface Category {
  id: number;
  name: string;
  slug: string;
  description: string;
  parentId: number | null;
  sort: number;
  isActive: boolean;
  videoCount: number;
  createdAt: string;
  updatedAt: string;
  children?: Category[];
}

const CategoriesPage: React.FC = () => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingCategory, setEditingCategory] = useState<Category | null>(null);
  const [viewMode, setViewMode] = useState<'table' | 'tree'>('table');
  const [form] = Form.useForm();
  const queryClient = useQueryClient();

  // 分类列表查询
  const { data: categoriesData, isLoading, error } = useQuery(
    ['categories'],
    () => apiService.getCategories()
  );

  // 创建/更新分类
  const saveCategoryMutation = useMutation(
    (data: any) => {
      if (editingCategory) {
        return apiService.updateCategory(editingCategory.id, data);
      } else {
        return apiService.createCategory(data);
      }
    },
    {
      onSuccess: () => {
        message.success(editingCategory ? '更新成功' : '创建成功');
        setIsModalVisible(false);
        setEditingCategory(null);
        form.resetFields();
        queryClient.invalidateQueries(['categories']);
      },
      onError: (error: any) => {
        message.error(error.response?.data?.message || '操作失败');
      }
    }
  );

  // 删除分类
  const deleteCategoryMutation = useMutation(
    (id: number) => apiService.deleteCategory(id),
    {
      onSuccess: () => {
        message.success('删除成功');
        queryClient.invalidateQueries(['categories']);
      },
      onError: (error: any) => {
        message.error(error.response?.data?.message || '删除失败');
      }
    }
  );

  // 表格列定义
  const columns: ColumnsType<Category> = [
    {
      title: '分类名称',
      dataIndex: 'name',
      key: 'name',
      render: (name: string, record) => (
        <Space>
          {record.parentId ? <FileOutlined /> : <FolderOutlined />}
          <span style={{ fontWeight: record.parentId ? 'normal' : 'bold' }}>
            {name}
          </span>
        </Space>
      ),
    },
    {
      title: '标识符',
      dataIndex: 'slug',
      key: 'slug',
      render: (slug: string) => (
        <code style={{ background: '#f5f5f5', padding: '2px 6px', borderRadius: '4px' }}>
          {slug}
        </code>
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: '父分类',
      dataIndex: 'parentId',
      key: 'parentId',
      render: (parentId: number | null) => {
        if (!parentId) return '-';
        const parent = getArrayData(categoriesData)?.find((cat: Category) => cat.id === parentId);
        return parent ? parent.name : `ID: ${parentId}`;
      },
    },
    {
      title: '排序',
      dataIndex: 'sort',
      key: 'sort',
      width: 80,
      sorter: (a, b) => a.sort - b.sort,
    },
    {
      title: '视频数量',
      dataIndex: 'videoCount',
      key: 'videoCount',
      width: 100,
      render: (count: number) => (
        <Tag color={count > 0 ? 'blue' : 'default'}>{count}</Tag>
      ),
    },
    {
      title: '状态',
      dataIndex: 'isActive',
      key: 'isActive',
      width: 80,
      render: (isActive: boolean) => (
        <Tag color={isActive ? 'success' : 'error'}>
          {isActive ? '启用' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个分类吗？"
            description={record.videoCount > 0 ? '该分类下还有视频，删除后视频将变为未分类' : undefined}
            onConfirm={() => deleteCategoryMutation.mutate(record.id)}
          >
            <Button
              type="link"
              size="small"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 处理编辑
  const handleEdit = (record: Category) => {
    setEditingCategory(record);
    form.setFieldsValue(record);
    setIsModalVisible(true);
  };

  // 处理保存
  const handleSave = (values: any) => {
    saveCategoryMutation.mutate(values);
  };

  // 安全获取数组数据的辅助函数
  const getArrayData = (data: any, fallback: any[] = []) => {
    // apiService.getCategories() 直接返回数组
    if (Array.isArray(data)) return data;
    // 兼容其他可能的数据结构
    if (Array.isArray(data?.data?.categories)) return data.data.categories;
    if (Array.isArray(data?.data?.data)) return data.data.data;
    if (Array.isArray(data?.data)) return data.data;
    return fallback;
  };

  // 构建树形数据
  const buildTreeData = (categories: Category[]) => {
    const map = new Map();
    const roots: any[] = [];

    // 创建映射
    categories.forEach(cat => {
      map.set(cat.id, {
        key: cat.id,
        title: cat.name,
        icon: cat.parentId ? <FileOutlined /> : <FolderOutlined />,
        children: [],
        ...cat
      });
    });

    // 构建树形结构
    categories.forEach(cat => {
      const node = map.get(cat.id);
      if (cat.parentId && map.has(cat.parentId)) {
        map.get(cat.parentId).children.push(node);
      } else {
        roots.push(node);
      }
    });

    return roots;
  };

  const categories = getArrayData(categoriesData);
  const treeData = buildTreeData(categories);



  return (
    <div style={{ padding: '24px' }}>
      <Card>
        <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Space>
            <Button
              type={viewMode === 'table' ? 'primary' : 'default'}
              onClick={() => setViewMode('table')}
            >
              表格视图
            </Button>
            <Button
              type={viewMode === 'tree' ? 'primary' : 'default'}
              onClick={() => setViewMode('tree')}
            >
              树形视图
            </Button>
          </Space>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => {
              setEditingCategory(null);
              form.resetFields();
              setIsModalVisible(true);
            }}
          >
            添加分类
          </Button>
        </div>

        {viewMode === 'table' ? (
          <Table
            columns={columns}
            dataSource={categories}
            rowKey="id"
            loading={isLoading}
            pagination={{
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
            }}
          />
        ) : (
          <Row gutter={16}>
            <Col span={12}>
              <Card title="分类树形结构" size="small">
                <Tree
                  showIcon
                  defaultExpandAll
                  treeData={treeData}
                  height={400}
                />
              </Card>
            </Col>
            <Col span={12}>
              <Card title="分类统计" size="small">
                <div style={{ padding: '16px' }}>
                  <p>总分类数: {categories.length}</p>
                  <p>顶级分类: {categories.filter(cat => !cat.parentId).length}</p>
                  <p>子分类: {categories.filter(cat => cat.parentId).length}</p>
                  <p>启用分类: {categories.filter(cat => cat.isActive).length}</p>
                  <p>总视频数: {categories.reduce((sum, cat) => sum + cat.videoCount, 0)}</p>
                </div>
              </Card>
            </Col>
          </Row>
        )}
      </Card>

      {/* 分类编辑模态框 */}
      <Modal
        title={editingCategory ? '编辑分类' : '添加分类'}
        open={isModalVisible}
        onCancel={() => {
          setIsModalVisible(false);
          setEditingCategory(null);
          form.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSave}
        >
          <Form.Item
            name="name"
            label="分类名称"
            rules={[{ required: true, message: '请输入分类名称' }]}
          >
            <Input placeholder="请输入分类名称" />
          </Form.Item>

          <Form.Item
            name="slug"
            label="标识符"
            rules={[
              { required: true, message: '请输入标识符' },
              { pattern: /^[a-z0-9-_]+$/, message: '只能包含小写字母、数字、连字符和下划线' }
            ]}
          >
            <Input placeholder="例如: movie, tv-series" />
          </Form.Item>

          <Form.Item
            name="description"
            label="描述"
          >
            <Input.TextArea rows={3} placeholder="请输入分类描述" />
          </Form.Item>

          <Form.Item
            name="parentId"
            label="父分类"
          >
            <Select placeholder="选择父分类，不选择表示顶级分类" allowClear>
              {categories
                .filter(cat => !editingCategory || cat.id !== editingCategory.id)
                .filter(cat => !cat.parentId) // 只显示顶级分类作为父分类选项
                .map(cat => (
                  <Select.Option key={cat.id} value={cat.id}>
                    {cat.name}
                  </Select.Option>
                ))}
            </Select>
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="sort"
                label="排序"
                initialValue={0}
              >
                <InputNumber min={0} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="isActive"
                label="状态"
                valuePropName="checked"
                initialValue={true}
              >
                <Switch checkedChildren="启用" unCheckedChildren="禁用" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setIsModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit" loading={saveCategoryMutation.isLoading}>
                {editingCategory ? '更新' : '创建'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default CategoriesPage;
