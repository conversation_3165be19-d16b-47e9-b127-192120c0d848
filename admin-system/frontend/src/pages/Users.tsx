import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Input,
  Select,
  Space,
  Tag,
  Modal,
  Form,
  InputNumber,
  Switch,
  DatePicker,
  message,
  Popconfirm,
  Avatar,
  Typography,
  Row,
  Col,
  Statistic
} from 'antd';
import {
  SearchOutlined,
  EditOutlined,
  EyeOutlined,
  UserOutlined,
  CrownOutlined,
  DollarOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import { api } from '../services/api';

const { Search } = Input;
const { Option } = Select;
const { Title } = Typography;
const { RangePicker } = DatePicker;

interface User {
  id: number;
  phone: string | null;
  email: string | null;
  nickname: string;
  avatar: string | null;
  bio: string | null;
  coins: number;
  isVip: number; // 0 or 1
  vipExpireAt: string | null;
  lastLoginAt: string | null;
  isGuest: number; // 0 or 1
  guestId: string | null;
  deviceId: string | null;
  emailVerified: number; // 0 or 1
  isMerged: number; // 0 or 1
  mergedToUserId: number | null;
  mergedAt: string | null;
  inviteCode: string | null;
  invitedBy: number | null;
  inviteLevel: number;
  totalInvites: number;
  totalCommission: string;
  createdAt: string;
  updatedAt: string;
  // 可选的计算字段
  totalSpent?: number;
  totalOrders?: number;
  totalFavorites?: number;
  totalPurchased?: number;
}

interface UserStats {
  totalUsers: number;
  activeUsers: number;
  vipUsers: number;
  guestUsers: number;
  todayNewUsers: number;
  todayActiveUsers: number;
}

const UsersPage: React.FC = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [stats, setStats] = useState<UserStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [keyword, setKeyword] = useState('');
  const [roleFilter, setRoleFilter] = useState<string>('');
  const [statusFilter, setStatusFilter] = useState<boolean | undefined>(undefined);
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs] | null>(null);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [form] = Form.useForm();

  // 获取用户列表
  const fetchUsers = async () => {
    try {
      setLoading(true);
      const params: any = {
        page: currentPage,
        pageSize,
        keyword,
        sortBy: 'createdAt',
        sortOrder: 'DESC'
      };

      if (roleFilter) params.role = roleFilter;
      if (statusFilter !== undefined) params.isActive = statusFilter;
      if (dateRange) {
        params.dateRange = [
          dateRange[0].format('YYYY-MM-DD'),
          dateRange[1].format('YYYY-MM-DD')
        ];
      }

      const response = await api.get('/users', { params });
      if (response.data.success) {
        setUsers(response.data.data.items);
        setTotal(response.data.data.total);
      }
    } catch (error) {
      console.error('获取用户列表失败:', error);
      message.error('获取用户列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取用户统计
  const fetchStats = async () => {
    try {
      const response = await api.get('/users/stats');
      if (response.data.success) {
        setStats(response.data.data);
      }
    } catch (error) {
      console.error('获取用户统计失败:', error);
    }
  };

  // 获取用户详情
  const fetchUserDetail = async (userId: number) => {
    try {
      const response = await api.get(`/users/${userId}`);
      if (response.data.success) {
        setCurrentUser(response.data.data);
        setDetailModalVisible(true);
      }
    } catch (error) {
      console.error('获取用户详情失败:', error);
      message.error('获取用户详情失败');
    }
  };

  // 更新用户信息
  const updateUser = async (values: any) => {
    try {
      if (!currentUser) return;

      // 清理数据，移除空值和null值
      const cleanedValues: any = {};
      Object.entries(values).forEach(([key, value]) => {
        if (value !== null && value !== undefined && value !== '') {
          cleanedValues[key] = value;
        }
      });

      const response = await api.put(`/users/${currentUser.id}`, cleanedValues);
      if (response.data.success) {
        message.success('用户信息更新成功');
        setEditModalVisible(false);
        fetchUsers();
        fetchStats();
      }
    } catch (error: any) {
      console.error('更新用户信息失败:', error);
      message.error(error.response?.data?.message || '更新用户信息失败');
    }
  };

  useEffect(() => {
    fetchUsers();
    fetchStats();
  }, [currentPage, pageSize, keyword, roleFilter, statusFilter, dateRange]);

  // 表格列定义
  const columns: ColumnsType<User> = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '用户信息',
      key: 'userInfo',
      width: 200,
      render: (_, record) => (
        <Space>
          <Avatar 
            src={record.avatar} 
            icon={<UserOutlined />}
            size="small"
          />
          <div>
            <div style={{ fontWeight: 'bold' }}>{record.nickname || `用户${record.id}`}</div>
            <div style={{ fontSize: '12px', color: '#666' }}>
              {record.email || record.phone || (record.guestId ? `游客ID: ${record.guestId.slice(0, 8)}...` : '-')}
            </div>
          </div>
        </Space>
      ),
    },
    {
      title: '用户类型',
      key: 'userType',
      width: 120,
      render: (_, record) => {
        if (record.isGuest) {
          return <Tag color="default">游客</Tag>;
        } else if (record.isVip) {
          return <Tag color="gold" icon={<CrownOutlined />}>VIP</Tag>;
        } else {
          return <Tag color="blue">普通用户</Tag>;
        }
      },
    },
    {
      title: '金币余额',
      dataIndex: 'coins',
      key: 'coins',
      width: 100,
      render: (coins) => (
        <span style={{ color: '#faad14', fontWeight: 'bold' }}>
          <DollarOutlined /> {coins}
        </span>
      ),
    },
    {
      title: '总消费',
      dataIndex: 'totalSpent',
      key: 'totalSpent',
      width: 100,
      render: (amount) => `¥${amount || 0}`,
    },
    {
      title: '状态',
      key: 'status',
      width: 80,
      render: (_, record) => {
        // 由于数据库中没有isActive字段，我们根据其他条件判断状态
        // 如果用户有guestId，说明是正常的游客用户
        // 如果用户没有被合并(isMerged=0)，说明是正常状态
        const isActive = record.isMerged === 0;
        return (
          <Tag color={isActive ? 'success' : 'error'}>
            {isActive ? '正常' : '已合并'}
          </Tag>
        );
      },
    },
    {
      title: '最后登录',
      dataIndex: 'lastLoginAt',
      key: 'lastLoginAt',
      width: 150,
      render: (date) => date ? dayjs(date).format('YYYY-MM-DD HH:mm') : '-',
    },
    {
      title: '注册时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 150,
      render: (date) => dayjs(date).format('YYYY-MM-DD HH:mm'),
    },
    {
      title: '操作',
      key: 'actions',
      width: 150,
      fixed: 'right',
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => fetchUserDetail(record.id)}
          >
            详情
          </Button>
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => {
              setCurrentUser(record);
              form.setFieldsValue({
                nickname: record.nickname,
                email: record.email,
                isVip: Boolean(record.isVip),
                vipExpireAt: record.vipExpireAt ? dayjs(record.vipExpireAt) : null,
                coins: record.coins
              });
              setEditModalVisible(true);
            }}
          >
            编辑
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>用户管理</Title>

      {/* 统计卡片 */}
      {stats && (
        <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
          <Col xs={24} sm={8} lg={4}>
            <Card>
              <Statistic
                title="总用户数"
                value={stats.totalUsers}
                prefix={<UserOutlined />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={8} lg={4}>
            <Card>
              <Statistic
                title="活跃用户"
                value={stats.activeUsers}
                prefix={<UserOutlined />}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={8} lg={4}>
            <Card>
              <Statistic
                title="VIP用户"
                value={stats.vipUsers}
                prefix={<CrownOutlined />}
                valueStyle={{ color: '#faad14' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={8} lg={4}>
            <Card>
              <Statistic
                title="游客用户"
                value={stats.guestUsers}
                prefix={<UserOutlined />}
                valueStyle={{ color: '#d9d9d9' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={8} lg={4}>
            <Card>
              <Statistic
                title="今日新增"
                value={stats.todayNewUsers}
                prefix={<UserOutlined />}
                valueStyle={{ color: '#722ed1' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={8} lg={4}>
            <Card>
              <Statistic
                title="今日活跃"
                value={stats.todayActiveUsers}
                prefix={<UserOutlined />}
                valueStyle={{ color: '#13c2c2' }}
              />
            </Card>
          </Col>
        </Row>
      )}

      {/* 搜索和筛选 */}
      <Card style={{ marginBottom: '16px' }}>
        <Space wrap>
          <Search
            placeholder="搜索用户名、邮箱、昵称"
            allowClear
            style={{ width: 250 }}
            onSearch={setKeyword}
            enterButton={<SearchOutlined />}
          />
          <Select
            placeholder="用户类型"
            allowClear
            style={{ width: 120 }}
            value={roleFilter}
            onChange={setRoleFilter}
          >
            <Option value="guest">游客</Option>
            <Option value="user">普通用户</Option>
            <Option value="vip">VIP用户</Option>
          </Select>
          <Select
            placeholder="状态"
            allowClear
            style={{ width: 100 }}
            value={statusFilter}
            onChange={setStatusFilter}
          >
            <Option value={0}>正常</Option>
            <Option value={1}>已合并</Option>
          </Select>
          <RangePicker
            placeholder={['开始日期', '结束日期']}
            value={dateRange}
            onChange={setDateRange}
          />
          <Button
            icon={<ReloadOutlined />}
            onClick={() => {
              setKeyword('');
              setRoleFilter('');
              setStatusFilter(undefined);
              setDateRange(null);
              setCurrentPage(1);
            }}
          >
            重置
          </Button>
        </Space>
      </Card>

      {/* 用户表格 */}
      <Card>
        <Table
          columns={columns}
          dataSource={users}
          rowKey="id"
          loading={loading}
          scroll={{ x: 1200 }}
          pagination={{
            current: currentPage,
            pageSize,
            total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            onChange: (page, size) => {
              setCurrentPage(page);
              setPageSize(size || 20);
            },
          }}
        />
      </Card>

      {/* 编辑用户模态框 */}
      <Modal
        title="编辑用户信息"
        open={editModalVisible}
        onCancel={() => setEditModalVisible(false)}
        onOk={() => form.submit()}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={updateUser}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="昵称"
                name="nickname"
                rules={[{ max: 100, message: '昵称不能超过100个字符' }]}
              >
                <Input placeholder="请输入昵称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="邮箱"
                name="email"
                rules={[
                  { type: 'email', message: '请输入有效的邮箱地址' },
                  { max: 255, message: '邮箱不能超过255个字符' }
                ]}
              >
                <Input placeholder="请输入邮箱" />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="金币余额"
                name="coins"
                rules={[{ type: 'number', min: 0, message: '金币余额不能为负数' }]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="请输入金币余额"
                  min={0}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="VIP到期时间"
                name="vipExpireAt"
              >
                <DatePicker
                  style={{ width: '100%' }}
                  placeholder="请选择VIP到期时间"
                  showTime
                />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="VIP状态"
                name="isVip"
                valuePropName="checked"
              >
                <Switch checkedChildren="是" unCheckedChildren="否" />
              </Form.Item>
            </Col>

          </Row>
        </Form>
      </Modal>

      {/* 用户详情模态框 */}
      <Modal
        title="用户详情"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={null}
        width={800}
      >
        {currentUser && (
          <div>
            <Row gutter={[16, 16]}>
              <Col span={24}>
                <Card title="基本信息" size="small">
                  <Row gutter={16}>
                    <Col span={8}>
                      <div style={{ textAlign: 'center' }}>
                        <Avatar
                          src={currentUser.avatar}
                          icon={<UserOutlined />}
                          size={80}
                        />
                        <div style={{ marginTop: '8px', fontWeight: 'bold' }}>
                          {currentUser.nickname || `用户${currentUser.id}`}
                        </div>
                      </div>
                    </Col>
                    <Col span={16}>
                      <Space direction="vertical" style={{ width: '100%' }}>
                        <div><strong>用户ID:</strong> {currentUser.id}</div>
                        <div><strong>昵称:</strong> {currentUser.nickname || '-'}</div>
                        <div><strong>邮箱:</strong> {currentUser.email || '-'}</div>
                        <div>
                          <strong>用户类型:</strong>
                          {currentUser.isGuest ? (
                            <Tag color="default" style={{ marginLeft: '8px' }}>游客</Tag>
                          ) : currentUser.isVip ? (
                            <Tag color="gold" icon={<CrownOutlined />} style={{ marginLeft: '8px' }}>VIP</Tag>
                          ) : (
                            <Tag color="blue" style={{ marginLeft: '8px' }}>普通用户</Tag>
                          )}
                        </div>
                        <div>
                          <strong>账户状态:</strong>
                          <Tag
                            color={currentUser.isMerged === 0 ? 'success' : 'error'}
                            style={{ marginLeft: '8px' }}
                          >
                            {currentUser.isMerged === 0 ? '正常' : '已合并'}
                          </Tag>
                        </div>
                      </Space>
                    </Col>
                  </Row>
                </Card>
              </Col>
            </Row>

            <Row gutter={[16, 16]} style={{ marginTop: '16px' }}>
              <Col span={12}>
                <Card title="财务信息" size="small">
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <div>
                      <strong>金币余额:</strong>
                      <span style={{ color: '#faad14', fontWeight: 'bold', marginLeft: '8px' }}>
                        <DollarOutlined /> {currentUser.coins}
                      </span>
                    </div>
                    <div><strong>总消费:</strong> ¥{currentUser.totalSpent || 0}</div>
                    {currentUser.isVip && currentUser.vipExpireAt && (
                      <div>
                        <strong>VIP到期:</strong> {dayjs(currentUser.vipExpireAt).format('YYYY-MM-DD HH:mm')}
                      </div>
                    )}
                  </Space>
                </Card>
              </Col>
              <Col span={12}>
                <Card title="活动统计" size="small">
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <div><strong>订单数量:</strong> {currentUser.totalOrders || 0}</div>
                    <div><strong>收藏视频:</strong> {currentUser.totalFavorites || 0}</div>
                    <div><strong>购买视频:</strong> {currentUser.totalPurchased || 0}</div>
                  </Space>
                </Card>
              </Col>
            </Row>

            <Row gutter={[16, 16]} style={{ marginTop: '16px' }}>
              <Col span={24}>
                <Card title="时间信息" size="small">
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <div>
                      <strong>注册时间:</strong> {dayjs(currentUser.createdAt).format('YYYY-MM-DD HH:mm:ss')}
                    </div>
                    <div>
                      <strong>最后登录:</strong> {currentUser.lastLoginAt ? dayjs(currentUser.lastLoginAt).format('YYYY-MM-DD HH:mm:ss') : '从未登录'}
                    </div>
                    <div>
                      <strong>更新时间:</strong> {dayjs(currentUser.updatedAt).format('YYYY-MM-DD HH:mm:ss')}
                    </div>
                  </Space>
                </Card>
              </Col>
            </Row>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default UsersPage;
