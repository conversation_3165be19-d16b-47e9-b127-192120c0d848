import React, { useState, useEffect } from 'react';
import {
  Card,
  Tabs,
  Table,
  Button,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  InputNumber,
  Select,
  message,
  Statistic,
  Row,
  Col,
  DatePicker,
  Tooltip,
  Popconfirm
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  CopyOutlined,
  SearchOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { api } from '@/services/api';
import type { ColumnsType } from 'antd/es/table';
import { useNavigate, useLocation } from 'react-router-dom';
import dayjs from 'dayjs';

const { TabPane } = Tabs;
const { Option } = Select;
const { RangePicker } = DatePicker;

// 邀请码接口
interface InviteCode {
  id: number;
  code: string;
  createdBy: number;
  createdByName: string;
  maxUses: number;
  currentUses: number;
  expiresAt: string | null;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

// 邀请关系接口
interface InviteRelation {
  id: number;
  inviterUserId: number;
  inviterNickname: string;
  inviteeUserId: number;
  inviteeNickname: string;
  inviteCode: string;
  level: number;
  createdAt: string;
}

// 佣金记录接口
interface Commission {
  id: number;
  userId: number;
  userNickname: string;
  fromUserId: number;
  fromUserNickname: string;
  orderId: number;
  orderAmount: string;
  commissionRate: string;
  commissionAmount: string;
  level: number;
  status: 'pending' | 'paid' | 'cancelled';
  createdAt: string;
  paidAt: string | null;
}

const InvitationsPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState('codes');
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingRecord, setEditingRecord] = useState<any>(null);
  const [form] = Form.useForm();
  const queryClient = useQueryClient();
  const navigate = useNavigate();
  const location = useLocation();

  // 根据URL路径设置活动标签页
  useEffect(() => {
    const path = location.pathname;
    if (path === '/invitations/codes') {
      setActiveTab('codes');
    } else if (path === '/invitations/relations') {
      setActiveTab('relations');
    } else if (path === '/invitations/commissions') {
      setActiveTab('commissions');
    } else {
      setActiveTab('codes');
    }
  }, [location.pathname]);

  // 处理标签页切换
  const handleTabChange = (key: string) => {
    setActiveTab(key);
    switch (key) {
      case 'codes':
        navigate('/invitations/codes');
        break;
      case 'relations':
        navigate('/invitations/relations');
        break;
      case 'commissions':
        navigate('/invitations/commissions');
        break;
      default:
        navigate('/invitations/codes');
    }
  };

  // 邀请码列表查询
  const { data: codesData, isLoading: codesLoading } = useQuery(
    ['invite-codes'],
    () => api.get('/invitations/codes'),
    { enabled: activeTab === 'codes' }
  );

  // 邀请关系查询
  const { data: relationsData, isLoading: relationsLoading } = useQuery(
    ['invite-relations'],
    () => api.get('/invitations/relations'),
    { enabled: activeTab === 'relations' }
  );

  // 佣金记录查询
  const { data: commissionsData, isLoading: commissionsLoading } = useQuery(
    ['commissions'],
    () => api.get('/invitations/commissions'),
    { enabled: activeTab === 'commissions' }
  );

  // 邀请码统计
  const { data: statsData } = useQuery(
    ['invite-stats'],
    () => api.get('/invitations/stats')
  );

  // 创建/更新邀请码
  const saveCodeMutation = useMutation(
    (data: any) => {
      if (editingRecord) {
        return api.put(`/invitations/codes/${editingRecord.id}`, data);
      } else {
        return api.post('/invitations/codes', data);
      }
    },
    {
      onSuccess: () => {
        message.success(editingRecord ? '更新成功' : '创建成功');
        setIsModalVisible(false);
        setEditingRecord(null);
        form.resetFields();
        queryClient.invalidateQueries(['invite-codes']);
        queryClient.invalidateQueries(['invite-stats']);
      },
      onError: (error: any) => {
        message.error(error.response?.data?.message || '操作失败');
      }
    }
  );

  // 删除邀请码
  const deleteCodeMutation = useMutation(
    (id: number) => api.delete(`/invitations/codes/${id}`),
    {
      onSuccess: () => {
        message.success('删除成功');
        queryClient.invalidateQueries(['invite-codes']);
        queryClient.invalidateQueries(['invite-stats']);
      },
      onError: (error: any) => {
        message.error(error.response?.data?.message || '删除失败');
      }
    }
  );

  // 邀请码表格列
  const codeColumns: ColumnsType<InviteCode> = [
    {
      title: '邀请码',
      dataIndex: 'code',
      key: 'code',
      render: (code: string) => (
        <Space>
          <code style={{ background: '#f5f5f5', padding: '2px 6px', borderRadius: '4px' }}>
            {code}
          </code>
          <Button
            type="link"
            size="small"
            icon={<CopyOutlined />}
            onClick={() => {
              navigator.clipboard.writeText(code);
              message.success('已复制到剪贴板');
            }}
          />
        </Space>
      ),
    },
    {
      title: '创建者',
      dataIndex: 'createdByName',
      key: 'createdByName',
    },
    {
      title: '使用情况',
      key: 'usage',
      render: (_, record) => (
        <span>
          {record.currentUses} / {record.maxUses === -1 ? '无限制' : record.maxUses}
        </span>
      ),
    },
    {
      title: '状态',
      dataIndex: 'isActive',
      key: 'isActive',
      render: (isActive: boolean, record) => {
        const isExpired = record.expiresAt && dayjs(record.expiresAt).isBefore(dayjs());
        const isExhausted = record.maxUses !== -1 && record.currentUses >= record.maxUses;
        
        if (isExpired) {
          return <Tag color="red">已过期</Tag>;
        }
        if (isExhausted) {
          return <Tag color="orange">已用完</Tag>;
        }
        return <Tag color={isActive ? 'green' : 'red'}>{isActive ? '有效' : '禁用'}</Tag>;
      },
    },
    {
      title: '过期时间',
      dataIndex: 'expiresAt',
      key: 'expiresAt',
      render: (expiresAt: string | null) => 
        expiresAt ? dayjs(expiresAt).format('YYYY-MM-DD HH:mm') : '永不过期',
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (createdAt: string) => dayjs(createdAt).format('YYYY-MM-DD HH:mm'),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个邀请码吗？"
            onConfirm={() => deleteCodeMutation.mutate(record.id)}
          >
            <Button
              type="link"
              size="small"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 处理编辑
  const handleEdit = (record: InviteCode) => {
    setEditingRecord(record);
    form.setFieldsValue({
      ...record,
      expiresAt: record.expiresAt ? dayjs(record.expiresAt) : null,
    });
    setIsModalVisible(true);
  };

  // 处理保存
  const handleSave = (values: any) => {
    const data = {
      ...values,
      expiresAt: values.expiresAt ? values.expiresAt.toISOString() : null,
    };
    saveCodeMutation.mutate(data);
  };

  // 安全获取数组数据的辅助函数
  const getArrayData = (data: any, fallback: any[] = []) => {
    if (Array.isArray(data?.data?.data)) return data.data.data;
    if (Array.isArray(data?.data)) return data.data;
    if (Array.isArray(data)) return data;
    return fallback;
  };

  return (
    <div style={{ padding: '24px' }}>
      {/* 统计卡片 */}
      {statsData?.data && (
        <Row gutter={16} style={{ marginBottom: '24px' }}>
          <Col span={6}>
            <Card>
              <Statistic
                title="总邀请码数"
                value={statsData.data.totalCodes || 0}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="有效邀请码"
                value={statsData.data.activeCodes || 0}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="总邀请人数"
                value={statsData.data.totalInvites || 0}
                valueStyle={{ color: '#faad14' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="总佣金金额"
                value={statsData.data.totalCommission || 0}
                suffix="币"
                valueStyle={{ color: '#f5222d' }}
              />
            </Card>
          </Col>
        </Row>
      )}

      <Card>
        <Tabs 
          activeKey={activeTab} 
          onChange={handleTabChange}
          tabBarExtraContent={
            activeTab === 'codes' ? (
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => {
                  setEditingRecord(null);
                  form.resetFields();
                  setIsModalVisible(true);
                }}
              >
                创建邀请码
              </Button>
            ) : null
          }
        >
          <TabPane tab="邀请码管理" key="codes">
            <Table
              columns={codeColumns}
              dataSource={getArrayData(codesData)}
              rowKey="id"
              loading={codesLoading}
              pagination={{
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
              }}
            />
          </TabPane>
          
          <TabPane tab="邀请关系" key="relations">
            <div>邀请关系管理功能开发中...</div>
          </TabPane>
          
          <TabPane tab="佣金管理" key="commissions">
            <div>佣金管理功能开发中...</div>
          </TabPane>
        </Tabs>
      </Card>

      {/* 邀请码编辑模态框 */}
      <Modal
        title={editingRecord ? '编辑邀请码' : '创建邀请码'}
        open={isModalVisible}
        onCancel={() => {
          setIsModalVisible(false);
          setEditingRecord(null);
          form.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSave}
        >
          <Form.Item
            name="code"
            label="邀请码"
            rules={[{ required: true, message: '请输入邀请码' }]}
          >
            <Input placeholder="请输入邀请码" />
          </Form.Item>

          <Form.Item
            name="maxUses"
            label="最大使用次数"
            rules={[{ required: true, message: '请输入最大使用次数' }]}
          >
            <InputNumber
              min={-1}
              placeholder="输入-1表示无限制"
              style={{ width: '100%' }}
            />
          </Form.Item>

          <Form.Item
            name="expiresAt"
            label="过期时间"
          >
            <DatePicker
              showTime
              placeholder="选择过期时间，不选择表示永不过期"
              style={{ width: '100%' }}
            />
          </Form.Item>

          <Form.Item
            name="isActive"
            label="状态"
            initialValue={true}
          >
            <Select>
              <Option value={true}>有效</Option>
              <Option value={false}>禁用</Option>
            </Select>
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setIsModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit" loading={saveCodeMutation.isLoading}>
                {editingRecord ? '更新' : '创建'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default InvitationsPage;
