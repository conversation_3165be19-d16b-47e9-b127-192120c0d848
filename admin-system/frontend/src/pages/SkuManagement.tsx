import React, { useState } from 'react';
import { Card, Table, Button, Modal, Form, Input, InputNumber, Switch, Select, Space, message, Popconfirm } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { api } from '@/services/api';

const { TextArea } = Input;
const { Option } = Select;

interface Sku {
  id: number;
  skuid: string;
  name: string;
  description?: string;
  price: number;
  coins: number;
  bonus: number;
  popular: boolean;
  status: 'active' | 'inactive';
  sortOrder: number;
  createdAt: string;
  updatedAt: string;
}

const SkuManagement: React.FC = () => {
  const [modalVisible, setModalVisible] = useState(false);
  const [editingSku, setEditingSku] = useState<Sku | null>(null);
  const [form] = Form.useForm();
  const queryClient = useQueryClient();

  // 获取SKU列表
  const { data: skusData, isLoading } = useQuery(
    'admin-skus',
    () => api.post('/admin/skus/list', { page: 1, limit: 100 }),
    {
      select: (data) => data.data?.data?.skus || []
    }
  );

  // 创建SKU
  const createSkuMutation = useMutation(
    (data: any) => api.post('/admin/skus/create', data),
    {
      onSuccess: () => {
        message.success('SKU创建成功');
        queryClient.invalidateQueries('admin-skus');
        closeModal();
      },
      onError: (error: any) => {
        message.error(error.response?.data?.message || '创建失败');
      }
    }
  );

  // 更新SKU
  const updateSkuMutation = useMutation(
    ({ id, data }: { id: number; data: any }) => api.put(`/admin/skus/${id}`, data),
    {
      onSuccess: () => {
        message.success('SKU更新成功');
        queryClient.invalidateQueries('admin-skus');
        closeModal();
      },
      onError: (error: any) => {
        message.error(error.response?.data?.message || '更新失败');
      }
    }
  );

  // 删除SKU
  const deleteSkuMutation = useMutation(
    (id: number) => api.delete(`/admin/skus/${id}`),
    {
      onSuccess: () => {
        message.success('SKU删除成功');
        queryClient.invalidateQueries('admin-skus');
      },
      onError: (error: any) => {
        message.error(error.response?.data?.message || '删除失败');
      }
    }
  );

  // 打开创建/编辑模态框
  const openModal = (sku?: Sku) => {
    setEditingSku(sku || null);
    if (sku) {
      form.setFieldsValue({
        skuid: sku.skuid,
        name: sku.name,
        description: sku.description,
        price: sku.price,
        coins: sku.coins,
        bonus: sku.bonus,
        popular: sku.popular,
        status: sku.status,
        sortOrder: sku.sortOrder
      });
    } else {
      form.resetFields();
    }
    setModalVisible(true);
  };

  // 关闭模态框
  const closeModal = () => {
    setModalVisible(false);
    setEditingSku(null);
    form.resetFields();
  };

  // 保存SKU
  const saveSku = async (values: any) => {
    if (editingSku) {
      updateSkuMutation.mutate({ id: editingSku.id, data: values });
    } else {
      createSkuMutation.mutate(values);
    }
  };

  // 删除SKU
  const deleteSku = (id: number) => {
    deleteSkuMutation.mutate(id);
  };

  // 切换SKU状态
  const toggleStatus = async (sku: Sku) => {
    const newStatus = sku.status === 'active' ? 'inactive' : 'active';
    updateSkuMutation.mutate({ 
      id: sku.id, 
      data: { status: newStatus } 
    });
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: 'SKU ID',
      dataIndex: 'skuid',
      key: 'skuid',
      width: 120,
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: '价格(元)',
      dataIndex: 'price',
      key: 'price',
      render: (price: number) => `¥${price}`,
    },
    {
      title: '金币',
      dataIndex: 'coins',
      key: 'coins',
    },
    {
      title: '赠送金币',
      dataIndex: 'bonus',
      key: 'bonus',
    },
    {
      title: '热门',
      dataIndex: 'popular',
      key: 'popular',
      render: (popular: boolean) => popular ? '是' : '否',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string, record: Sku) => (
        <Switch
          checked={status === 'active'}
          onChange={() => toggleStatus(record)}
          checkedChildren="启用"
          unCheckedChildren="禁用"
          loading={updateSkuMutation.isLoading}
        />
      ),
    },
    {
      title: '排序',
      dataIndex: 'sortOrder',
      key: 'sortOrder',
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record: Sku) => (
        <Space size="middle">
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => openModal(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个SKU吗？"
            onConfirm={() => deleteSku(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
              loading={deleteSkuMutation.isLoading}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Card
        title="SKU管理"
        extra={
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => openModal()}
          >
            新增SKU
          </Button>
        }
      >
        <Table
          columns={columns}
          dataSource={skusData}
          rowKey="id"
          loading={isLoading}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
        />
      </Card>

      <Modal
        title={editingSku ? '编辑SKU' : '新增SKU'}
        open={modalVisible}
        onCancel={closeModal}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={saveSku}
          initialValues={{
            bonus: 0,
            popular: false,
            status: 'active',
            sortOrder: 0
          }}
        >
          <Form.Item
            label="第三方SKU ID"
            name="skuid"
            rules={[{ required: true, message: '请输入第三方SKU ID' }]}
          >
            <Input placeholder="请输入第三方平台的SKU ID" />
          </Form.Item>

          <Form.Item
            label="SKU名称"
            name="name"
            rules={[{ required: true, message: '请输入SKU名称' }]}
          >
            <Input placeholder="请输入SKU名称" />
          </Form.Item>

          <Form.Item
            label="描述"
            name="description"
          >
            <TextArea rows={3} placeholder="请输入SKU描述" />
          </Form.Item>

          <Form.Item
            label="价格(元)"
            name="price"
            rules={[
              { required: true, message: '请输入价格' },
              { type: 'number', min: 0.01, message: '价格必须大于0' }
            ]}
          >
            <InputNumber
              style={{ width: '100%' }}
              placeholder="请输入价格"
              precision={2}
              min={0.01}
            />
          </Form.Item>

          <Form.Item
            label="金币数量"
            name="coins"
            rules={[
              { required: true, message: '请输入金币数量' },
              { type: 'number', min: 1, message: '金币数量必须大于0' }
            ]}
          >
            <InputNumber
              style={{ width: '100%' }}
              placeholder="请输入金币数量"
              min={1}
            />
          </Form.Item>

          <Form.Item
            label="赠送金币"
            name="bonus"
          >
            <InputNumber
              style={{ width: '100%' }}
              placeholder="请输入赠送金币数量"
              min={0}
            />
          </Form.Item>

          <Form.Item
            label="是否热门"
            name="popular"
            valuePropName="checked"
          >
            <Switch checkedChildren="是" unCheckedChildren="否" />
          </Form.Item>

          <Form.Item
            label="状态"
            name="status"
          >
            <Select>
              <Option value="active">启用</Option>
              <Option value="inactive">禁用</Option>
            </Select>
          </Form.Item>

          <Form.Item
            label="排序顺序"
            name="sortOrder"
          >
            <InputNumber
              style={{ width: '100%' }}
              placeholder="请输入排序顺序"
              min={0}
            />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button 
                type="primary" 
                htmlType="submit"
                loading={createSkuMutation.isLoading || updateSkuMutation.isLoading}
              >
                保存
              </Button>
              <Button onClick={closeModal}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default SkuManagement;
