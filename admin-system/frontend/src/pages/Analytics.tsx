import React, { useState, useEffect } from 'react';
import {
  Card,
  Tabs,
  Row,
  Col,
  Statistic,
  Table,
  DatePicker,
  Select,
  Space,
  Button,
  Progress,
  Tag
} from 'antd';
import {
  ArrowUpOutlined,
  ArrowDownOutlined,
  UserOutlined,
  VideoCameraOutlined,
  DollarOutlined,
  EyeOutlined,
  DownloadOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { useQuery } from 'react-query';
import { api } from '@/services/api';
import { useNavigate, useLocation } from 'react-router-dom';
import dayjs from 'dayjs';
import type { ColumnsType } from 'antd/es/table';

const { TabPane } = Tabs;
const { RangePicker } = DatePicker;
const { Option } = Select;

// 数据分析接口
interface AnalyticsData {
  overview: {
    totalUsers: number;
    totalVideos: number;
    totalRevenue: number;
    totalViews: number;
    userGrowth: number;
    videoGrowth: number;
    revenueGrowth: number;
    viewGrowth: number;
  };
  userStats: {
    newUsers: number;
    activeUsers: number;
    vipUsers: number;
    guestUsers: number;
    userRetention: number;
  };
  videoStats: {
    totalViews: number;
    avgViewDuration: number;
    popularVideos: Array<{
      id: number;
      title: string;
      views: number;
      revenue: number;
    }>;
    categoryStats: Array<{
      category: string;
      videoCount: number;
      viewCount: number;
      revenue: number;
    }>;
  };
  revenueStats: {
    totalRevenue: number;
    vipRevenue: number;
    videoRevenue: number;
    rechargeRevenue: number;
    dailyRevenue: Array<{
      date: string;
      amount: number;
    }>;
  };
}

const AnalyticsPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs]>([
    dayjs().subtract(30, 'day'),
    dayjs()
  ]);
  const navigate = useNavigate();
  const location = useLocation();

  // 根据URL路径设置活动标签页
  useEffect(() => {
    const path = location.pathname;
    if (path === '/analytics/overview') {
      setActiveTab('overview');
    } else if (path === '/analytics/users') {
      setActiveTab('users');
    } else if (path === '/analytics/videos') {
      setActiveTab('videos');
    } else if (path === '/analytics/revenue') {
      setActiveTab('revenue');
    } else {
      setActiveTab('overview');
    }
  }, [location.pathname]);

  // 处理标签页切换
  const handleTabChange = (key: string) => {
    setActiveTab(key);
    switch (key) {
      case 'overview':
        navigate('/analytics/overview');
        break;
      case 'users':
        navigate('/analytics/users');
        break;
      case 'videos':
        navigate('/analytics/videos');
        break;
      case 'revenue':
        navigate('/analytics/revenue');
        break;
      default:
        navigate('/analytics/overview');
    }
  };

  // 分析数据查询
  const { data: analyticsData, isLoading, refetch } = useQuery(
    ['analytics', dateRange],
    () => api.get('/analytics', {
      params: {
        startDate: dateRange[0].format('YYYY-MM-DD'),
        endDate: dateRange[1].format('YYYY-MM-DD')
      }
    })
  );

  // 热门视频表格列
  const popularVideoColumns: ColumnsType<any> = [
    {
      title: '排名',
      key: 'rank',
      width: 60,
      render: (_, __, index) => index + 1,
    },
    {
      title: '视频标题',
      dataIndex: 'title',
      key: 'title',
      ellipsis: true,
    },
    {
      title: '观看次数',
      dataIndex: 'views',
      key: 'views',
      render: (views: number) => views.toLocaleString(),
    },
    {
      title: '收入',
      dataIndex: 'revenue',
      key: 'revenue',
      render: (revenue: number) => `${revenue}币`,
    },
  ];

  // 分类统计表格列
  const categoryColumns: ColumnsType<any> = [
    {
      title: '分类',
      dataIndex: 'category',
      key: 'category',
    },
    {
      title: '视频数量',
      dataIndex: 'videoCount',
      key: 'videoCount',
    },
    {
      title: '观看次数',
      dataIndex: 'viewCount',
      key: 'viewCount',
      render: (count: number) => count.toLocaleString(),
    },
    {
      title: '收入',
      dataIndex: 'revenue',
      key: 'revenue',
      render: (revenue: number) => `${revenue}币`,
    },
    {
      title: '平均收入',
      key: 'avgRevenue',
      render: (_, record) => `${(record.revenue / record.videoCount || 0).toFixed(2)}币`,
    },
  ];

  const data = analyticsData?.data?.data || analyticsData?.data;

  return (
    <div style={{ padding: '24px' }}>
      {/* 工具栏 */}
      <Card style={{ marginBottom: '24px' }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Space>
              <span>时间范围:</span>
              <RangePicker
                value={dateRange}
                onChange={(dates) => dates && setDateRange(dates as [dayjs.Dayjs, dayjs.Dayjs])}
                format="YYYY-MM-DD"
              />
              <Button icon={<ReloadOutlined />} onClick={() => refetch()}>
                刷新
              </Button>
            </Space>
          </Col>
          <Col>
            <Button icon={<DownloadOutlined />} type="primary">
              导出报告
            </Button>
          </Col>
        </Row>
      </Card>

      <Tabs activeKey={activeTab} onChange={handleTabChange}>
        <TabPane tab="数据概览" key="overview">
          <Row gutter={16}>
            <Col span={6}>
              <Card>
                <Statistic
                  title="总用户数"
                  value={data?.overview?.totalUsers || 0}
                  prefix={<UserOutlined />}
                  suffix={
                    <span style={{ fontSize: '14px', color: data?.overview?.userGrowth >= 0 ? '#3f8600' : '#cf1322' }}>
                      {data?.overview?.userGrowth >= 0 ? <ArrowUpOutlined /> : <ArrowDownOutlined />}
                      {Math.abs(data?.overview?.userGrowth || 0)}%
                    </span>
                  }
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="总视频数"
                  value={data?.overview?.totalVideos || 0}
                  prefix={<VideoCameraOutlined />}
                  suffix={
                    <span style={{ fontSize: '14px', color: data?.overview?.videoGrowth >= 0 ? '#3f8600' : '#cf1322' }}>
                      {data?.overview?.videoGrowth >= 0 ? <ArrowUpOutlined /> : <ArrowDownOutlined />}
                      {Math.abs(data?.overview?.videoGrowth || 0)}%
                    </span>
                  }
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="总收入"
                  value={data?.overview?.totalRevenue || 0}
                  prefix={<DollarOutlined />}
                  suffix="币"
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="总观看次数"
                  value={data?.overview?.totalViews || 0}
                  prefix={<EyeOutlined />}
                />
              </Card>
            </Col>
          </Row>
        </TabPane>

        <TabPane tab="用户分析" key="users">
          <Row gutter={16}>
            <Col span={6}>
              <Card>
                <Statistic
                  title="新增用户"
                  value={data?.userStats?.newUsers || 0}
                  valueStyle={{ color: '#3f8600' }}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="活跃用户"
                  value={data?.userStats?.activeUsers || 0}
                  valueStyle={{ color: '#1890ff' }}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="VIP用户"
                  value={data?.userStats?.vipUsers || 0}
                  valueStyle={{ color: '#faad14' }}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="游客用户"
                  value={data?.userStats?.guestUsers || 0}
                  valueStyle={{ color: '#722ed1' }}
                />
              </Card>
            </Col>
          </Row>

          <Row gutter={16} style={{ marginTop: '24px' }}>
            <Col span={12}>
              <Card title="用户留存率">
                <Progress
                  percent={data?.userStats?.userRetention || 0}
                  status="active"
                  strokeColor={{
                    from: '#108ee9',
                    to: '#87d068',
                  }}
                />
              </Card>
            </Col>
            <Col span={12}>
              <Card title="用户类型分布">
                <div style={{ padding: '20px 0' }}>
                  <div style={{ marginBottom: '16px' }}>
                    <span>VIP用户: </span>
                    <Progress
                      percent={(data?.userStats?.vipUsers / (data?.overview?.totalUsers || 1)) * 100}
                      size="small"
                      format={() => `${data?.userStats?.vipUsers || 0}人`}
                    />
                  </div>
                  <div>
                    <span>游客用户: </span>
                    <Progress
                      percent={(data?.userStats?.guestUsers / (data?.overview?.totalUsers || 1)) * 100}
                      size="small"
                      format={() => `${data?.userStats?.guestUsers || 0}人`}
                    />
                  </div>
                </div>
              </Card>
            </Col>
          </Row>
        </TabPane>

        <TabPane tab="视频分析" key="videos">
          <Row gutter={16} style={{ marginBottom: '24px' }}>
            <Col span={8}>
              <Card>
                <Statistic
                  title="总观看次数"
                  value={data?.videoStats?.totalViews || 0}
                  prefix={<EyeOutlined />}
                />
              </Card>
            </Col>
            <Col span={8}>
              <Card>
                <Statistic
                  title="平均观看时长"
                  value={data?.videoStats?.avgViewDuration || 0}
                  suffix="分钟"
                />
              </Card>
            </Col>
            <Col span={8}>
              <Card>
                <Statistic
                  title="视频完播率"
                  value={85}
                  suffix="%"
                  valueStyle={{ color: '#3f8600' }}
                />
              </Card>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Card title="热门视频 TOP 10">
                <Table
                  columns={popularVideoColumns}
                  dataSource={data?.videoStats?.popularVideos || []}
                  pagination={false}
                  size="small"
                />
              </Card>
            </Col>
            <Col span={12}>
              <Card title="分类统计">
                <Table
                  columns={categoryColumns}
                  dataSource={data?.videoStats?.categoryStats || []}
                  pagination={false}
                  size="small"
                />
              </Card>
            </Col>
          </Row>
        </TabPane>

        <TabPane tab="收入分析" key="revenue">
          <Row gutter={16} style={{ marginBottom: '24px' }}>
            <Col span={6}>
              <Card>
                <Statistic
                  title="总收入"
                  value={data?.revenueStats?.totalRevenue || 0}
                  suffix="币"
                  valueStyle={{ color: '#3f8600' }}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="VIP收入"
                  value={data?.revenueStats?.vipRevenue || 0}
                  suffix="币"
                  valueStyle={{ color: '#faad14' }}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="视频收入"
                  value={data?.revenueStats?.videoRevenue || 0}
                  suffix="币"
                  valueStyle={{ color: '#1890ff' }}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="充值收入"
                  value={data?.revenueStats?.rechargeRevenue || 0}
                  suffix="币"
                  valueStyle={{ color: '#722ed1' }}
                />
              </Card>
            </Col>
          </Row>

          <Card title="收入趋势">
            <div style={{ height: '300px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
              <span style={{ color: '#999' }}>收入趋势图表开发中...</span>
            </div>
          </Card>
        </TabPane>
      </Tabs>
    </div>
  );
};

export default AnalyticsPage;
