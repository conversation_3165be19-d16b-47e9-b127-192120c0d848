import React, { useState } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  DatePicker,
  Tag,
  Modal,
  Form,
  message,
  Popconfirm,
  Tooltip,
  Statistic,
  Row,
  Col
} from 'antd';
import {
  SearchOutlined,
  EditOutlined,
  EyeOutlined,
  CheckOutlined,
  CloseOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { api } from '@/services/api';
import type { ColumnsType } from 'antd/es/table';

const { RangePicker } = DatePicker;
const { Option } = Select;

interface Order {
  id: number;
  userId: number;
  nickname: string;
  guestId: string;
  orderNo: string;
  type: 'recharge' | 'vip_purchase' | 'video_purchase';
  amount: string;
  status: 'pending' | 'paid' | 'failed';
  paymentMethod: string | null;
  paymentId: string | null;
  videoId: number | null;
  vipDuration: number | null;
  paidAt: string;
  remark?: string;
  createdAt: string;
  updatedAt: string;
}

interface OrderFilters {
  page: number;
  pageSize: number;
  keyword?: string;
  type?: string;
  status?: string;
  dateRange?: [string, string];
}

const OrdersPage: React.FC = () => {
  const [filters, setFilters] = useState<OrderFilters>({
    page: 1,
    pageSize: 20
  });
  const [editingOrder, setEditingOrder] = useState<Order | null>(null);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [form] = Form.useForm();
  const queryClient = useQueryClient();

  // 获取订单列表
  const { data: ordersData, isLoading } = useQuery(
    ['orders', filters],
    () => api.get('/orders', { params: filters }),
    {
      keepPreviousData: true
    }
  );

  // 获取订单统计
  const { data: statsData } = useQuery(
    'order-stats',
    () => api.get('/orders/stats')
  );

  // 更新订单状态
  const updateMutation = useMutation(
    ({ id, data }: { id: number; data: any }) => api.put(`/orders/${id}`, data),
    {
      onSuccess: () => {
        message.success('更新成功');
        queryClient.invalidateQueries('orders');
        queryClient.invalidateQueries('order-stats');
        setIsModalVisible(false);
        setEditingOrder(null);
        form.resetFields();
      },
      onError: () => {
        message.error('更新失败');
      }
    }
  );

  // 表格列配置
  const columns: ColumnsType<Order> = [
    {
      title: '订单ID',
      dataIndex: 'id',
      width: 100,
    },
    {
      title: '用户',
      dataIndex: 'nickname',
      width: 120,
      render: (nickname: string, record) => (
        <Tooltip title={`用户ID: ${record.userId}`}>
          <span>{nickname || `用户${record.userId}`}</span>
        </Tooltip>
      ),
    },
    {
      title: '类型',
      dataIndex: 'type',
      width: 100,
      render: (type: string) => {
        const typeMap = {
          recharge: { color: 'blue', text: '充值' },
          vip_purchase: { color: 'gold', text: 'VIP' },
          video_purchase: { color: 'green', text: '视频' },
          // 兼容旧数据
          vip: { color: 'gold', text: 'VIP' },
          video: { color: 'green', text: '视频' }
        };
        const config = typeMap[type as keyof typeof typeMap] || { color: 'default', text: type };
        return <Tag color={config.color}>{config.text}</Tag>;
      },
    },
    {
      title: '金额',
      dataIndex: 'amount',
      width: 100,
      render: (amount: string) => (
        <span style={{ fontWeight: 'bold' }}>{amount}币</span>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 100,
      render: (status: string) => {
        const statusMap = {
          pending: { color: 'orange', text: '待处理' },
          paid: { color: 'green', text: '已支付' },
          failed: { color: 'red', text: '失败' }
        };
        const config = statusMap[status as keyof typeof statusMap] || { color: 'default', text: status };
        return <Tag color={config.color}>{config.text}</Tag>;
      },
    },
    {
      title: '支付方式',
      dataIndex: 'paymentMethod',
      width: 120,
      render: (paymentMethod: string) => paymentMethod || '未知',
    },
    {
      title: '订单号',
      dataIndex: 'orderNo',
      width: 150,
      ellipsis: true,
      render: (orderNo: string) => (
        <Tooltip title={orderNo}>
          <span>{orderNo}</span>
        </Tooltip>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      width: 180,
      render: (date: string) => new Date(date).toLocaleString(),
    },
    {
      title: '操作',
      key: 'actions',
      width: 200,
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => handleView(record)}
          >
            查看
          </Button>
          {record.status === 'pending' && (
            <>
              <Popconfirm
                title="确定要完成这个订单吗？"
                onConfirm={() => updateMutation.mutate({
                  id: record.id,
                  data: { status: 'paid' }
                })}
                okText="确定"
                cancelText="取消"
              >
                <Button
                  type="link"
                  size="small"
                  icon={<CheckOutlined />}
                >
                  完成
                </Button>
              </Popconfirm>
              <Popconfirm
                title="确定要拒绝这个订单吗？"
                onConfirm={() => updateMutation.mutate({
                  id: record.id,
                  data: { status: 'failed' }
                })}
                okText="确定"
                cancelText="取消"
              >
                <Button
                  type="link"
                  size="small"
                  danger
                  icon={<CloseOutlined />}
                >
                  拒绝
                </Button>
              </Popconfirm>
            </>
          )}

          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
        </Space>
      ),
    },
  ];

  // 处理查看
  const handleView = (order: Order) => {
    Modal.info({
      title: '订单详情',
      width: 600,
      content: (
        <div style={{ marginTop: 16 }}>
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <strong>订单ID:</strong> {order.id}
            </Col>
            <Col span={12}>
              <strong>用户ID:</strong> {order.userId}
            </Col>
            <Col span={12}>
              <strong>类型:</strong> {order.type}
            </Col>
            <Col span={12}>
              <strong>金额:</strong> {order.amount}币
            </Col>
            <Col span={12}>
              <strong>状态:</strong> {order.status}
            </Col>
            <Col span={12}>
              <strong>支付方式:</strong> {order.paymentMethod || '未知'}
            </Col>
            <Col span={24}>
              <strong>订单号:</strong> {order.orderNo}
            </Col>
            <Col span={24}>
              <strong>备注:</strong> {order.remark || '无'}
            </Col>
            <Col span={12}>
              <strong>创建时间:</strong> {new Date(order.createdAt).toLocaleString()}
            </Col>
            <Col span={12}>
              <strong>更新时间:</strong> {new Date(order.updatedAt).toLocaleString()}
            </Col>
          </Row>
        </div>
      ),
    });
  };

  // 处理编辑
  const handleEdit = (order: Order) => {
    setEditingOrder(order);
    form.setFieldsValue(order);
    setIsModalVisible(true);
  };

  // 处理保存
  const handleSave = (values: any) => {
    if (editingOrder) {
      updateMutation.mutate({
        id: editingOrder.id,
        data: values
      });
    }
  };

  return (
    <div style={{ padding: '24px' }}>
      {/* 统计卡片 */}
      {statsData?.data?.data && (
        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col span={6}>
            <Card>
              <Statistic
                title="总订单数"
                value={statsData.data.data.totalOrders}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="今日订单"
                value={statsData.data.data.todayOrders}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="今日收入"
                value={statsData.data.data.todayRevenue}
                suffix="币"
                valueStyle={{ color: '#faad14' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="月度收入"
                value={statsData.data.data.monthlyRevenue}
                suffix="币"
                valueStyle={{ color: '#f5222d' }}
              />
            </Card>
          </Col>
        </Row>
      )}

      <Card>
        <div style={{ marginBottom: '16px' }}>
          <Space wrap>
            <Input
              placeholder="搜索用户名或交易号"
              prefix={<SearchOutlined />}
              style={{ width: 200 }}
              onChange={(e) => setFilters({ ...filters, keyword: e.target.value })}
            />
            <Select
              placeholder="选择类型"
              style={{ width: 120 }}
              allowClear
              onChange={(value) => setFilters({ ...filters, type: value })}
            >
              <Option value="recharge">充值</Option>
              <Option value="vip_purchase">VIP</Option>
              <Option value="video_purchase">视频</Option>
            </Select>
            <Select
              placeholder="选择状态"
              style={{ width: 120 }}
              allowClear
              onChange={(value) => setFilters({ ...filters, status: value })}
            >
              <Option value="pending">待处理</Option>
              <Option value="paid">已支付</Option>
              <Option value="failed">失败</Option>
            </Select>
            <RangePicker
              onChange={(dates) => setFilters({
                ...filters,
                dateRange: dates ? [
                  dates[0]!.format('YYYY-MM-DD'),
                  dates[1]!.format('YYYY-MM-DD')
                ] : undefined
              })}
            />
            <Button
              icon={<ReloadOutlined />}
              onClick={() => queryClient.invalidateQueries('orders')}
            >
              刷新
            </Button>
          </Space>
        </div>

        <Table
          columns={columns}
          dataSource={ordersData?.data?.data?.items || []}
          rowKey="id"
          loading={isLoading}
          pagination={{
            current: filters.page,
            pageSize: filters.pageSize,
            total: ordersData?.data?.data?.total || 0,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
            onChange: (page, pageSize) => setFilters({ ...filters, page, pageSize })
          }}
        />
      </Card>

      {/* 编辑模态框 */}
      <Modal
        title="编辑订单"
        open={isModalVisible}
        onCancel={() => {
          setIsModalVisible(false);
          setEditingOrder(null);
          form.resetFields();
        }}
        onOk={() => form.submit()}
        confirmLoading={updateMutation.isLoading}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSave}
        >
          <Form.Item
            name="status"
            label="状态"
            rules={[{ required: true, message: '请选择状态' }]}
          >
            <Select>
              <Option value="pending">待处理</Option>
              <Option value="paid">已支付</Option>
              <Option value="failed">失败</Option>
            </Select>
          </Form.Item>
          <Form.Item
            name="remark"
            label="备注"
          >
            <Input.TextArea rows={3} />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default OrdersPage;
