import React from 'react';
import { 
  Card, 
  Form, 
  Input, 
  InputNumber, 
  Switch, 
  Button, 
  Space, 
  message, 
  Spin,
  Row,
  Col,
  Divider,
  Typography
} from 'antd';
import { 
  PlusOutlined, 
  DeleteOutlined,
  SaveOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { api } from '@/services/api';

const { Title, Text } = Typography;
const { TextArea } = Input;

// 充值配置页面
const RechargeConfigPage: React.FC = () => {
  const [form] = Form.useForm();
  const queryClient = useQueryClient();

  // 获取充值配置
  const { data: rechargeConfig, isLoading } = useQuery(
    'recharge-config',
    () => api.get('/payment/recharge'),
    {
      onSuccess: (data) => {
        console.log('充值配置数据:', data.data);
        form.setFieldsValue(data.data);
      },
      onError: (error: any) => {
        console.error('获取充值配置失败:', error);
        message.error('获取充值配置失败');
      }
    }
  );

  // 更新充值配置
  const updateMutation = useMutation(
    (values: any) => api.put('/payment/recharge', values),
    {
      onSuccess: () => {
        message.success('充值配置更新成功');
        queryClient.invalidateQueries('recharge-config');
      },
      onError: (error: any) => {
        console.error('更新充值配置失败:', error);
        message.error(error.response?.data?.message || '更新失败');
      }
    }
  );

  const handleSubmit = (values: any) => {
    console.log('提交充值配置:', values);
    updateMutation.mutate(values);
  };

  const handleRefresh = () => {
    queryClient.invalidateQueries('recharge-config');
  };

  if (isLoading) {
    return (
      <div style={{ padding: '24px', textAlign: 'center' }}>
        <Spin size="large" />
        <div style={{ marginTop: '16px' }}>加载充值配置中...</div>
      </div>
    );
  }

  return (
    <div style={{ padding: '24px', background: '#f0f2f5', minHeight: '100vh' }}>
      <Card 
        title={
          <Space>
            <Title level={4} style={{ margin: 0 }}>充值配置</Title>
          </Space>
        } 
        extra={
          <Space>
            <Button 
              icon={<ReloadOutlined />}
              onClick={handleRefresh}
            >
              刷新
            </Button>
            <Button 
              type="primary" 
              icon={<SaveOutlined />}
              loading={updateMutation.isLoading}
              onClick={() => form.submit()}
            >
              保存配置
            </Button>
          </Space>
        }
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            enableRecharge: true,
            minRecharge: 1,
            maxRecharge: 10000,
            rechargeNotice: '充值后金币立即到账，可用于购买VIP或视频',
            rechargeOptions: [
              { amount: 10, coins: 100, bonus: 0, description: '新手套餐' },
              { amount: 50, coins: 500, bonus: 50, description: '热门套餐' },
              { amount: 100, coins: 1000, bonus: 200, description: '超值套餐' }
            ]
          }}
        >
          <Row gutter={24}>
            <Col span={12}>
              <Form.Item
                label="启用充值功能"
                name="enableRecharge"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="充值提示"
                name="rechargeNotice"
              >
                <TextArea rows={3} placeholder="请输入充值提示信息" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col span={12}>
              <Form.Item
                label="最小充值金额"
                name="minRecharge"
                rules={[{ required: true, message: '请输入最小充值金额' }]}
              >
                <InputNumber
                  min={1}
                  style={{ width: '100%' }}
                  placeholder="请输入最小充值金额"
                  addonAfter="元"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="最大充值金额"
                name="maxRecharge"
                rules={[{ required: true, message: '请输入最大充值金额' }]}
              >
                <InputNumber
                  min={1}
                  style={{ width: '100%' }}
                  placeholder="请输入最大充值金额"
                  addonAfter="元"
                />
              </Form.Item>
            </Col>
          </Row>

          <Divider orientation="left">充值套餐配置</Divider>

          <Form.List name="rechargeOptions">
            {(fields, { add, remove }) => (
              <>
                {fields.map(({ key, name, ...restField }) => (
                  <Card
                    key={key}
                    size="small"
                    style={{ marginBottom: 16 }}
                    title={`套餐 ${name + 1}`}
                    extra={
                      <Button
                        type="text"
                        danger
                        icon={<DeleteOutlined />}
                        onClick={() => remove(name)}
                      >
                        删除
                      </Button>
                    }
                  >
                    <Row gutter={16}>
                      <Col span={6}>
                        <Form.Item
                          {...restField}
                          name={[name, 'amount']}
                          label="充值金额"
                          rules={[{ required: true, message: '请输入充值金额' }]}
                        >
                          <InputNumber
                            min={1}
                            style={{ width: '100%' }}
                            placeholder="充值金额"
                            addonAfter="元"
                          />
                        </Form.Item>
                      </Col>
                      <Col span={6}>
                        <Form.Item
                          {...restField}
                          name={[name, 'coins']}
                          label="获得金币"
                          rules={[{ required: true, message: '请输入获得金币数' }]}
                        >
                          <InputNumber
                            min={1}
                            style={{ width: '100%' }}
                            placeholder="获得金币"
                            addonAfter="金币"
                          />
                        </Form.Item>
                      </Col>
                      <Col span={6}>
                        <Form.Item
                          {...restField}
                          name={[name, 'bonus']}
                          label="奖励金币"
                        >
                          <InputNumber
                            min={0}
                            style={{ width: '100%' }}
                            placeholder="奖励金币"
                            addonAfter="金币"
                          />
                        </Form.Item>
                      </Col>
                      <Col span={6}>
                        <Form.Item
                          {...restField}
                          name={[name, 'description']}
                          label="套餐描述"
                        >
                          <Input placeholder="套餐描述" />
                        </Form.Item>
                      </Col>
                    </Row>
                  </Card>
                ))}
                <Button
                  type="dashed"
                  onClick={() => add({ amount: 10, coins: 100, bonus: 0, description: '' })}
                  block
                  icon={<PlusOutlined />}
                >
                  添加充值套餐
                </Button>
              </>
            )}
          </Form.List>
        </Form>
      </Card>
    </div>
  );
};

export default RechargeConfigPage;
