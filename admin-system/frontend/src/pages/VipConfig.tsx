import React from 'react';
import { 
  Card, 
  Form, 
  Input, 
  InputNumber, 
  Switch, 
  Button, 
  Space, 
  message, 
  Spin,
  Row,
  Col,
  Divider,
  Typography
} from 'antd';
import { 
  PlusOutlined, 
  DeleteOutlined,
  SaveOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { api } from '@/services/api';

const { Title, Text } = Typography;
const { TextArea } = Input;

// VIP套餐配置页面
const VipConfigPage: React.FC = () => {
  const [form] = Form.useForm();
  const queryClient = useQueryClient();

  // 获取VIP配置
  const { data: vipConfig, isLoading } = useQuery(
    'vip-config',
    () => api.get('/payment/vip'),
    {
      onSuccess: (data) => {
        console.log('VIP配置数据:', data.data);
        form.setFieldsValue(data.data);
      },
      onError: (error: any) => {
        console.error('获取VIP配置失败:', error);
        message.error('获取VIP配置失败');
      }
    }
  );

  // 更新VIP配置
  const updateMutation = useMutation(
    (values: any) => api.put('/payment/vip', values),
    {
      onSuccess: () => {
        message.success('VIP配置更新成功');
        queryClient.invalidateQueries('vip-config');
      },
      onError: (error: any) => {
        console.error('更新VIP配置失败:', error);
        message.error(error.response?.data?.message || '更新失败');
      }
    }
  );

  const handleSubmit = (values: any) => {
    console.log('提交VIP配置:', values);
    updateMutation.mutate(values);
  };

  const handleRefresh = () => {
    queryClient.invalidateQueries('vip-config');
  };

  if (isLoading) {
    return (
      <div style={{ padding: '24px', textAlign: 'center' }}>
        <Spin size="large" />
        <div style={{ marginTop: '16px' }}>加载VIP配置中...</div>
      </div>
    );
  }

  return (
    <div style={{ padding: '24px', background: '#f0f2f5', minHeight: '100vh' }}>
      <Card 
        title={
          <Space>
            <Title level={4} style={{ margin: 0 }}>VIP套餐配置</Title>
          </Space>
        } 
        extra={
          <Space>
            <Button 
              icon={<ReloadOutlined />}
              onClick={handleRefresh}
            >
              刷新
            </Button>
            <Button 
              type="primary" 
              icon={<SaveOutlined />}
              loading={updateMutation.isLoading}
              onClick={() => form.submit()}
            >
              保存配置
            </Button>
          </Space>
        }
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            enableVip: true,
            vipTrialDays: 3,
            vipPrivileges: 'VIP用户可以观看所有付费视频，享受高清画质，无广告体验',
            vipPlans: [
              { name: '3天VIP', days: 3, price: 20, discount: 0, description: '体验VIP特权' },
              { name: '7天VIP', days: 7, price: 40, discount: 10, description: '一周畅享' },
              { name: '30天VIP', days: 30, price: 150, discount: 20, description: '月度会员' }
            ]
          }}
        >
          <Row gutter={24}>
            <Col span={8}>
              <Form.Item
                label="启用VIP功能"
                name="enableVip"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="VIP试用天数"
                name="vipTrialDays"
                rules={[{ required: true, message: '请输入VIP试用天数' }]}
              >
                <InputNumber
                  min={0}
                  style={{ width: '100%' }}
                  placeholder="VIP试用天数"
                  addonAfter="天"
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="VIP特权说明"
                name="vipPrivileges"
              >
                <TextArea rows={3} placeholder="请输入VIP特权说明" />
              </Form.Item>
            </Col>
          </Row>

          <Divider orientation="left">VIP套餐配置</Divider>

          <Form.List name="vipPlans">
            {(fields, { add, remove }) => (
              <>
                {fields.map(({ key, name, ...restField }) => (
                  <Card
                    key={key}
                    size="small"
                    style={{ marginBottom: 16 }}
                    title={`套餐 ${name + 1}`}
                    extra={
                      <Button
                        type="text"
                        danger
                        icon={<DeleteOutlined />}
                        onClick={() => remove(name)}
                      >
                        删除
                      </Button>
                    }
                  >
                    <Row gutter={16}>
                      <Col span={6}>
                        <Form.Item
                          {...restField}
                          name={[name, 'name']}
                          label="套餐名称"
                          rules={[{ required: true, message: '请输入套餐名称' }]}
                        >
                          <Input placeholder="套餐名称" />
                        </Form.Item>
                      </Col>
                      <Col span={6}>
                        <Form.Item
                          {...restField}
                          name={[name, 'days']}
                          label="有效天数"
                          rules={[{ required: true, message: '请输入有效天数' }]}
                        >
                          <InputNumber
                            min={1}
                            style={{ width: '100%' }}
                            placeholder="有效天数"
                            addonAfter="天"
                          />
                        </Form.Item>
                      </Col>
                      <Col span={6}>
                        <Form.Item
                          {...restField}
                          name={[name, 'price']}
                          label="价格"
                          rules={[{ required: true, message: '请输入价格' }]}
                        >
                          <InputNumber
                            min={1}
                            style={{ width: '100%' }}
                            placeholder="价格"
                            addonAfter="金币"
                          />
                        </Form.Item>
                      </Col>
                      <Col span={6}>
                        <Form.Item
                          {...restField}
                          name={[name, 'discount']}
                          label="折扣"
                        >
                          <InputNumber
                            min={0}
                            max={100}
                            style={{ width: '100%' }}
                            placeholder="折扣"
                            addonAfter="%"
                          />
                        </Form.Item>
                      </Col>
                    </Row>
                    <Row>
                      <Col span={24}>
                        <Form.Item
                          {...restField}
                          name={[name, 'description']}
                          label="套餐描述"
                        >
                          <Input placeholder="套餐描述" />
                        </Form.Item>
                      </Col>
                    </Row>
                  </Card>
                ))}
                <Button
                  type="dashed"
                  onClick={() => add({ name: '', days: 30, price: 100, discount: 0, description: '' })}
                  block
                  icon={<PlusOutlined />}
                >
                  添加VIP套餐
                </Button>
              </>
            )}
          </Form.List>
        </Form>
      </Card>
    </div>
  );
};

export default VipConfigPage;
