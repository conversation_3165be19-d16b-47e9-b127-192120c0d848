.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.login-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  z-index: 1;
}

.login-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
}

.login-content {
  position: relative;
  z-index: 2;
  width: 100%;
  max-width: 400px;
  padding: 20px;
}

.login-card {
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.2);
  
  .ant-card-body {
    padding: 40px 32px;
  }
}

.login-header {
  text-align: center;
  margin-bottom: 32px;
}

.login-logo {
  margin-bottom: 16px;
  
  .logo-icon {
    font-size: 48px;
    color: #1890ff;
    background: linear-gradient(135deg, #1890ff, #722ed1);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
}

.login-title {
  margin-bottom: 8px !important;
  color: #262626;
  font-weight: 600;
}

.login-subtitle {
  font-size: 14px;
  color: #8c8c8c;
}

.login-form {
  .ant-form-item {
    margin-bottom: 20px;
  }
  
  .ant-input-affix-wrapper,
  .ant-input {
    height: 48px;
    border-radius: 8px;
    border: 1px solid #d9d9d9;
    transition: all 0.3s;
    
    &:hover {
      border-color: #40a9ff;
    }
    
    &:focus,
    &.ant-input-affix-wrapper-focused {
      border-color: #1890ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }
  }
  
  .ant-input-prefix {
    color: #8c8c8c;
    margin-right: 8px;
  }
}

.login-button {
  height: 48px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  background: linear-gradient(135deg, #1890ff, #722ed1);
  border: none;
  transition: all 0.3s;
  
  &:hover {
    background: linear-gradient(135deg, #40a9ff, #9254de);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
  }
  
  &:active {
    transform: translateY(0);
  }
  
  &.ant-btn-loading {
    background: linear-gradient(135deg, #1890ff, #722ed1);
  }
}

.login-footer {
  text-align: center;
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #f0f0f0;
}

.login-tips {
  font-size: 12px;
  color: #8c8c8c;
  background: #f6f8fa;
  padding: 8px 12px;
  border-radius: 6px;
  display: inline-block;
}

// 响应式设计
@media (max-width: 768px) {
  .login-content {
    max-width: 320px;
    padding: 16px;
  }
  
  .login-card .ant-card-body {
    padding: 32px 24px;
  }
  
  .login-title {
    font-size: 20px;
  }
  
  .login-logo .logo-icon {
    font-size: 40px;
  }
}

// 动画效果
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.login-card {
  animation: fadeInUp 0.6s ease-out;
}

// 加载状态样式
.ant-spin-container {
  .ant-form-item {
    transition: opacity 0.3s;
  }
}

.ant-spin-spinning {
  .ant-form-item {
    opacity: 0.7;
  }
}
