import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Typography,
  Spin,
  Select,
  DatePicker,
  Space,
  Alert,
  Progress,
  Tag
} from 'antd';
import {
  UserOutlined,
  VideoCameraOutlined,
  ShoppingCartOutlined,
  DollarOutlined,
  EyeOutlined,
  TeamOutlined,
  CrownOutlined,
  RiseOutlined,
  FallOutlined
} from '@ant-design/icons';
import { Line, Column, Pie } from '@ant-design/plots';
import dayjs from 'dayjs';
import { api } from '../services/api';

const { Title } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;

interface DashboardStats {
  users: {
    total: number;
    active: number;
    vip: number;
    guest: number;
    todayNew: number;
  };
  videos: {
    total: number;
    published: number;
    pending: number;
    todayUploaded: number;
  };
  orders: {
    total: number;
    todayOrders: number;
    todayRevenue: number;
    monthlyRevenue: number;
  };
  system: {
    onlineUsers: number;
    todayViews: number;
    storageUsed: string;
    serverStatus: 'healthy' | 'warning' | 'error';
  };
}

const DashboardPage: React.FC = () => {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [chartData, setChartData] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [chartLoading, setChartLoading] = useState(false);
  const [chartType, setChartType] = useState('users');
  const [chartPeriod, setChartPeriod] = useState('7d');
  const [realTimeData, setRealTimeData] = useState<any>(null);

  // 获取统计数据
  const fetchStats = async () => {
    try {
      setLoading(true);
      const response = await api.get('/dashboard/stats');
      if (response.data.success) {
        setStats(response.data.data);
      }
    } catch (error) {
      console.error('获取统计数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 获取图表数据
  const fetchChartData = async (type: string, period: string) => {
    try {
      setChartLoading(true);
      const response = await api.get('/dashboard/chart-data', {
        params: { type, period }
      });
      if (response.data.success) {
        setChartData(response.data.data);
      }
    } catch (error) {
      console.error('获取图表数据失败:', error);
    } finally {
      setChartLoading(false);
    }
  };

  // 获取实时数据
  const fetchRealTimeData = async () => {
    try {
      const response = await api.get('/dashboard/realtime');
      if (response.data.success) {
        setRealTimeData(response.data.data);
      }
    } catch (error) {
      console.error('获取实时数据失败:', error);
    }
  };

  useEffect(() => {
    fetchStats();
    fetchRealTimeData();

    // 设置定时器更新实时数据
    const interval = setInterval(fetchRealTimeData, 30000); // 30秒更新一次

    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    fetchChartData(chartType, chartPeriod);
  }, [chartType, chartPeriod]);

  // 图表配置
  const lineConfig = {
    data: chartData,
    xField: 'date',
    yField: 'count',
    smooth: true,
    color: '#1890ff',
    point: {
      size: 3,
      shape: 'circle',
    },
    tooltip: {
      formatter: (datum: any) => {
        return {
          name: '数量',
          value: datum.count,
        };
      },
    },
  };

  const getServerStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return '#52c41a';
      case 'warning': return '#faad14';
      case 'error': return '#f5222d';
      default: return '#d9d9d9';
    }
  };

  const getServerStatusText = (status: string) => {
    switch (status) {
      case 'healthy': return '正常';
      case 'warning': return '警告';
      case 'error': return '错误';
      default: return '未知';
    }
  };

  if (loading) {
    return (
      <div style={{ padding: '24px', textAlign: 'center' }}>
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px' }}>
        <Title level={2}>仪表板</Title>
        <Space>
          <Tag color={getServerStatusColor(stats?.system.serverStatus || 'healthy')}>
            服务器状态: {getServerStatusText(stats?.system.serverStatus || 'healthy')}
          </Tag>
          {realTimeData && (
            <Tag color="blue">
              在线用户: {realTimeData.onlineUsers}
            </Tag>
          )}
        </Space>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总用户数"
              value={stats?.users.total || 0}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
            <div style={{ marginTop: '8px', fontSize: '12px', color: '#666' }}>
              今日新增: {stats?.users.todayNew || 0}
            </div>
          </Card>
        </Col>

        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总视频数"
              value={stats?.videos.total || 0}
              prefix={<VideoCameraOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
            <div style={{ marginTop: '8px', fontSize: '12px', color: '#666' }}>
              今日上传: {stats?.videos.todayUploaded || 0}
            </div>
          </Card>
        </Col>

        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总订单数"
              value={stats?.orders.total || 0}
              prefix={<ShoppingCartOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
            <div style={{ marginTop: '8px', fontSize: '12px', color: '#666' }}>
              今日订单: {stats?.orders.todayOrders || 0}
            </div>
          </Card>
        </Col>

        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="今日收入"
              value={stats?.orders.todayRevenue || 0}
              prefix={<DollarOutlined />}
              suffix="元"
              valueStyle={{ color: '#cf1322' }}
            />
            <div style={{ marginTop: '8px', fontSize: '12px', color: '#666' }}>
              本月收入: {stats?.orders.monthlyRevenue || 0}元
            </div>
          </Card>
        </Col>
      </Row>

      {/* 详细统计 */}
      <Row gutter={[16, 16]} style={{ marginTop: '24px' }}>
        <Col xs={24} lg={12}>
          <Card title="用户分布">
            <Row gutter={16}>
              <Col span={8}>
                <Statistic
                  title="活跃用户"
                  value={stats?.users.active || 0}
                  prefix={<TeamOutlined />}
                  valueStyle={{ color: '#52c41a' }}
                />
              </Col>
              <Col span={8}>
                <Statistic
                  title="VIP用户"
                  value={stats?.users.vip || 0}
                  prefix={<CrownOutlined />}
                  valueStyle={{ color: '#faad14' }}
                />
              </Col>
              <Col span={8}>
                <Statistic
                  title="游客用户"
                  value={stats?.users.guest || 0}
                  prefix={<UserOutlined />}
                  valueStyle={{ color: '#1890ff' }}
                />
              </Col>
            </Row>
          </Card>
        </Col>

        <Col xs={24} lg={12}>
          <Card title="视频状态">
            <Row gutter={16}>
              <Col span={8}>
                <Statistic
                  title="已发布"
                  value={stats?.videos.published || 0}
                  prefix={<VideoCameraOutlined />}
                  valueStyle={{ color: '#52c41a' }}
                />
              </Col>
              <Col span={8}>
                <Statistic
                  title="待审核"
                  value={stats?.videos.pending || 0}
                  prefix={<EyeOutlined />}
                  valueStyle={{ color: '#faad14' }}
                />
              </Col>
              <Col span={8}>
                <Statistic
                  title="今日观看"
                  value={stats?.system.todayViews || 0}
                  prefix={<EyeOutlined />}
                  valueStyle={{ color: '#1890ff' }}
                />
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>

      {/* 图表区域 */}
      <Row gutter={[16, 16]} style={{ marginTop: '24px' }}>
        <Col span={24}>
          <Card
            title="数据趋势"
            extra={
              <Space>
                <Select
                  value={chartType}
                  onChange={setChartType}
                  style={{ width: 120 }}
                >
                  <Option value="users">用户增长</Option>
                  <Option value="videos">视频上传</Option>
                  <Option value="orders">订单数量</Option>
                  <Option value="views">观看次数</Option>
                </Select>
                <Select
                  value={chartPeriod}
                  onChange={setChartPeriod}
                  style={{ width: 100 }}
                >
                  <Option value="7d">7天</Option>
                  <Option value="30d">30天</Option>
                  <Option value="12m">12个月</Option>
                </Select>
              </Space>
            }
          >
            <Spin spinning={chartLoading}>
              <div style={{ height: '300px' }}>
                {chartData.length > 0 ? (
                  <Line {...lineConfig} />
                ) : (
                  <div style={{
                    height: '100%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: '#999'
                  }}>
                    暂无数据
                  </div>
                )}
              </div>
            </Spin>
          </Card>
        </Col>
      </Row>

      {/* 系统信息 */}
      <Row gutter={[16, 16]} style={{ marginTop: '24px' }}>
        <Col xs={24} lg={12}>
          <Card title="系统状态">
            <Space direction="vertical" style={{ width: '100%' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <span>服务器状态</span>
                <Tag color={getServerStatusColor(stats?.system.serverStatus || 'healthy')}>
                  {getServerStatusText(stats?.system.serverStatus || 'healthy')}
                </Tag>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <span>存储使用</span>
                <span>{stats?.system.storageUsed || '0GB'}</span>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <span>在线用户</span>
                <span style={{ color: '#52c41a', fontWeight: 'bold' }}>
                  {stats?.system.onlineUsers || 0}
                </span>
              </div>
            </Space>
          </Card>
        </Col>

        <Col xs={24} lg={12}>
          <Card title="快速操作">
            <Space direction="vertical" style={{ width: '100%' }}>
              <Alert
                message="系统运行正常"
                description="所有服务运行稳定，数据同步正常。"
                type="success"
                showIcon
              />
              <div style={{ marginTop: '16px' }}>
                <p style={{ margin: 0, color: '#666' }}>
                  最后更新时间: {dayjs().format('YYYY-MM-DD HH:mm:ss')}
                </p>
              </div>
            </Space>
          </Card>
        </Col>
      </Row>

      {/* 实时数据 */}
      {realTimeData && (
        <Row gutter={[16, 16]} style={{ marginTop: '24px' }}>
          <Col span={24}>
            <Card title="今日实时数据">
              <Row gutter={[16, 16]}>
                <Col xs={24} sm={6}>
                  <Statistic
                    title="新增用户"
                    value={realTimeData.newUsers || 0}
                    prefix={<RiseOutlined />}
                    valueStyle={{ color: '#3f8600' }}
                  />
                </Col>
                <Col xs={24} sm={6}>
                  <Statistic
                    title="新增视频"
                    value={realTimeData.newVideos || 0}
                    prefix={<VideoCameraOutlined />}
                    valueStyle={{ color: '#1890ff' }}
                  />
                </Col>
                <Col xs={24} sm={6}>
                  <Statistic
                    title="新增订单"
                    value={realTimeData.newOrders || 0}
                    prefix={<ShoppingCartOutlined />}
                    valueStyle={{ color: '#722ed1' }}
                  />
                </Col>
                <Col xs={24} sm={6}>
                  <Statistic
                    title="今日收入"
                    value={realTimeData.todayRevenue || 0}
                    prefix={<DollarOutlined />}
                    suffix="元"
                    valueStyle={{ color: '#cf1322' }}
                  />
                </Col>
              </Row>
            </Card>
          </Col>
        </Row>
      )}
    </div>
  );
};

export default DashboardPage;
