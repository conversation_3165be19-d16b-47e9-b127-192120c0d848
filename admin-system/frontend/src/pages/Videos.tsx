import React, { useState, useRef, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  DatePicker,
  Tag,
  Image,
  Modal,
  Form,
  InputNumber,
  Switch,
  Upload,
  message,
  Popconfirm,
  Tooltip
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  UploadOutlined,
  PlayCircleOutlined
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { api } from '@/services/api';
import type { ColumnsType } from 'antd/es/table';
import Hls from 'hls.js';

const { RangePicker } = DatePicker;
const { Option } = Select;

interface Video {
  id: number;
  title: string;
  description: string;
  thumbnail: string;
  videoUrl: string;
  duration: number;
  categoryId: number;
  categoryName: string;
  price: number;
  status: 'published' | 'pending' | 'rejected';
  viewCount: number;
  likeCount: number;
  createdAt: string;
  updatedAt: string;
}

interface VideoFilters {
  page: number;
  pageSize: number;
  keyword?: string;
  categoryId?: number;
  status?: string;
  isPaid?: boolean;
  dateRange?: [string, string];
}

const VideosPage: React.FC = () => {
  const [filters, setFilters] = useState<VideoFilters>({
    page: 1,
    pageSize: 20
  });
  const [editingVideo, setEditingVideo] = useState<Video | null>(null);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isPreviewVisible, setIsPreviewVisible] = useState(false);
  const [previewVideo, setPreviewVideo] = useState<Video | null>(null);
  const [form] = Form.useForm();
  const queryClient = useQueryClient();
  const videoRef = useRef<HTMLVideoElement>(null);
  const hlsRef = useRef<Hls | null>(null);

  // 获取视频列表
  const { data: videosData, isLoading, error: videosError } = useQuery(
    ['videos', filters],
    () => api.get('/videos', { params: filters }),
    {
      keepPreviousData: true,
      onError: (error) => {
        console.error('获取视频列表失败:', error);
      }
    }
  );



  // 获取分类列表
  const { data: categoriesData } = useQuery(
    'categories',
    () => api.get('/videos/categories')
  );



  // 安全获取分类数据
  const getCategoriesData = () => {
    if (!categoriesData) return [];

    // 尝试不同的数据结构路径
    if (Array.isArray(categoriesData.data?.data)) {
      return categoriesData.data.data;
    }
    if (Array.isArray(categoriesData.data)) {
      return categoriesData.data;
    }
    if (Array.isArray(categoriesData)) {
      return categoriesData;
    }

    return [];
  };

  // 删除视频
  const deleteMutation = useMutation(
    (id: number) => api.delete(`/videos/${id}`),
    {
      onSuccess: () => {
        message.success('删除成功');
        queryClient.invalidateQueries('videos');
      },
      onError: () => {
        message.error('删除失败');
      }
    }
  );

  // 更新视频
  const updateMutation = useMutation(
    ({ id, data }: { id: number; data: any }) => api.put(`/videos/${id}`, data),
    {
      onSuccess: () => {
        message.success('更新成功');
        queryClient.invalidateQueries('videos');
        setIsModalVisible(false);
        setEditingVideo(null);
        form.resetFields();
      },
      onError: () => {
        message.error('更新失败');
      }
    }
  );

  // 表格列配置
  const columns: ColumnsType<Video> = [
    {
      title: 'ID',
      dataIndex: 'id',
      width: 80,
    },
    {
      title: '缩略图',
      dataIndex: 'cover',
      width: 100,
      render: (cover: string) => (
        <Image
          src={cover}
          alt="缩略图"
          width={60}
          height={40}
          style={{ objectFit: 'cover', borderRadius: 4 }}
          fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
        />
      ),
    },
    {
      title: '标题',
      dataIndex: 'title',
      ellipsis: true,
      render: (title: string) => (
        <Tooltip title={title}>
          <span>{title}</span>
        </Tooltip>
      ),
    },
    {
      title: '分类',
      dataIndex: 'category',
      width: 100,
    },
    {
      title: '价格',
      dataIndex: 'price',
      width: 80,
      render: (price: number) => (
        <span>{price === 0 ? '免费' : `${price}币`}</span>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 100,
      render: (status: string) => {
        const statusMap = {
          free: { color: 'green', text: '免费' },
          paid: { color: 'blue', text: '付费' },
          published: { color: 'green', text: '已发布' },
          pending: { color: 'orange', text: '待审核' },
          rejected: { color: 'red', text: '已拒绝' }
        };
        const config = statusMap[status as keyof typeof statusMap] || { color: 'default', text: status };
        return <Tag color={config.color}>{config.text}</Tag>;
      },
    },
    {
      title: '观看次数',
      dataIndex: 'viewCount',
      width: 100,
      sorter: true,
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      width: 180,
      render: (date: string) => new Date(date).toLocaleString(),
    },
    {
      title: '操作',
      key: 'actions',
      width: 150,
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            size="small"
            icon={<PlayCircleOutlined />}
            onClick={() => handlePreview(record)}
          >
            预览
          </Button>
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个视频吗？"
            onConfirm={() => deleteMutation.mutate(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              size="small"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 处理搜索
  const handleSearch = (values: any) => {
    setFilters({
      ...filters,
      page: 1,
      ...values,
      dateRange: values.dateRange ? [
        values.dateRange[0].format('YYYY-MM-DD'),
        values.dateRange[1].format('YYYY-MM-DD')
      ] : undefined
    });
  };

  // 处理编辑
  const handleEdit = (video: Video) => {
    setEditingVideo(video);
    form.setFieldsValue(video);
    setIsModalVisible(true);
  };

  // 处理保存
  const handleSave = (values: any) => {
    if (editingVideo) {
      updateMutation.mutate({
        id: editingVideo.id,
        data: values
      });
    }
  };

  // 处理视频预览
  const handlePreview = (video: Video) => {
    setPreviewVideo(video);
    setIsPreviewVisible(true);
  };

  // 初始化HLS播放器
  const initHlsPlayer = (videoUrl: string) => {
    if (!videoRef.current) return;

    // 清理之前的实例
    if (hlsRef.current) {
      hlsRef.current.destroy();
      hlsRef.current = null;
    }

    if (Hls.isSupported()) {
      const hls = new Hls({
        enableWorker: false,
        lowLatencyMode: true,
        backBufferLength: 90
      });

      hls.loadSource(videoUrl);
      hls.attachMedia(videoRef.current);

      hls.on(Hls.Events.MANIFEST_PARSED, () => {
        console.log('HLS manifest parsed, ready to play');
      });

      hls.on(Hls.Events.ERROR, (event, data) => {
        console.error('HLS error:', data);
        if (data.fatal) {
          switch (data.type) {
            case Hls.ErrorTypes.NETWORK_ERROR:
              message.error('网络错误，无法加载视频');
              break;
            case Hls.ErrorTypes.MEDIA_ERROR:
              message.error('媒体错误，视频格式不支持');
              break;
            default:
              message.error('播放器错误');
              break;
          }
        }
      });

      hlsRef.current = hls;
    } else if (videoRef.current.canPlayType('application/vnd.apple.mpegurl')) {
      // Safari原生支持HLS
      videoRef.current.src = videoUrl;
    } else {
      message.error('当前浏览器不支持HLS播放');
    }
  };

  // 清理HLS播放器
  const cleanupHlsPlayer = () => {
    if (hlsRef.current) {
      hlsRef.current.destroy();
      hlsRef.current = null;
    }
  };

  // 监听预览模态框的打开/关闭
  useEffect(() => {
    if (isPreviewVisible && previewVideo) {
      // 延迟初始化，确保DOM已渲染
      setTimeout(() => {
        initHlsPlayer(previewVideo.videoUrl);
      }, 100);
    } else {
      cleanupHlsPlayer();
    }

    return () => {
      cleanupHlsPlayer();
    };
  }, [isPreviewVisible, previewVideo]);

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      cleanupHlsPlayer();
    };
  }, []);

  return (
    <div style={{ padding: '24px' }}>
      <Card>
        <div style={{ marginBottom: '16px' }}>
          <Space wrap>
            <Input
              placeholder="搜索标题或描述"
              prefix={<SearchOutlined />}
              style={{ width: 200 }}
              onChange={(e) => setFilters({ ...filters, keyword: e.target.value })}
            />
            <Select
              placeholder="选择分类"
              style={{ width: 150 }}
              allowClear
              onChange={(value) => setFilters({ ...filters, categoryId: value })}
            >
              {getCategoriesData().map((category: any) => (
                <Option key={category.id} value={category.id}>
                  {category.name}
                </Option>
              ))}
            </Select>
            <Select
              placeholder="价格类型"
              style={{ width: 120 }}
              allowClear
              onChange={(value) => setFilters({ ...filters, status: value })}
            >
              <Option value="free">免费</Option>
              <Option value="paid">付费</Option>
            </Select>

            <RangePicker
              onChange={(dates) => setFilters({
                ...filters,
                dateRange: dates ? [
                  dates[0]!.format('YYYY-MM-DD'),
                  dates[1]!.format('YYYY-MM-DD')
                ] : undefined
              })}
            />
          </Space>
        </div>

        <Table
          columns={columns}
          dataSource={videosData?.data?.data?.items || []}
          rowKey="id"
          loading={isLoading}
          pagination={{
            current: filters.page,
            pageSize: filters.pageSize,
            total: videosData?.data?.data?.total || 0,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
            onChange: (page, pageSize) => setFilters({ ...filters, page, pageSize })
          }}
        />
      </Card>

      {/* 编辑模态框 */}
      <Modal
        title="编辑视频"
        open={isModalVisible}
        onCancel={() => {
          setIsModalVisible(false);
          setEditingVideo(null);
          form.resetFields();
        }}
        onOk={() => form.submit()}
        confirmLoading={updateMutation.isLoading}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSave}
        >
          <Form.Item
            name="title"
            label="标题"
            rules={[{ required: true, message: '请输入标题' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="description"
            label="描述"
          >
            <Input.TextArea rows={3} />
          </Form.Item>
          <Form.Item
            name="categoryId"
            label="分类"
            rules={[{ required: true, message: '请选择分类' }]}
          >
            <Select>
              {getCategoriesData().map((category: any) => (
                <Option key={category.id} value={category.id}>
                  {category.name}
                </Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item
            name="price"
            label="价格（币）"
            rules={[{ required: true, message: '请输入价格' }]}
          >
            <InputNumber min={0} style={{ width: '100%' }} />
          </Form.Item>
          <Form.Item
            name="status"
            label="价格类型"
            rules={[{ required: true, message: '请选择价格类型' }]}
          >
            <Select>
              <Option value="free">免费</Option>
              <Option value="paid">付费</Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>

      {/* 视频预览模态框 */}
      <Modal
        title={previewVideo ? `预览视频: ${previewVideo.title}` : '视频预览'}
        open={isPreviewVisible}
        onCancel={() => {
          setIsPreviewVisible(false);
          setPreviewVideo(null);
        }}
        footer={null}
        width={800}
        centered
        destroyOnClose
      >
        {previewVideo && (
          <div style={{ textAlign: 'center' }}>
            <video
              ref={videoRef}
              controls
              style={{
                width: '100%',
                maxHeight: '400px',
                backgroundColor: '#000'
              }}
              poster={previewVideo.cover}
              preload="metadata"
            >
              您的浏览器不支持视频播放
            </video>
            <div style={{ marginTop: '16px', textAlign: 'left' }}>
              <p><strong>标题:</strong> {previewVideo.title}</p>
              <p><strong>描述:</strong> {previewVideo.description}</p>
              <p><strong>分类:</strong> {previewVideo.category}</p>
              <p><strong>价格:</strong> {previewVideo.price === 0 ? '免费' : `${previewVideo.price}币`}</p>
              <p><strong>观看次数:</strong> {previewVideo.viewCount}</p>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default VideosPage;
