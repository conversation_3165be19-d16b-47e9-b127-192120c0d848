import React, { useState } from 'react';
import { Form, Input, But<PERSON>, Card, Typography, message, Spin } from 'antd';
import { UserOutlined, LockOutlined, LoginOutlined } from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuthStore } from '@/store/auth';
import './Login.less';

const { Title, Text } = Typography;

interface LoginForm {
  username: string;
  password: string;
}

const LoginPage: React.FC = () => {
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const location = useLocation();
  const { login, loading } = useAuthStore();
  const [submitting, setSubmitting] = useState(false);

  // 获取重定向路径
  const from = (location.state as any)?.from?.pathname || '/dashboard';

  const handleSubmit = async (values: LoginForm) => {
    try {
      setSubmitting(true);
      await login(values.username, values.password);
      
      message.success('登录成功');
      navigate(from, { replace: true });
    } catch (error: any) {
      console.error('登录失败:', error);
      // 错误信息已在API拦截器中处理
    } finally {
      setSubmitting(false);
    }
  };

  const handleFormKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      form.submit();
    }
  };

  return (
    <div className="login-container">
      <div className="login-background">
        <div className="login-overlay" />
      </div>
      
      <div className="login-content">
        <Card className="login-card" bordered={false}>
          <div className="login-header">
            <div className="login-logo">
              <LoginOutlined className="logo-icon" />
            </div>
            <Title level={2} className="login-title">
              SmartVideo 管理后台
            </Title>
            <Text type="secondary" className="login-subtitle">
              视频流媒体管理系统
            </Text>
          </div>

          <Spin spinning={loading || submitting} tip="登录中...">
            <Form
              form={form}
              name="login"
              size="large"
              onFinish={handleSubmit}
              onKeyPress={handleFormKeyPress}
              autoComplete="off"
              className="login-form"
            >
              <Form.Item
                name="username"
                rules={[
                  { required: true, message: '请输入用户名' },
                  { min: 3, message: '用户名至少3个字符' },
                  { max: 50, message: '用户名最多50个字符' },
                  { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线' }
                ]}
              >
                <Input
                  prefix={<UserOutlined />}
                  placeholder="请输入用户名"
                  autoComplete="username"
                />
              </Form.Item>

              <Form.Item
                name="password"
                rules={[
                  { required: true, message: '请输入密码' },
                  { min: 6, message: '密码至少6个字符' }
                ]}
              >
                <Input.Password
                  prefix={<LockOutlined />}
                  placeholder="请输入密码"
                  autoComplete="current-password"
                />
              </Form.Item>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  className="login-button"
                  loading={submitting}
                  block
                >
                  {submitting ? '登录中...' : '登录'}
                </Button>
              </Form.Item>
            </Form>
          </Spin>

          <div className="login-footer">
            <Text type="secondary" className="login-tips">
              默认管理员账号：admin / admin123
            </Text>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default LoginPage;
