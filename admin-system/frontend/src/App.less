// 全局样式重置
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-size: 14px;
  line-height: 1.5715;
  color: rgba(0, 0, 0, 0.85);
  background-color: #f0f2f5;
}

#root {
  height: 100%;
}

.app {
  height: 100%;
  min-height: 100vh;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
  
  &:hover {
    background: #a8a8a8;
  }
}

// 通用工具类
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.full-width {
  width: 100%;
}

.full-height {
  height: 100%;
}

.mb-16 {
  margin-bottom: 16px;
}

.mb-24 {
  margin-bottom: 24px;
}

.mt-16 {
  margin-top: 16px;
}

.mt-24 {
  margin-top: 24px;
}

.p-16 {
  padding: 16px;
}

.p-24 {
  padding: 24px;
}

// 卡片样式增强
.ant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  
  .ant-card-head {
    border-bottom: 1px solid #f0f0f0;
    
    .ant-card-head-title {
      font-weight: 600;
    }
  }
}

// 表格样式增强
.ant-table {
  .ant-table-thead > tr > th {
    background-color: #fafafa;
    font-weight: 600;
  }
  
  .ant-table-tbody > tr:hover > td {
    background-color: #f5f5f5;
  }
}

// 按钮样式增强
.ant-btn {
  border-radius: 6px;
  font-weight: 500;
  
  &.ant-btn-primary {
    box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);
    
    &:hover {
      box-shadow: 0 4px 8px rgba(24, 144, 255, 0.3);
    }
  }
}

// 输入框样式增强
.ant-input,
.ant-input-affix-wrapper {
  border-radius: 6px;
  
  &:hover {
    border-color: #40a9ff;
  }
  
  &:focus,
  &.ant-input-affix-wrapper-focused {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }
}

// 选择器样式增强
.ant-select {
  .ant-select-selector {
    border-radius: 6px;
  }
  
  &:hover .ant-select-selector {
    border-color: #40a9ff;
  }
  
  &.ant-select-focused .ant-select-selector {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }
}

// 标签样式
.status-tag {
  border-radius: 4px;
  font-weight: 500;
  
  &.success {
    background-color: #f6ffed;
    border-color: #b7eb8f;
    color: #52c41a;
  }
  
  &.warning {
    background-color: #fffbe6;
    border-color: #ffe58f;
    color: #faad14;
  }
  
  &.error {
    background-color: #fff2f0;
    border-color: #ffccc7;
    color: #f5222d;
  }
  
  &.processing {
    background-color: #e6f7ff;
    border-color: #91d5ff;
    color: #1890ff;
  }
}

// 统计卡片样式
.stat-card {
  .ant-card-body {
    padding: 24px;
  }
  
  .stat-title {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.45);
    margin-bottom: 8px;
  }
  
  .stat-value {
    font-size: 30px;
    font-weight: 600;
    color: rgba(0, 0, 0, 0.85);
    margin-bottom: 8px;
  }
  
  .stat-extra {
    font-size: 12px;
    color: rgba(0, 0, 0, 0.45);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .ant-table {
    font-size: 12px;
  }
  
  .ant-card {
    margin-bottom: 16px;
  }
  
  .stat-card .stat-value {
    font-size: 24px;
  }
}

// 加载动画
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  
  .ant-spin {
    .ant-spin-text {
      margin-top: 12px;
      color: rgba(0, 0, 0, 0.45);
    }
  }
}

// 空状态样式
.empty-container {
  padding: 40px 20px;
  text-align: center;
  
  .ant-empty-description {
    color: rgba(0, 0, 0, 0.45);
  }
}

// 错误页面样式
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: 40px 20px;
  
  .error-icon {
    font-size: 64px;
    color: #f5222d;
    margin-bottom: 24px;
  }
  
  .error-title {
    font-size: 24px;
    font-weight: 600;
    color: rgba(0, 0, 0, 0.85);
    margin-bottom: 16px;
  }
  
  .error-description {
    font-size: 16px;
    color: rgba(0, 0, 0, 0.45);
    margin-bottom: 24px;
    text-align: center;
    max-width: 400px;
  }
}
