{"name": "smartvideo-admin-frontend", "version": "1.0.0", "description": "SmartVideoWeb后台管理系统 - 前端界面", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:check": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "type-check": "tsc --noEmit"}, "keywords": ["admin", "frontend", "video", "management", "react", "typescript", "antd"], "author": "SmartVideoWeb Team", "license": "MIT", "dependencies": {"@ant-design/icons": "^5.2.6", "@ant-design/plots": "^2.4.0", "@ant-design/pro-components": "^2.6.48", "ahooks": "^3.7.8", "antd": "^5.12.8", "axios": "^1.6.2", "classnames": "^2.3.2", "dayjs": "^1.11.13", "hls.js": "^1.6.4", "immer": "^10.0.3", "lodash-es": "^4.17.21", "react": "^18.2.0", "react-dom": "^18.2.0", "react-error-boundary": "^4.0.11", "react-helmet-async": "^2.0.4", "react-query": "^3.39.3", "react-router-dom": "^6.20.1", "recharts": "^2.8.0", "uuid": "^9.0.1", "zustand": "^4.4.7"}, "devDependencies": {"@types/lodash-es": "^4.17.12", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "less": "^4.2.0", "terser": "^5.41.0", "typescript": "^5.2.2", "vite": "^5.0.8", "vite-plugin-eslint": "^1.8.1"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}