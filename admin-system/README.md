# SmartVideoWeb 后台管理系统

## 🎯 系统概述

为SmartVideoWeb视频流媒体项目设计的现代化后台管理系统，提供完整的业务数据管理功能。

## 🏗️ 技术架构

### 后端技术栈
- **框架**: Node.js + TypeScript + Koa
- **数据库**: Sequelize ORM + MySQL
- **认证**: JWT + RBAC权限控制
- **API**: RESTful API设计

### 前端技术栈
- **框架**: React 18 + TypeScript + Vite
- **UI库**: Ant Design 5.x
- **路由**: React Router DOM v6
- **状态管理**: Zustand
- **图表**: ECharts/Recharts
- **表格**: Ant Design Table (支持虚拟滚动)

## 📊 功能模块

### 1. 仪表板 (Dashboard)
- **数据概览**: 用户数、视频数、订单数、收入统计
- **实时监控**: 在线用户、播放量、转化率
- **图表分析**: 趋势图、饼图、柱状图
- **快捷操作**: 常用功能入口

### 2. 用户管理 (User Management)
- **用户列表**: 分页查询、搜索过滤
- **用户详情**: 基本信息、VIP状态、消费记录
- **用户操作**: 禁用/启用、VIP管理、金币调整
- **游客管理**: 游客账号转换、设备绑定

### 3. 视频管理 (Video Management)
- **视频列表**: 批量操作、状态管理
- **视频上传**: 文件上传、信息编辑
- **分类管理**: 分类CRUD、层级管理
- **内容审核**: 审核流程、状态跟踪

### 4. 订单管理 (Order Management)
- **订单列表**: 多条件筛选、状态管理
- **订单详情**: 完整订单信息、操作日志
- **退款管理**: 退款申请、处理流程
- **财务统计**: 收入分析、对账功能

### 5. 配置管理 (Configuration)
- **系统配置**: VIP套餐、充值选项、支付方式
- **内容配置**: 轮播图、推荐位、公告
- **参数设置**: 系统参数、业务规则
- **缓存管理**: 缓存清理、预热

### 6. 邀请系统 (Invitation System)
- **邀请统计**: 邀请数据、转化分析
- **佣金管理**: 佣金计算、发放记录
- **层级管理**: 多级邀请关系
- **奖励配置**: 邀请奖励规则

### 7. 系统管理 (System Management)
- **管理员管理**: 账号管理、权限分配
- **角色权限**: RBAC权限体系
- **操作日志**: 管理员操作记录
- **系统监控**: 服务状态、性能监控

## 🔐 权限设计

### 角色定义
```typescript
enum AdminRole {
  SUPER_ADMIN = 'super_admin',    // 超级管理员
  ADMIN = 'admin',                // 管理员
  OPERATOR = 'operator',          // 运营人员
  VIEWER = 'viewer'               // 只读用户
}
```

### 权限模块
```typescript
enum Permission {
  // 用户管理
  USER_VIEW = 'user:view',
  USER_EDIT = 'user:edit',
  USER_DELETE = 'user:delete',
  
  // 视频管理
  VIDEO_VIEW = 'video:view',
  VIDEO_EDIT = 'video:edit',
  VIDEO_DELETE = 'video:delete',
  VIDEO_UPLOAD = 'video:upload',
  
  // 订单管理
  ORDER_VIEW = 'order:view',
  ORDER_EDIT = 'order:edit',
  ORDER_REFUND = 'order:refund',
  
  // 系统管理
  SYSTEM_CONFIG = 'system:config',
  ADMIN_MANAGE = 'admin:manage',
  LOG_VIEW = 'log:view'
}
```

## 📁 项目结构

```
admin-system/
├── backend/                 # 后端API服务
│   ├── src/
│   │   ├── controllers/     # 控制器
│   │   ├── models/         # 数据模型
│   │   ├── routes/         # 路由定义
│   │   ├── middleware/     # 中间件
│   │   ├── services/       # 业务服务
│   │   ├── utils/          # 工具函数
│   │   └── config/         # 配置文件
│   ├── package.json
│   └── tsconfig.json
├── frontend/               # 前端管理界面
│   ├── src/
│   │   ├── components/     # 通用组件
│   │   ├── pages/          # 页面组件
│   │   ├── hooks/          # 自定义Hooks
│   │   ├── services/       # API服务
│   │   ├── store/          # 状态管理
│   │   ├── utils/          # 工具函数
│   │   └── types/          # 类型定义
│   ├── package.json
│   └── vite.config.ts
├── shared/                 # 共享类型和工具
│   ├── types/              # 共享类型定义
│   └── utils/              # 共享工具函数
└── docs/                   # 文档
    ├── api.md              # API文档
    ├── deployment.md       # 部署文档
    └── development.md      # 开发文档
```

## 🚀 快速开始

### 环境要求
- Node.js >= 16
- MySQL >= 8.0
- npm 或 yarn

### 安装依赖
```bash
# 安装后端依赖
cd admin-system/backend
npm install

# 安装前端依赖
cd ../frontend
npm install
```

### 数据库配置
```bash
# 创建数据库
mysql -u root -p
CREATE DATABASE smartvideo_admin CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 运行迁移
cd backend
npm run migrate
npm run seed
```

### 启动服务
```bash
# 启动后端服务 (端口: 3003)
cd backend
npm run dev

# 启动前端服务 (端口: 3001)
cd frontend
npm run dev
```

### 默认管理员账号
```
用户名: admin
密码: admin123
角色: 超级管理员
```

## 📈 核心特性

### 1. 响应式设计
- 支持桌面端和平板端
- 自适应布局和组件
- 移动端友好的操作体验

### 2. 高性能表格
- 虚拟滚动支持大数据量
- 智能分页和懒加载
- 多列排序和筛选

### 3. 实时数据
- WebSocket实时推送
- 数据自动刷新
- 状态实时同步

### 4. 安全保障
- JWT令牌认证
- RBAC权限控制
- 操作日志记录
- 敏感数据脱敏

### 5. 用户体验
- 统一的设计语言
- 流畅的交互动画
- 智能的错误处理
- 完善的加载状态

## 🔧 开发指南

### API设计规范
- RESTful API设计
- 统一的响应格式
- 完善的错误码体系
- 详细的API文档

### 代码规范
- TypeScript严格模式
- ESLint + Prettier
- 统一的命名规范
- 完善的类型定义

### 测试策略
- 单元测试覆盖
- 集成测试验证
- E2E测试保障
- 性能测试监控

## 📝 更新日志

### v1.0.0 (2024-01-30)
- 🎉 初始版本发布
- ✨ 完整的管理功能
- 🔐 权限控制系统
- 📊 数据统计分析
- 🎨 现代化UI设计

## 📞 技术支持

如有问题或建议，请联系开发团队或提交Issue。
