# 🚀 SmartVideoWeb 后台管理系统 - 快速启动指南

## 📋 系统概述

SmartVideoWeb后台管理系统是为视频流媒体项目设计的现代化管理平台，提供用户管理、视频管理、订单管理、配置管理等完整功能。

### 🏗️ 技术架构
- **后端**: Node.js + TypeScript + Koa + Sequelize + MySQL
- **前端**: React + TypeScript + Vite + Ant Design
- **数据库**: MySQL 8.0
- **认证**: JWT + RBAC权限控制

### 🌟 核心功能
- 📊 **仪表板** - 数据统计和实时监控
- 👥 **用户管理** - 用户信息、VIP状态、金币管理
- 🎬 **视频管理** - 视频上传、审核、分类管理
- 💰 **订单管理** - 充值订单、VIP订单、退款处理
- ⚙️ **配置管理** - 系统配置、业务参数设置
- 🔐 **权限管理** - 角色权限、操作日志

## 🛠️ 环境要求

### 必需软件
- **Node.js** >= 16.0.0
- **npm** >= 8.0.0
- **MySQL** >= 8.0

### 推荐工具
- **VS Code** - 代码编辑器
- **MySQL Workbench** - 数据库管理
- **Postman** - API测试

## ⚡ 快速启动

### 1. 克隆项目
```bash
# 如果是独立项目
git clone <repository-url>
cd admin-system

# 如果是在现有项目中
cd smartVideoWeb/admin-system
```

### 2. 数据库准备
```bash
# 登录MySQL
mysql -u root -p

# 创建数据库
CREATE DATABASE smartvideo_admin CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 退出MySQL
exit
```

### 3. 一键安装和启动
```bash
# 安装所有依赖
npm run install:all

# 初始化数据库
npm run setup:backend

# 启动开发服务器
npm run dev
```

### 4. 访问系统
- **前端管理界面**: http://localhost:3001
- **后端API服务**: http://localhost:3003
- **API文档**: http://localhost:3003/api

### 5. 默认登录账号
```
超级管理员:
用户名: admin
密码: admin123

运营人员:
用户名: operator  
密码: admin123

只读用户:
用户名: viewer
密码: admin123
```

## 📁 项目结构

```
admin-system/
├── backend/                 # 后端API服务
│   ├── src/
│   │   ├── controllers/     # 控制器
│   │   ├── models/         # 数据模型
│   │   ├── routes/         # 路由定义
│   │   ├── middleware/     # 中间件
│   │   ├── services/       # 业务服务
│   │   ├── scripts/        # 脚本文件
│   │   ├── config/         # 配置文件
│   │   └── types/          # 类型定义
│   └── package.json
├── frontend/               # 前端管理界面
│   ├── src/
│   │   ├── components/     # 通用组件
│   │   ├── pages/          # 页面组件
│   │   ├── services/       # API服务
│   │   ├── store/          # 状态管理
│   │   ├── types/          # 类型定义
│   │   └── utils/          # 工具函数
│   └── package.json
└── package.json            # 根配置文件
```

## 🔧 开发指南

### 后端开发
```bash
# 进入后端目录
cd backend

# 启动开发服务器
npm run dev

# 数据库迁移
npm run migrate

# 重新生成种子数据
npm run seed

# 代码检查
npm run lint

# 运行测试
npm test
```

### 前端开发
```bash
# 进入前端目录
cd frontend

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build

# 预览构建结果
npm run preview

# 代码检查
npm run lint
```

### 数据库配置
如需修改数据库连接，编辑 `backend/src/config/database.ts`:

```typescript
const config = {
  development: {
    host: 'localhost',
    port: 3306,
    database: 'smartvideo_admin',
    username: 'root',
    password: 'your_password'
  }
}
```

## 🎯 功能模块

### 1. 认证系统
- JWT令牌认证
- 角色权限控制
- 密码加密存储
- 登录日志记录

### 2. 用户管理
- 用户列表查询
- 用户信息编辑
- VIP状态管理
- 金币余额调整

### 3. 视频管理
- 视频上传审核
- 分类层级管理
- 内容状态控制
- 批量操作支持

### 4. 订单管理
- 订单状态跟踪
- 退款流程处理
- 财务数据统计
- 导出功能支持

### 5. 配置管理
- VIP套餐配置
- 充值选项设置
- 支付方式管理
- 系统参数调整

## 🔐 权限体系

### 角色定义
- **超级管理员** (super_admin) - 所有权限
- **管理员** (admin) - 业务管理权限
- **运营人员** (operator) - 内容运营权限
- **只读用户** (viewer) - 查看权限

### 权限模块
- 仪表板查看
- 用户管理
- 视频管理
- 订单管理
- 配置管理
- 系统管理

## 📊 监控和日志

### 操作日志
- 管理员操作记录
- API请求日志
- 错误日志追踪
- 性能监控数据

### 系统监控
- 服务器状态
- 数据库连接
- 内存使用情况
- 响应时间统计

## 🚀 部署指南

### 开发环境
```bash
# 启动所有服务
npm run dev

# 分别启动
npm run dev:backend
npm run dev:frontend
```

### 生产环境
```bash
# 构建项目
npm run build

# 启动生产服务
npm run start
```

### Docker部署
```bash
# 构建镜像
docker build -t smartvideo-admin .

# 运行容器
docker run -p 3001:3001 -p 3003:3003 smartvideo-admin
```

## 🔍 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查MySQL服务是否启动
   - 确认数据库配置正确
   - 验证用户权限

2. **前端无法访问后端**
   - 检查后端服务是否启动
   - 确认端口配置正确
   - 检查CORS设置

3. **登录失败**
   - 确认数据库已初始化
   - 检查默认账号是否创建
   - 验证JWT配置

### 日志查看
```bash
# 后端日志
cd backend && npm run dev

# 前端日志
cd frontend && npm run dev

# 数据库日志
tail -f /var/log/mysql/error.log
```

## 📞 技术支持

如遇到问题，请：
1. 查看控制台错误信息
2. 检查网络连接状态
3. 确认服务启动状态
4. 联系技术支持团队

## 🎉 开始使用

现在您可以：
1. 访问 http://localhost:3001 进入管理后台
2. 使用默认账号登录系统
3. 探索各个功能模块
4. 根据需要进行配置调整

祝您使用愉快！🎊
