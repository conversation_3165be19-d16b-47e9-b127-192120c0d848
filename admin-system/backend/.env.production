# Server Configuration
PORT=3003
NODE_ENV=production

# Database Configuration
DB_HOST=*************
DB_USER=hebendb
DB_PASSWORD=5DyJmZjaYMFEpMBy
DB_NAME=hebendb
DB_PORT=62102

# Database Pool Configuration
DB_POOL_MAX=20
DB_POOL_MIN=5
DB_POOL_ACQUIRE=60000
DB_POOL_IDLE=10000
DB_CONNECT_TIMEOUT=60000

# JWT Configuration
JWT_SECRET=admin-system-jwt-secret-key-change-this-in-production
JWT_EXPIRES_IN=7d

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# CORS Configuration
CORS_ORIGIN=http://localhost:3001
