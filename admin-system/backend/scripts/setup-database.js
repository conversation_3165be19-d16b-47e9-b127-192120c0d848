const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

async function setupDatabase() {
  const connection = await mysql.createConnection({
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || 'GJiashuai123',
    database: process.env.DB_NAME || 'hepburn',
    port: parseInt(process.env.DB_PORT || '3306')
  });

  try {
    console.log('🔧 开始创建数据库表...');

    // 1. 创建邀请码表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS \`invitation_codes\` (
        \`id\` int(11) NOT NULL AUTO_INCREMENT,
        \`code\` varchar(20) NOT NULL UNIQUE COMMENT '邀请码',
        \`createdBy\` int(11) NOT NULL COMMENT '创建者ID',
        \`maxUses\` int(11) DEFAULT 100 COMMENT '最大使用次数',
        \`usedCount\` int(11) DEFAULT 0 COMMENT '已使用次数',
        \`expiresAt\` datetime DEFAULT NULL COMMENT '过期时间',
        \`isActive\` tinyint(1) DEFAULT 1 COMMENT '是否启用',
        \`createdAt\` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
        \`updatedAt\` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (\`id\`),
        KEY \`idx_code\` (\`code\`),
        KEY \`idx_created_by\` (\`createdBy\`),
        KEY \`idx_active\` (\`isActive\`)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='邀请码表'
    `);
    console.log('✅ 邀请码表创建成功');

    // 2. 创建邀请关系表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS \`invitation_relations\` (
        \`id\` int(11) NOT NULL AUTO_INCREMENT,
        \`inviterId\` int(11) NOT NULL COMMENT '邀请人ID',
        \`inviteeId\` int(11) NOT NULL COMMENT '被邀请人ID',
        \`inviteCode\` varchar(20) NOT NULL COMMENT '使用的邀请码',
        \`level\` int(11) DEFAULT 1 COMMENT '邀请层级',
        \`status\` enum('active','inactive') DEFAULT 'active' COMMENT '关系状态',
        \`createdAt\` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
        \`updatedAt\` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (\`id\`),
        UNIQUE KEY \`unique_invitee\` (\`inviteeId\`),
        KEY \`idx_inviter\` (\`inviterId\`),
        KEY \`idx_invite_code\` (\`inviteCode\`),
        KEY \`idx_level\` (\`level\`)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='邀请关系表'
    `);
    console.log('✅ 邀请关系表创建成功');

    // 3. 创建佣金记录表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS \`commissions\` (
        \`id\` int(11) NOT NULL AUTO_INCREMENT,
        \`userId\` int(11) NOT NULL COMMENT '获得佣金的用户ID',
        \`fromUserId\` int(11) NOT NULL COMMENT '产生佣金的用户ID',
        \`orderId\` int(11) NOT NULL COMMENT '关联订单ID',
        \`commissionAmount\` decimal(10,2) NOT NULL COMMENT '佣金金额',
        \`commissionRate\` decimal(5,4) NOT NULL COMMENT '佣金比例',
        \`level\` int(11) NOT NULL COMMENT '佣金层级',
        \`status\` enum('pending','paid','cancelled') DEFAULT 'pending' COMMENT '佣金状态',
        \`paidAt\` datetime DEFAULT NULL COMMENT '支付时间',
        \`createdAt\` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
        \`updatedAt\` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (\`id\`),
        KEY \`idx_user_id\` (\`userId\`),
        KEY \`idx_from_user_id\` (\`fromUserId\`),
        KEY \`idx_order_id\` (\`orderId\`),
        KEY \`idx_status\` (\`status\`),
        KEY \`idx_level\` (\`level\`)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='佣金记录表'
    `);
    console.log('✅ 佣金记录表创建成功');

    // 4. 创建分类表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS \`categories\` (
        \`id\` int(11) NOT NULL AUTO_INCREMENT,
        \`name\` varchar(50) NOT NULL COMMENT '分类名称',
        \`slug\` varchar(50) NOT NULL UNIQUE COMMENT '分类标识符',
        \`description\` text COMMENT '分类描述',
        \`parentId\` int(11) DEFAULT NULL COMMENT '父分类ID',
        \`sort\` int(11) DEFAULT 0 COMMENT '排序',
        \`isActive\` tinyint(1) DEFAULT 1 COMMENT '是否启用',
        \`createdAt\` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
        \`updatedAt\` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (\`id\`),
        UNIQUE KEY \`unique_slug\` (\`slug\`),
        KEY \`idx_parent_id\` (\`parentId\`),
        KEY \`idx_active\` (\`isActive\`),
        KEY \`idx_sort\` (\`sort\`)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分类表'
    `);
    console.log('✅ 分类表创建成功');

    // 5. 检查并添加orders表的remark字段
    const [columns] = await connection.execute(`
      SELECT COLUMN_NAME
      FROM INFORMATION_SCHEMA.COLUMNS
      WHERE TABLE_SCHEMA = 'hepburn'
      AND TABLE_NAME = 'orders'
      AND COLUMN_NAME = 'remark'
    `);

    if (columns.length === 0) {
      await connection.execute(`
        ALTER TABLE \`orders\`
        ADD COLUMN \`remark\` varchar(255) DEFAULT NULL COMMENT '备注' AFTER \`status\`
      `);
      console.log('✅ orders表添加remark字段成功');
    } else {
      console.log('✅ orders表remark字段已存在');
    }

    // 6. 插入默认分类数据
    await connection.execute(`
      INSERT IGNORE INTO \`categories\` (\`name\`, \`slug\`, \`description\`, \`parentId\`, \`sort\`, \`isActive\`) VALUES
      ('电影', 'movie', '电影分类', NULL, 1, 1),
      ('电视剧', 'tv-series', '电视剧分类', NULL, 2, 1),
      ('动漫', 'anime', '动漫分类', NULL, 3, 1),
      ('纪录片', 'documentary', '纪录片分类', NULL, 4, 1),
      ('综艺', 'variety', '综艺节目分类', NULL, 5, 1),
      ('短视频', 'short-video', '短视频分类', NULL, 6, 1),
      ('其他', 'other', '其他分类', NULL, 99, 1),
      ('未分类', 'uncategorized', '未分类内容', NULL, 100, 1)
    `);
    console.log('✅ 默认分类数据插入成功');

    // 7. 插入测试邀请码数据
    await connection.execute(`
      INSERT IGNORE INTO \`invitation_codes\` (\`code\`, \`createdBy\`, \`maxUses\`, \`expiresAt\`, \`isActive\`) VALUES
      ('WELCOME2025', 1, 1000, '2025-12-31 23:59:59', 1),
      ('VIP100', 1, 100, '2025-06-30 23:59:59', 1),
      ('NEWUSER', 1, 500, NULL, 1)
    `);
    console.log('✅ 测试邀请码数据插入成功');

    console.log('✅ 数据库设置完成！');

    // 验证表是否创建成功
    const dbName = process.env.DB_NAME || 'hepburn';
    const [tables] = await connection.execute(`
      SELECT TABLE_NAME
      FROM INFORMATION_SCHEMA.TABLES
      WHERE TABLE_SCHEMA = ?
      AND TABLE_NAME IN ('invitation_codes', 'invitation_relations', 'commissions', 'categories')
    `, [dbName]);

    console.log('📋 已创建的表:');
    tables.forEach(table => {
      console.log(`  ✓ ${table.TABLE_NAME}`);
    });

    // 检查默认数据
    const [categories] = await connection.execute('SELECT COUNT(*) as count FROM categories');
    const [inviteCodes] = await connection.execute('SELECT COUNT(*) as count FROM invitation_codes');

    console.log(`📊 数据统计:`);
    console.log(`  分类数量: ${categories[0].count}`);
    console.log(`  邀请码数量: ${inviteCodes[0].count}`);

  } catch (error) {
    console.error('❌ 数据库设置失败:', error.message);
    process.exit(1);
  } finally {
    await connection.end();
  }
}

setupDatabase();
