{"name": "smartvideo-admin-backend", "version": "1.0.0", "description": "SmartVideoWeb后台管理系统 - 后端API服务", "main": "dist/index.js", "scripts": {"dev": "nodemon --exec ts-node -r tsconfig-paths/register src/index.ts", "build": "tsc", "start": "node dist/index.js", "migrate": "ts-node -r tsconfig-paths/register src/scripts/migrate.ts", "seed": "ts-node -r tsconfig-paths/register src/scripts/seed.ts", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix"}, "keywords": ["admin", "backend", "video", "management", "typescript", "koa"], "author": "SmartVideoWeb Team", "license": "MIT", "dependencies": {"@koa/cors": "^4.0.0", "@koa/router": "^12.0.1", "axios": "^1.9.0", "bcryptjs": "^2.4.3", "dotenv": "^16.5.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "koa": "^2.14.2", "koa-body": "^6.0.1", "koa-helmet": "^7.0.2", "koa-logger": "^3.2.1", "koa-ratelimit": "^5.1.0", "lodash": "^4.17.21", "moment": "^2.29.4", "multer": "^1.4.5-lts.1", "mysql2": "^3.6.5", "redis": "^4.6.12", "sequelize": "^6.35.2", "sharp": "^0.33.1", "uuid": "^9.0.1", "ws": "^8.16.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/koa": "^2.13.12", "@types/koa__cors": "^4.0.0", "@types/koa__router": "^12.0.4", "@types/koa-logger": "^3.1.5", "@types/lodash": "^4.14.202", "@types/multer": "^1.4.11", "@types/node": "^20.10.6", "@types/uuid": "^9.0.7", "@types/ws": "^8.5.10", "@typescript-eslint/eslint-plugin": "^6.17.0", "@typescript-eslint/parser": "^6.17.0", "eslint": "^8.56.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.3.3"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}