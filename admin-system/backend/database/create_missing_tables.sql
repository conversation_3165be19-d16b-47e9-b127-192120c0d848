-- 创建缺失的数据库表

-- 1. 邀请码表
CREATE TABLE IF NOT EXISTS `invitation_codes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `code` varchar(20) NOT NULL UNIQUE COMMENT '邀请码',
  `createdBy` int(11) NOT NULL COMMENT '创建者ID',
  `maxUses` int(11) DEFAULT 100 COMMENT '最大使用次数',
  `usedCount` int(11) DEFAULT 0 COMMENT '已使用次数',
  `expiresAt` datetime DEFAULT NULL COMMENT '过期时间',
  `isActive` tinyint(1) DEFAULT 1 COMMENT '是否启用',
  `createdAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_code` (`code`),
  KEY `idx_created_by` (`createdBy`),
  KEY `idx_active` (`isActive`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='邀请码表';

-- 2. 邀请关系表
CREATE TABLE IF NOT EXISTS `invitation_relations` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `inviterId` int(11) NOT NULL COMMENT '邀请人ID',
  `inviteeId` int(11) NOT NULL COMMENT '被邀请人ID',
  `inviteCode` varchar(20) NOT NULL COMMENT '使用的邀请码',
  `level` int(11) DEFAULT 1 COMMENT '邀请层级',
  `status` enum('active','inactive') DEFAULT 'active' COMMENT '关系状态',
  `createdAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_invitee` (`inviteeId`),
  KEY `idx_inviter` (`inviterId`),
  KEY `idx_invite_code` (`inviteCode`),
  KEY `idx_level` (`level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='邀请关系表';

-- 3. 佣金记录表
CREATE TABLE IF NOT EXISTS `commissions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `userId` int(11) NOT NULL COMMENT '获得佣金的用户ID',
  `fromUserId` int(11) NOT NULL COMMENT '产生佣金的用户ID',
  `orderId` int(11) NOT NULL COMMENT '关联订单ID',
  `commissionAmount` decimal(10,2) NOT NULL COMMENT '佣金金额',
  `commissionRate` decimal(5,4) NOT NULL COMMENT '佣金比例',
  `level` int(11) NOT NULL COMMENT '佣金层级',
  `status` enum('pending','paid','cancelled') DEFAULT 'pending' COMMENT '佣金状态',
  `paidAt` datetime DEFAULT NULL COMMENT '支付时间',
  `createdAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`userId`),
  KEY `idx_from_user_id` (`fromUserId`),
  KEY `idx_order_id` (`orderId`),
  KEY `idx_status` (`status`),
  KEY `idx_level` (`level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='佣金记录表';

-- 4. 分类表
CREATE TABLE IF NOT EXISTS `categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL COMMENT '分类名称',
  `slug` varchar(50) NOT NULL UNIQUE COMMENT '分类标识符',
  `description` text COMMENT '分类描述',
  `parentId` int(11) DEFAULT NULL COMMENT '父分类ID',
  `sort` int(11) DEFAULT 0 COMMENT '排序',
  `isActive` tinyint(1) DEFAULT 1 COMMENT '是否启用',
  `createdAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_slug` (`slug`),
  KEY `idx_parent_id` (`parentId`),
  KEY `idx_active` (`isActive`),
  KEY `idx_sort` (`sort`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分类表';

-- 5. 为orders表添加remark字段（如果不存在）
-- 注意：MySQL不支持ADD COLUMN IF NOT EXISTS，需要先检查
SET @sql = (SELECT IF(
  (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
   WHERE TABLE_SCHEMA = 'hepburn' AND TABLE_NAME = 'orders' AND COLUMN_NAME = 'remark') = 0,
  'ALTER TABLE `orders` ADD COLUMN `remark` varchar(255) DEFAULT NULL COMMENT ''备注'' AFTER `status`',
  'SELECT ''Column remark already exists'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 6. 插入一些默认分类数据
INSERT IGNORE INTO `categories` (`name`, `slug`, `description`, `parentId`, `sort`, `isActive`) VALUES
('电影', 'movie', '电影分类', NULL, 1, 1),
('电视剧', 'tv-series', '电视剧分类', NULL, 2, 1),
('动漫', 'anime', '动漫分类', NULL, 3, 1),
('纪录片', 'documentary', '纪录片分类', NULL, 4, 1),
('综艺', 'variety', '综艺节目分类', NULL, 5, 1),
('短视频', 'short-video', '短视频分类', NULL, 6, 1),
('其他', 'other', '其他分类', NULL, 99, 1),
('未分类', 'uncategorized', '未分类内容', NULL, 100, 1);

-- 7. 插入一些测试邀请码数据
INSERT IGNORE INTO `invitation_codes` (`code`, `createdBy`, `maxUses`, `expiresAt`, `isActive`) VALUES
('WELCOME2025', 1, 1000, '2025-12-31 23:59:59', 1),
('VIP100', 1, 100, '2025-06-30 23:59:59', 1),
('NEWUSER', 1, 500, NULL, 1);

-- 8. 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS `idx_user_videos_type_user` ON `user_videos` (`type`, `userId`);
CREATE INDEX IF NOT EXISTS `idx_user_videos_type_video` ON `user_videos` (`type`, `videoId`);
CREATE INDEX IF NOT EXISTS `idx_user_videos_created` ON `user_videos` (`createdAt`);
CREATE INDEX IF NOT EXISTS `idx_orders_status_created` ON `orders` (`status`, `createdAt`);
CREATE INDEX IF NOT EXISTS `idx_orders_type_created` ON `orders` (`type`, `createdAt`);
CREATE INDEX IF NOT EXISTS `idx_videos_category` ON `videos` (`category`);
CREATE INDEX IF NOT EXISTS `idx_videos_status_created` ON `videos` (`status`, `createdAt`);
CREATE INDEX IF NOT EXISTS `idx_users_created` ON `users` (`createdAt`);
CREATE INDEX IF NOT EXISTS `idx_users_last_login` ON `users` (`lastLoginAt`);

-- 完成
SELECT 'Database tables created successfully!' as message;
