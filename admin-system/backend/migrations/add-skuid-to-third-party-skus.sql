-- 添加skuid字段到third_party_skus表
-- 执行时间: 2025-06-03

-- 添加skuid字段
ALTER TABLE `third_party_skus` 
ADD COLUMN `skuid` VARCHAR(255) NOT NULL COMMENT '第三方平台SKU ID' AFTER `id`;

-- 添加唯一索引
ALTER TABLE `third_party_skus` 
ADD UNIQUE INDEX `idx_skuid` (`skuid`);

-- 为现有数据添加默认的skuid值（基于id生成）
UPDATE `third_party_skus` 
SET `skuid` = CONCAT('sku_', `id`) 
WHERE `skuid` = '' OR `skuid` IS NULL;

-- 验证数据
SELECT id, skuid, name, price, coins FROM `third_party_skus` ORDER BY id;
