{"name": "smartvideo-admin-system", "version": "1.0.0", "description": "SmartVideoWeb后台管理系统", "private": true, "scripts": {"install:all": "npm run install:backend && npm run install:frontend", "install:backend": "cd backend && npm install", "install:frontend": "cd frontend && npm install", "dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "build": "npm run build:backend && npm run build:frontend", "build:backend": "cd backend && npm run build", "build:frontend": "cd frontend && npm run build", "start": "concurrently \"npm run start:backend\" \"npm run start:frontend\"", "start:backend": "cd backend && npm start", "start:frontend": "cd frontend && npm run preview", "setup": "npm run install:all && npm run setup:backend", "setup:backend": "cd backend && npm run migrate && npm run seed", "lint": "npm run lint:backend && npm run lint:frontend", "lint:backend": "cd backend && npm run lint", "lint:frontend": "cd frontend && npm run lint", "lint:fix": "npm run lint:fix:backend && npm run lint:fix:frontend", "lint:fix:backend": "cd backend && npm run lint:fix", "lint:fix:frontend": "cd frontend && npm run lint:fix", "clean": "npm run clean:backend && npm run clean:frontend", "clean:backend": "cd backend && rm -rf dist node_modules", "clean:frontend": "cd frontend && rm -rf dist node_modules", "test": "npm run test:backend", "test:backend": "cd backend && npm test"}, "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "keywords": ["admin", "management", "video", "streaming", "react", "typescript", "koa", "mysql"], "author": "SmartVideoWeb Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/smartvideo/admin-system.git"}, "bugs": {"url": "https://github.com/smartvideo/admin-system/issues"}, "homepage": "https://github.com/smartvideo/admin-system#readme"}